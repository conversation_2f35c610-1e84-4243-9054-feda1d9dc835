//
//  UPMarket2SDIStockHistogramView.m
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON> on 2020/4/24.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2SDIStockHistogramView.h"
#import <UPMarketUISDK/UPMarketUITransformTool.h>
#import "UPMarket2StockUIUtil.h"

@implementation UPMarket2SDIStockHistogramView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
    }
    return self;
}

- (void)drawRect:(CGRect)rect {
    CGContextRef ctx = UIGraphicsGetCurrentContext();
    
    //绘制柱状图
    [self drawRectViewWithContext:ctx];
}

- (void)drawRectViewWithContext:(CGContextRef)ctx {
    NSArray<NSNumber *> *vols = self.dataArray;
    
    double maxVol = [[vols valueForKeyPath:@"@max.doubleValue"] doubleValue];
    double minVol = [[vols valueForKeyPath:@"@min.doubleValue"] doubleValue];

    if (maxVol == 0 &&  minVol == 0) {
        
        NSString *text = @"暂无资金流向";
        
        CGRect textRect = [UPMarketUIDrawTool rectOfNSString:text fontSize:UPWidth(12)];
        
        [UPMarketUIDrawTool drawTextWithTextString:text fontSize:UPWidth(12) color:UIColor.up_textSecondary1Color x:self.up_width/2 - textRect.size.width/2 y:self.up_height/2 - textRect.size.height/2];
        
    } else {
        NSArray *titles = self.titleArray;
        
        CGFloat padding_top = UPWidth(21);   // 上下间距
        CGFloat padding = UPWidth(15);   // 上下间距
        CGFloat title_height = UPWidth(13); // 文字高度
        CGFloat height = self.up_height - padding_top - padding - title_height;
        
        CGFloat unit = height/(maxVol - MIN(minVol, 0));
        
        //baseLine
        CGFloat baseY = padding_top + maxVol*unit;
        
        if (maxVol > 0 && minVol > 0) { // 只有红色
            unit = height/maxVol;
            baseY = self.up_height - padding_top - title_height;
        } else if (maxVol < 0 && minVol < 0) { // 只有绿色
            unit = height/ABS(minVol);
            baseY = title_height + padding/2;
        }
        
        CGPoint startLinePoint = CGPointMake(0, baseY);
        CGPoint endLinePoint = CGPointMake(self.up_width, baseY);
        
        [UPMarketUIDrawTool drawLineWithStartPoint:startLinePoint endPoint:endLinePoint lineWidth:0.5 lineColor:UIColor.upmarketui_borderColor];
        //other
        
        CGFloat rectPadding = ((self.up_width - 2*padding) - vols.count * self.lineWidth)/(vols.count-1); // 两柱子之间间距
        
        for (int i = 0; i < vols.count; i++) {
            //图形
            
            // 第一个最后一个和中间的可用width不同,不能直接用self.up_width / vols.count
            CGFloat maxWidth = self.lineWidth + rectPadding;
            if(i == 0 || i == vols.count - 1){
                maxWidth = padding + self.lineWidth + rectPadding/2;
            }
            
            // 第一个和最后一个可用文字绘制宽度=padding+rectPadding/2 + linewidth
            // 中间的可宽度=rectPadding + lindWidth
            // 第一个和最后一个的中心点计算不一样，所以中心点计算方式不能单纯依靠startLinePoint.x
            CGFloat centX = startLinePoint.x + rectPadding/2 +self.lineWidth/2 + maxWidth/2;
            if (i == 0) {
                centX = maxWidth/2;
            }
            startLinePoint =  CGPointMake(padding + self.lineWidth/2 + (self.lineWidth + rectPadding)*i, baseY);
            endLinePoint = CGPointMake(padding + self.lineWidth/2 + (self.lineWidth + rectPadding)*i, baseY-(vols[i].doubleValue * unit));
            UIColor *lineColor = [UPMarket2StockUIUtil getRiseFallTextColor:vols[i].doubleValue baseValue:0];
            
            [UPMarketUIDrawTool drawLineWithStartPoint:endLinePoint endPoint:startLinePoint lineWidth: self.lineWidth lineColor:lineColor];
            
            
            //文字
            CGFloat unit = 10000.0;  // 万
            NSInteger precise = 0;
            NSString *text = [UPMarketUICalculateUtil transNumberByRoundingOff:vols[i].doubleValue/unit precise:precise needsymbol:NO];

            CGFloat textFont = [UPMarketUITransformTool fontSizeAutoFitByWidth:maxWidth string:text maxFontSize:UPWidth(11) minFontSize:UPWidth(8)];

            if (text.length == 6 && self.up_width < 375){
                NSString  *pattern = @"\\.[0-9]+";

                NSError *error = NULL;

                NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:pattern options:NSRegularExpressionCaseInsensitive error:&error];

                NSTextCheckingResult *result = [regex firstMatchInString:text options:0 range:NSMakeRange(0,[text length])];

                NSRange range = NSMakeRange(result.range.location+2, 1);

                if(result){
                    NSMutableString *textM = text.mutableCopy;
                    [textM deleteCharactersInRange:range];
                    text = textM.copy;
                }
            } else if (text.length > 6) {
                NSString *pattern= @"\\.[0-9]+";
                
                NSError *error = NULL;
                
                NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:pattern options:NSRegularExpressionCaseInsensitive error:&error];
                
                NSTextCheckingResult *result = [regex firstMatchInString:text options:0 range:NSMakeRange(0,[text length])];
                
                if(result){
                    NSMutableString *textM = text.mutableCopy;
                    [textM deleteCharactersInRange:result.range];
                    text = textM.copy;
                }
            }
            
            CGRect textRect = [UPMarketUIDrawTool rectOfNSString:text fontSize:textFont];
            
            CGFloat y = vols[i].doubleValue < 0 ? baseY - textRect.size.height - UPWidth(2) : baseY + UPWidth(2);
            
            [UPMarketUIDrawTool drawDigitalWithTextString:text fontSize:textFont color:lineColor x:centX - textRect.size.width/2  y:y];
            
            //标题
            NSString *title = titles[i];
            
            CGFloat titleFont = UPWidth(12);
            
            CGRect titleRect = [UPMarketUIDrawTool rectOfNSString:title fontSize:titleFont];
                        
            [UPMarketUIDrawTool drawTextWithTextString:title
                                      fontSize:titleFont
                                         color:UIColor.up_textSecondaryColor
                                             x:endLinePoint.x - titleRect.size.width/2
                                             y:self.up_height - titleRect.size.height];
        }
        
        
    }
}

- (void)draw {
    [self setNeedsDisplay];
}

#pragma mark - SETTER & GETTER
- (CGFloat)lineWidth {
    if (_lineWidth == 0) {
        return UPWidth(18);
    } else {
        return _lineWidth;
    }
}

@end
