//
//  GeneralPlugin.m
//  FQYFNative
//
//  Created by macPro on 2019/10/29.
//  Copyright © 2019 macPro. All rights reserved.
//

#import "GeneralPlugin.h"
#import "FMPDFReaderViewController.h"
#import "NSNULLTool.h"
#import "FileManagerTool.h"
//#import "FQYFVideoPlayViewController.h"
#import <Photos/Photos.h>
#import "JsonTool.h"
#import "CookieManagerTool.h"
#import "YTGNormalWebVC.h"
#import "FMPayTool.h"
#import "NetworkManager.h"
#import "NSURLProtocol+WebKitSupport.h"
#import "FMUserDefault.h"
#import "HTMLParseManager.h"
#import "FMAllTopicViewController.h"
#import "HttpRequestTool+UserCenter.h"
#import "FMCouponTableModel.h"
#import "TZImagePickerController.h"

@interface GeneralPlugin ()<TZImagePickerControllerDelegate>
@property (nonatomic, strong) CDVInvokedUrlCommand *uploadNoteImageCommand;
@property (nonatomic, assign) BOOL isDeviceReady;
@property (nonatomic, copy) void (^deviceReadyCallback)(void);

@end

@implementation GeneralPlugin{
    NSString *VideoUrl;
}

- (void)pluginInitialize {
    [super pluginInitialize];
    self.isDeviceReady = NO;
    
    // 监听 deviceready 事件
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                           selector:@selector(onDeviceReady) 
                                               name:CDVPageDidLoadNotification 
                                             object:nil];
}

- (void)onDeviceReady {
    self.isDeviceReady = YES;
    if (self.deviceReadyCallback) {
        self.deviceReadyCallback();
    }
}

- (void)showToast:(CDVInvokedUrlCommand *)command {
    NSArray *arguments = command.arguments;
    if (arguments.count) {
        [[HTMLParseManager shareInstance] parseWithHTMLString:arguments[0] completeWithAttrStr:^(NSMutableAttributedString * _Nonnull attrStr, NSArray *imgArr) {
            [SVProgressHUD showImage:nil attrStatus:attrStr];
        }];
    }
}

- (void)showLoading:(CDVInvokedUrlCommand *)command {
    [SVProgressHUD show];
}

- (void)dismissLoading:(CDVInvokedUrlCommand *)command {
    [SVProgressHUD dismiss];
}

- (void)showDialog:(CDVInvokedUrlCommand *)command {
    NSArray *arguments = [NSNULLTool changeNullToHollowStrWithObject:command.arguments];
    if ([self isArgumentsNumRightWithArguments:command rightNum:4]) {
        if ([arguments.lastObject length]) {
            //        ShowConfirm([FQYFHelper getCurrentVC], arguments[0], arguments[1], <#NSString *buttonTitle1#>, <#NSString *buttonTitle2#>, <#^(void)buttonTitle1Handler#>, <#^(void)buttonTitle2Handler#>)
        }
    }
}

- (void)showNewUserRewardDialog:(CDVInvokedUrlCommand *)command {
    NSArray *arguments = command.arguments;
    if (arguments.count) {
        [PushMessageView showWithTitle:@"任务完成" message:@"任务完成！新手任务奖励已发放"  noticeImage:nil sureTitle:@"更多任务" cancelTitle:@"确定" clickSure:^{
            for (UIViewController *vc in [FMHelper getCurrentVC].navigationController.viewControllers) {
                if ([vc isKindOfClass:[YTGNormalWebVC class]]) {
                    if ([((YTGNormalWebVC *)vc).startPage containsString:kAPI_UserCenter_FLZX]) {
                        [[FMHelper getCurrentVC].navigationController popToViewController:vc animated:YES];
                        return;
                    }
                }
            }
            [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                [[FMHelper getCurrentVC].navigationController popViewControllerAnimated:NO];
                
                [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://web?url=%@%@&title=%@", prefix.URLEncodedString, kAPI_UserCenter_FLZX.URLEncodedString, @"福利中心".URLEncodedString]];
            }];
        } clickCancel:^{
            [[FMHelper getCurrentVC].navigationController popViewControllerAnimated:YES];
        }];
    }
}

- (void)dismissDialog:(CDVInvokedUrlCommand *)command {
}

// 获取缓存Token
- (void)getTempToken:(CDVInvokedUrlCommand *)command {
    long currentTime = [FMUserDefault getDeviceRestartToCurrentTime];
    long tokenUpdateTime = [[FMUserDefault getUnArchiverDataForKey:@"TokenUpdateTime"] longValue];
    NSString *APIToken = [FMUserDefault getAPIToken];
    if (APIToken.length && currentTime - tokenUpdateTime < 24 * 60 * 60) {
        NSMutableDictionary *parma = [NSMutableDictionary dictionary];
        [parma setObject:APIToken forKey:@"token"];
        [parma setObject:APP_VERSION forKey:@"version"];
        
        [self.commandDelegate runInBackground:^{
            CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_OK messageAsString:[JsonTool jsonStringFromDicOrArr:parma]];
            [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
        }];
    } else {
        [HttpRequestTool userLoginAuthByToken:^{
            [FMUserDefault setArchiverData:[NSNumber numberWithLong:[FMUserDefault getDeviceRestartToCurrentTime]] forKey:@"TokenUpdateTime"];

            NSString *APIToken = [FMUserDefault getAPIToken];
            NSMutableDictionary *parma = [NSMutableDictionary dictionary];
            [parma setObject:APIToken forKey:@"token"];
            [parma setObject:APP_VERSION forKey:@"version"];
            
            [self.commandDelegate runInBackground:^{
                CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_OK messageAsString:[JsonTool jsonStringFromDicOrArr:parma]];
                [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
            }];
        } authTokenFailureBlock:^{
            [self.commandDelegate runInBackground:^{
                CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_OK messageAsString:@""];
                [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
            }];
        }];
    }
}

// 直接获取接口Token
- (void)getTempToken2:(CDVInvokedUrlCommand *)command {
    [HttpRequestTool userLoginAuthByToken:^{
        [FMUserDefault setArchiverData:[NSNumber numberWithLong:[FMUserDefault getDeviceRestartToCurrentTime]] forKey:@"TokenUpdateTime"];

        NSString *APIToken = [FMUserDefault getAPIToken];
        NSMutableDictionary *parma = [NSMutableDictionary dictionary];
        [parma setObject:APIToken forKey:@"token"];
        [parma setObject:APP_VERSION forKey:@"version"];
        
        [self.commandDelegate runInBackground:^{
            CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_OK messageAsString:[JsonTool jsonStringFromDicOrArr:parma]];
            [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
        }];
    } authTokenFailureBlock:^{
        [self.commandDelegate runInBackground:^{
            CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_OK messageAsString:@""];
            [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
        }];
    }];
}

// 获取设备信息
- (void)getDeviceInfo:(CDVInvokedUrlCommand *)command {
    NSString *UUID = [MyUUIDManager getUUID];
    NSString *bigFont = [FMHelper isBigFont] ? @"1" : @"0";
    NSDictionary *dic = @{@"platform":@"iOS", @"versionName":APP_VERSION, @"UUID":UUID, @"enableBigFont":bigFont};
    [self.commandDelegate runInBackground:^{
        CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_OK messageAsString:[JsonTool jsonStringFromDicOrArr:dic]];
        [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
    }];
}

// 获取用户信息
- (void)getUserInfo:(CDVInvokedUrlCommand *)command {
    // 如果设备未就绪,等待就绪后再执行
    if (!self.isDeviceReady) {
        __weak typeof(self) weakSelf = self;
        self.deviceReadyCallback = ^{
            [weakSelf handleGetUserInfo:command];
        };
        return;
    }
    
    [self handleGetUserInfo:command];
}

// 将原来的 getUserInfo 逻辑抽取出来
- (void)handleGetUserInfo:(CDVInvokedUrlCommand *)command {
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    if ([FMUserDefault getUserId].length) {
        [self.commandDelegate runInBackground:^{
            CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_OK 
                                                      messageAsString:userModel.userInfoJsonString];
            [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
        }];
    } else {
        [self.commandDelegate runInBackground:^{
            CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_OK 
                                                      messageAsString:@"用户未登录"];
            [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
        }];
    }
}

// 路由
- (void)router:(CDVInvokedUrlCommand *)command {
    if ([self isArgumentsNumRightWithArguments:command rightNum:1]) {
        NSString *urlStr = command.arguments[0];
        [ProtocolJump jumpWithUrl:urlStr];
    }
}

// 阅读pdf
- (void)readPdf:(CDVInvokedUrlCommand *)command {
    NSArray *arguments = [NSNULLTool changeNullToHollowStrWithObject:command.arguments];
    if ([self isArgumentsNumRightWithArguments:command rightNum:1]) {
        FMPDFReaderViewController *vc = [[FMPDFReaderViewController alloc] init];
        if (arguments.count == 2) {
            vc.title = [arguments[1] length] ? arguments[1] : @"阅读附件";
        }
        vc.pdfUrl = arguments[0];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }
}

- (void)readExcel:(CDVInvokedUrlCommand *)command {
    NSArray *arguments = [NSNULLTool changeNullToHollowStrWithObject:command.arguments];
    if ([self isArgumentsNumRightWithArguments:command rightNum:1]) {
    }
}

- (void)logout:(CDVInvokedUrlCommand *)command {
    [HttpRequestTool sendUserLogOutWithStart:^{
    } failure:^{
        [self.commandDelegate runInBackground:^{
            CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_ERROR messageAsString:@"退出失败"];
            [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
        }];
    } success:^(NSDictionary *dic) {
        [CookieManagerTool clearCookies];
        
        // 清空本地用户信息
        [FMUserDefault clearLocalUserData];
        // 清除通知，要点在于要先把BadgeNumber 设成跟当前不同的值，然后再设成0
        [UIApplication sharedApplication].applicationIconBadgeNumber = 1;
        [UIApplication sharedApplication].applicationIconBadgeNumber = 0;
        
        [[NSNotificationCenter defaultCenter] postNotificationName:kAccoutLoginOut object:nil];
    }];
}

// 验证登录状态
- (void)checkAccount:(CDVInvokedUrlCommand *)command {

}

// 下载资源
- (void)download:(CDVInvokedUrlCommand *)command {
    if ([self isArgumentsNumRightWithArguments:command rightNum:2]) {
        NSArray *arguments = command.arguments;
        NSString *type = arguments[1];
        
        if ([type isEqualToString:@"image"]) {
            [self saveImageWithUrlStr:arguments[0]];
        } else if ([type isEqualToString:@"video"]) {
            [self saveVideoWithUrlStr:arguments[0]];
        } else if ([type isEqualToString:@"audio"]) {
            [SVProgressHUD showErrorWithStatus:@"只能保存图片及视频到系统相册中"];
        }
    }
}

- (void)titleMenu:(CDVInvokedUrlCommand *)command {
    if ([self isArgumentsNumRightWithArguments:command rightNum:1]) {
        NSDictionary *dic = command.arguments.firstObject;
        YTGNormalWebVC *webVC = (YTGNormalWebVC *)self.viewController;
        [webVC configRightButtonWithRouter:^{
            if (dic[@"router"] && [dic[@"router"] isKindOfClass:[NSString class]] && [dic[@"router"] length]) {
                [ProtocolJump jumpWithUrl:dic[@"router"]];
            }
            [self.commandDelegate runInBackground:^{
                CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_OK messageAsString:dic[@"router"]];
                [result setKeepCallback:@(YES)];
                [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
            }];
        } type:[dic[@"type"] integerValue] content:dic[@"content"]];
    } else {
        YTGNormalWebVC *webVC = (YTGNormalWebVC *)self.viewController;
        [webVC hiddenRightButton];
    }
}

- (void)hideTitleMenu:(CDVInvokedUrlCommand *)command {
    YTGNormalWebVC *webVC = (YTGNormalWebVC *)self.viewController;
    [webVC hiddenRightButton];
}

- (void)navCloseBtnShow:(CDVInvokedUrlCommand *)command {
    if ([self isArgumentsNumRightWithArguments:command rightNum:1]) {
        NSArray *arguments = command.arguments;
        NSString *show = arguments[0];
        YTGNormalWebVC *webVC = (YTGNormalWebVC *)self.viewController;
        [webVC navCloseBtnShow:[show boolValue]];
    } else {
        YTGNormalWebVC *webVC = (YTGNormalWebVC *)self.viewController;
        [webVC navCloseBtnShow:NO];
    }
}

// 右上角图标事件
- (void)rightIcon:(CDVInvokedUrlCommand *)command {
    if ([self isArgumentsNumRightWithArguments:command rightNum:1]) {
        YTGNormalWebVC *webVC = (YTGNormalWebVC *)self.viewController;
        NSString *routerString = command.arguments[0]; // 获取路由字符串
        
        // 创建一个 Block，捕获 routerString
        void(^routerBlock)(void) = ^{
            if (routerString && [routerString isKindOfClass:[NSString class]] && routerString.length > 0) {
                [ProtocolJump jumpWithUrl:routerString]; // 在 Block 内部执行跳转
            }
        };
        
        // 将创建的 Block 传递给 webVC
        [webVC configRightButtonWithRouter:routerBlock type:0 content:nil];
    }
}

// 隐藏右上角图标
- (void)hideRightIcon:(CDVInvokedUrlCommand *)command {
    YTGNormalWebVC *webVC = (YTGNormalWebVC *)self.viewController;
    [webVC hiddenRightButton];
}

// 修改title
- (void)changeTitle:(CDVInvokedUrlCommand *)command {
    if ([self isArgumentsNumRightWithArguments:command rightNum:1]) {
        YTGNormalWebVC *webVC = (YTGNormalWebVC *)self.viewController;
        NSString *title = [command.arguments[0] stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]].length ? command.arguments[0] : @"";
        if (title.length) {
            webVC.title = title;
            // 处理iOS16上导航栏标题可能不显示的bug
            [webVC.navigationController.navigationBar setNeedsLayout];
        }
    }
}

// 关闭
- (void)close:(CDVInvokedUrlCommand *)command {
    YTGNormalWebVC *webVC = (YTGNormalWebVC *)self.viewController;
    NSMutableArray *arr = [NSMutableArray arrayWithArray:webVC.navigationController.viewControllers];
    for (UIViewController *vc in arr) {
        if (vc == webVC) {
            [arr removeObject:vc];
            break;
        }
    }
    webVC.navigationController.viewControllers = arr;
}

// 视频
- (void)playVideo:(CDVInvokedUrlCommand *)command {
    if ([self isArgumentsNumRightWithArguments:command rightNum:2]) {
//        FQYFVideoPlayViewController *vc = [[FQYFVideoPlayViewController alloc] init];
//        LZPlayerModel *model = [[LZPlayerModel alloc] init];
//        model.videoURL = [NSURL URLWithString:command.arguments[0]];
//        vc.playerModel = model;
////        playView.playUrl = command.arguments[0];
////        NSString *base64Img = [command.arguments[1] substringFromIndex:[command.arguments[1] rangeOfString:@"data:image/png;base64,"].length];
////        NSData *decodedImageData = [[NSData alloc] initWithBase64EncodedString:base64Img options:NSDataBase64DecodingIgnoreUnknownCharacters];
////        playView.placeholderImg = [UIImage imageWithData:decodedImageData];
//        [[FQYFHelper getCurrentVC].navigationController pushViewController:vc animated:NO];
    }
}

// 日期选择
- (void)dateSelector:(CDVInvokedUrlCommand *)command {

}

// 充值
- (void)rechargeDialog:(CDVInvokedUrlCommand *)command {
    if ([FMHelper getIAPPayStatus]) {
        [[FMPayTool payTool] goIAPPayViewControllerWithIsHaveBackBlock:NO currentVC:[FMHelper getCurrentVC]];
        return;
    }
    
    NSArray *arguments = [NSNULLTool changeNullToHollowStrWithObject:command.arguments];
    if ([self isArgumentsNumRightWithArguments:command rightNum:4]) {
        id activity = arguments[0];
        NSString *payPrice = arguments[1];
        NSString *payInfo = arguments[2];
        NSString *type = arguments[3];
        [[FMPayTool payTool] rechargeDialogWithProductId:activity payPrice:payPrice payInfo:payInfo type:type orderBlock:^(NSString *orderNum, NSString *errorMsg) {
            if (orderNum.length) {
                [self.commandDelegate runInBackground:^{
                    CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_OK messageAsString:orderNum];
                    [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
                }];
            } else {
                [self.commandDelegate runInBackground:^{
                    CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_ERROR messageAsString:errorMsg];
                    [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
                }];
            }
        }];
    }
}

// httpRequest
- (void)httpRequest:(CDVInvokedUrlCommand *)command {
    NSArray *arguments = [NSNULLTool changeNullToHollowStrWithObject:command.arguments];
    if ([self isArgumentsNumRightWithArguments:command rightNum:5]) {
        NSString *url = arguments[0];
        NSDictionary *header = arguments[2];
        NSDictionary *query = arguments[3];
        NSDictionary *body = arguments[4];
        NSString *requestType = arguments[1];
        HttpRequestType type = HttpRequestTypeGet;
        NSDictionary *params;
        if ([[requestType lowercaseString] isEqualToString:@"get"]) {
            params = query;
        } else if ([[requestType lowercaseString] isEqualToString:@"post"]) {
            type = HttpRequestTypePost;
            params = body;
        } else if ([[requestType lowercaseString] isEqualToString:@"put"]) {
            type = HttpRequestTypePut;
            params = body;
        } else if ([[requestType lowercaseString] isEqualToString:@"delete"]) {
            type = HttpRequestTypeDelete;
            params = body;
        }
        [NetworkManager requestWithType:type withUrlString:url header:header withParaments:params withSuccessBlock:^(NSDictionary *dic) {
            [self.commandDelegate runInBackground:^{
                CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_OK messageAsString:[JsonTool jsonStringFromDicOrArr:dic]];
                [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
            }];
        } withFailureBlock:^(NSError *error) {
            [self.commandDelegate runInBackground:^{
                CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_ERROR messageAsString:[JsonTool jsonStringFromDicOrArr:error.userInfo]];
                [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
            }];
        } progress:^(float progress) {
            
        }];
    }
}

// 取消注册协议
- (void)un_registerScheme:(CDVInvokedUrlCommand *)command {
    [NSURLProtocol wk_unregisterScheme:@"https"];
}

// 直播间交互
- (void)liveInteraction:(CDVInvokedUrlCommand *)command {
    if ([self isArgumentsNumRightWithArguments:command rightNum:2]) {
        NSMutableDictionary *param = [NSMutableDictionary dictionary];
        [param setObject:command.arguments[0] forKey:@"name"];
        [param setObject:command.callbackId forKey:@"callbackId"];
        if (command.arguments[1]) {
            [param setObject:command.arguments[1] forKey:@"body"];
        } else {
            [param setObject:@{} forKey:@"body"];
        }
        [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationliveInteraction object:param];
    }
}

// 选择话题
- (void)selectTopic:(CDVInvokedUrlCommand *)command {
    FMAllTopicViewController *vc = [[FMAllTopicViewController alloc] init];
    vc.type = 2;
    vc.addTopicBlock = ^BOOL(FMAddTopicCellModel * _Nonnull model) {
        [self.commandDelegate runInBackground:^{
            CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_OK messageAsString:[model modelToJSONString]];
            [self.commandDelegate sendPluginResult:result callbackId:command.callbackId];
        }];
        return YES;
    };
    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
}

//页面埋点
- (void)manPagePoint:(CDVInvokedUrlCommand *)command {
    NSArray *arguments = [NSNULLTool changeNullToHollowStrWithObject:command.arguments];
    if ([self isArgumentsNumRightWithArguments:command rightNum:2]) {
    }
}

// web加载失败后点击
- (void)webLoadFailClicked:(CDVInvokedUrlCommand *)command {
    YTGNormalWebVC *webVC = (YTGNormalWebVC *)self.viewController;
    [webVC reloadRequest];
}

// 投顾服务签约完成
- (void)bignameServiceSignComplete:(CDVInvokedUrlCommand *)command {
    BOOL canJumpVC = NO;
    for (UIViewController *vc in [FMHelper getCurrentVC].navigationController.childViewControllers) {
        if ([vc isMemberOfClass:NSClassFromString(@"FMBigCastHomePageViewController")]) {
            [[FMHelper getCurrentVC].navigationController popToViewController:vc animated:YES];
            canJumpVC = YES;
            break;
        } else if ([vc isMemberOfClass:NSClassFromString(@"FMMemberCenterProductViewController")]) {
            [[FMHelper getCurrentVC].navigationController popToViewController:vc animated:YES];
            canJumpVC = YES;
            break;
        } else if ([vc isMemberOfClass:NSClassFromString(@"FMCouponViewController")]) {
            [[FMHelper getCurrentVC].navigationController popToViewController:vc animated:YES];
            canJumpVC = YES;
            break;
        }
    }
    if (!canJumpVC) {
        [[FMHelper getCurrentVC].navigationController popViewControllerAnimated:YES];
    }
}

// 更新本地金币余额
- (void)updateBalance:(CDVInvokedUrlCommand *)command {
    if ([self isArgumentsNumRightWithArguments:command rightNum:1]) {
        [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];
    }
}

- (void)sendcoupon:(CDVInvokedUrlCommand *)command {
    [[FMPayTool payTool] checkOutRegistrFlagAndAnswerFlagResult:^{
        [HttpRequestTool getBeginnerUserCouponStart:^{
        } failure:^{
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                FMCouponTableModel *model = [FMCouponTableModel modelWithDictionary:dic[@"data"]];
                NSDictionary *couponDic = [FMUserDefault getUnArchiverDataForKey:@"BeginnerUserCoupon"];
                NSMutableDictionary *mutabDic;;
                if (!couponDic) {
                    mutabDic = [NSMutableDictionary dictionary];
                } else {
                    mutabDic = [NSMutableDictionary dictionaryWithDictionary:couponDic];
                }
                [mutabDic setObject:model forKey:[FMUserDefault getUserId]];
                [FMUserDefault setArchiverData:mutabDic forKey:@"BeginnerUserCoupon"];
                if ([dic[@"errmessage"] isEqualToString:@"已领取过"]) {
                    NSDictionary *couponShowDic = [FMUserDefault getUnArchiverDataForKey:@"SendCouponShow"];
                    NSMutableDictionary *mutabDic = [NSMutableDictionary dictionaryWithDictionary:couponShowDic];
                    [mutabDic setObject:[NSNumber numberWithBool:YES] forKey:[FMUserDefault getUserId]];
                    [FMUserDefault setArchiverData:mutabDic forKey:@"SendCouponShow"];
                }
                [ProtocolJump jumpWithUrl:@"qcyzt://note"];
            }
        }];
    }];
}

// 发布笔记获取图片 原生拉起相册 返回图片url
- (void)uploadNoteImage:(CDVInvokedUrlCommand *)command {
    if ([self isArgumentsNumRightWithArguments:command rightNum:1]) {
        NSInteger iamgeCount = [command.arguments[0] integerValue];
        TZImagePickerController *imagePicker = [[TZImagePickerController alloc] init];
        imagePicker.maxImagesCount = iamgeCount;
        imagePicker.allowPickingOriginalPhoto = NO;
        imagePicker.allowTakeVideo = NO;
        imagePicker.allowTakePicture = YES;
        imagePicker.allowPickingImage = YES;
        imagePicker.pickerDelegate = self;
        imagePicker.modalPresentationStyle = UIModalPresentationFullScreen;
        imagePicker.statusBarStyle = UIStatusBarStyleDefault;
        imagePicker.barItemTextColor = FMWhiteColor;
        [[FMHelper getCurrentVC] presentViewController:imagePicker animated:YES completion:nil];
        self.uploadNoteImageCommand = command;
    }
}

// 禁止截屏
- (void)uiSecure:(CDVInvokedUrlCommand *)command {
    YTGNormalWebVC *webVC = (YTGNormalWebVC *)self.viewController;
    webVC.banScreenshot = YES;
}

#pragma mark-TZImagePickerControllerDelegate
- (void)imagePickerController:(TZImagePickerController *)picker didFinishPickingPhotos:(NSArray<UIImage *> *)photos sourceAssets:(NSArray *)assets isSelectOriginalPhoto:(BOOL)isSelectOriginalPhoto {
    [NetworkManager uploadImageWithOperations:nil withImageArray:photos withUrlString:[NSString stringWithFormat:@"%@?waterMark=1&action=upload",kAPI_System_Uplaod] withSuccessBlock:^(NSDictionary *object) {
        [SVProgressHUD dismiss];
        NSMutableArray *imageUrlArray = [NSMutableArray array];
        if ([object[@"data"] isKindOfClass:[NSArray class]]) {
            NSArray *arr = [NSArray arrayWithArray:object[@"data"]];
            for (NSInteger i = 0; i < arr.count; i ++) {
                NSDictionary *dic = arr[i];
                [imageUrlArray addObject:dic[@"url"]];
            }
        }
        [self.commandDelegate runInBackground:^{
            CDVPluginResult *result = [CDVPluginResult resultWithStatus:CDVCommandStatus_OK messageAsString:[imageUrlArray componentsJoinedByString:@","]];
            [self.commandDelegate sendPluginResult:result callbackId:self.uploadNoteImageCommand.callbackId];
        }];
    } withFailurBlock:^(NSError *error) {
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } withUpLoadProgress:^(float progress) {
        [SVProgressHUD showProgress:progress status:@"图片上传中..."];
    }];
}


#pragma mark - Private
- (void)saveImageWithUrlStr:(NSString *)urlStr {
    NSURL *url = [NSURL URLWithString:urlStr];
    if (url) {
        [[SDWebImageDownloader sharedDownloader] downloadImageWithURL:url options:SDWebImageDownloaderLowPriority progress:^(NSInteger receivedSize, NSInteger expectedSize, NSURL * _Nullable targetURL) {
            
        } completed:^(UIImage * _Nullable image, NSData * _Nullable data, NSError * _Nullable error, BOOL finished) {
            if (error) {
                [SVProgressHUD showImage:nil status:@"保存失败"];
                return ;
            }
            
            UIImageWriteToSavedPhotosAlbum(image, self, @selector(saveResource:didFinishSavingWithError:contextInfo:), nil);
        }];
    }
}

- (void)saveVideoWithUrlStr:(NSString *)urlStr {
    NSURL *url = [NSURL URLWithString:urlStr];
    if (url) {
        NSArray *arr  = [urlStr componentsSeparatedByString:@"/"];
        NSString *path = [FileManagerTool getPathAtCachesWithDirectoryName:@"Resource" fileName:arr.lastObject];
        if (path.length) { // 已经下载过
            if (UIVideoAtPathIsCompatibleWithSavedPhotosAlbum(path)) { // 能否保存到相册
                UISaveVideoAtPathToSavedPhotosAlbum(path, self, @selector(saveResource:didFinishSavingWithError:contextInfo:), NULL);
            } else {
                [SVProgressHUD showImage:nil status:@"保存失败"];
            }
        } else {
            [HttpRequestTool downLoadFileRequestWithOperations:nil SavePath:[FileManagerTool createDirAtCachesWithDirectoryName:@"Resource"] UrlString:urlStr success:^(NSDictionary *dic) {
                NSString *savePath = [FileManagerTool getPathAtCachesWithDirectoryName:@"Resource" fileName:arr.lastObject];
                if (UIVideoAtPathIsCompatibleWithSavedPhotosAlbum(savePath)) { // 能否保存到相册
                    UISaveVideoAtPathToSavedPhotosAlbum(savePath, self, @selector(saveResource:didFinishSavingWithError:contextInfo:), NULL);
                } else {
                    [SVProgressHUD showImage:nil status:@"保存失败"];
                }
            } failure:^(NSError *error) {
                [SVProgressHUD showImage:nil status:@"保存失败"];
            } progress:^(float progress) {
            }];
        }
    }
}

- (void)saveResource:(UIImage *)image didFinishSavingWithError:(NSError *)error contextInfo:(void *)contextInfo {
    if (error) {
        //检测相册权限是否已经开启
        PHAuthorizationStatus status = [PHPhotoLibrary authorizationStatus];//相册访问权限
        if (status == PHAuthorizationStatusNotDetermined) {
            //相册访问权限未决定
            [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
                //请求权限, 这句代码过后权限就不会再是未决定,这句代码只会执行一次
            }];
        }
        if (status == PHAuthorizationStatusRestricted || status == PHAuthorizationStatusDenied) {//权限被拒绝或不支持相册
            UIAlertController *alert = [UIAlertController alertControllerWithTitle:nil message:@"保存失败,请前往设置查看APP是否开启相册权限" preferredStyle:UIAlertControllerStyleAlert];
            UIAlertAction *ok = [UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString] options:@{} completionHandler:nil];
                [alert dismissViewControllerAnimated:YES completion:nil];
            }];
            [alert addAction:ok];
            [[FMHelper getCurrentVC] presentViewController:alert animated:YES completion:nil];
        }else if (status == PHAuthorizationStatusAuthorized){
            [SVProgressHUD showImage:nil status:@"保存失败"];
        }
    } else {
        [SVProgressHUD showImage:nil status:@"保存成功"];
    }
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
