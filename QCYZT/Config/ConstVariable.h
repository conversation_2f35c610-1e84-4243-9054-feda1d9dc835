//
//  ConstVariable.h
//  FQYFNative
//
//  Created by macPro on 2018/8/9.
//  Copyright © 2018年 macPro. All rights reserved.
//  

#import <Foundation/Foundation.h>

UIKIT_EXTERN NSString * const lz_jsonAnalysisErrorDescribe;

#pragma mark -- 定义常用的常量
/// banner 的尺寸比例(高/宽度),
#define kBannerSizeRatio (109.0 / 345.0)
#define kGlobalLoopNGDelayTime 5                                                          // 默认的行情4g下刷新时间
#define kGlobalLoopWIFIDelayTime 3                                                        // 默认的行情wifi下刷新时间
#define kFMKLineChartViewHeight 300*UI_SCREEN_HEIGHT/667                                  // K线图的View高度
#define kVerifyCodeCountTime  120     // 验证码倒计时


#define kHomeButtonCollectionCellHeight floor(UI_Relative_WidthValue(105))                                 // 首页按钮高度

/// 搜索历史缓存key
#define SearchHistoryDataKey @"SearchHistoryCache"
/// 搜索热门缓存key
#define SearchHotDataKey @"SearchHotCache"
/// 图片导入股票提示框 开启/关闭缓存字段
#define NotShowImportStockTipView @"NotShowImportStockTipView"
/// 自选股分组管理提示框
#define NotShowSelfStockGroupManagerTipView @"NotShowSelfStockGroupManagerTipView"
/// 系列课程订阅提醒 view 开启或关闭 缓存字段
#define CourseRemindTipView @"CourseRemindTipView"
/// 笔记快捷回复提示
#define NoteQuickCommentTipView @"NoteQuickCommentTipView"



typedef NS_ENUM(NSInteger, NoteListType) {       // 自定义笔记类型
    NoteListTypeMyFocused = 1,                   // 我关注的
    NoteListTypeAll,                             // 所有
    NoteListTypeMyRead,                          // 我读过的
    NoteListTypeDakaWrote,                       // 投顾写的
    NoteListTypeMyWrote,                         // 我写的
    NoteListTypeMyCollected = 7,                 // 我收藏的
    NoteListTypeMyPurchased = 8                  // 我购买的
};

typedef NS_ENUM(NSInteger, CourseType) {         // 课程类型
    SeriesCourseType = 1,                        // 系列课程
    ExcellentCourseType = 2,                     // 优质课程
    ChaptersCourseType = 3                       // 系列章节课程
};


typedef NS_ENUM(NSInteger, AskCodeListType) {    // 自定义问股类型
    AskCodeListTypeMyFocused = 1,                // 我关注的投顾的
    AskCodeListTypeAll,                          // 所有
    AskCodeListTypeMyListened,                   // 我听过的
    AskCodeListTypeMyAsked,                      // 我问的
    AskCodeListTypeDakaAnswered,                 // 投顾回答的
    AskCodeListTypeMyAnswered,                   // 我回答的，问我的
    AskCodeListTypeMyCollected,                  // 我收藏的
    AskCodeListTypeSelfSelected                  // 自选
};

typedef NS_ENUM(NSUInteger, MessageType) {  // 消息类型
    MessageTypeTodayAspect = 1, // 今日看点
    MessageTypeBigCast,  // 投顾动态
    MessageTypeSmartGaze,  // 智能盯盘
    MessageTypeSelfStock,  // 自选股
    MessageTypeQuestion,  // 问股（投顾收到的是用户提问，用户收到的是投顾回复）
    MessageTypeCommentReply,  // 评论回复，弃用
    MessageTypeServiceRemind,  // 服务更新提醒
    MessageTypeSystemMsg,  // 系统通知
    MessageTypeCommentMe,  // 评论我的
    MessageTypePrivateLetter // 私信
};

typedef NS_ENUM(NSUInteger, MsgReadType) {
    MsgReadTypeAll  = 1,  // 全部已读
    MsgReadTypeOne  // 一条已读
};

typedef NS_ENUM(NSInteger, ShareType) {
    ShareTypeWechat,
    ShareTypeFriends,
};

typedef NS_ENUM(NSUInteger, ShareViewType) {
    NormalShareViewType = 0,    // 默认分享页面
    NormalShareViewTypeLive = 1,
//    LiveSmallPlayViewShareViewType   = 1,    // 直播分享页面(带小窗口播放)
//    LiveNoSmallPlayViewShareViewType = 2,    // 直播分享页面 (不带小窗口)
    InfoMationShareViewType = 3              // 资讯详情分享
};

// 这个字段主要用于[FMHelper showVIPAlertWithType:]方法，给训练营和问股做逻辑判断的，现在训练营都是免费、问股都是直接付费，没有VIP专属，所以这个字段基本没用了，也就没有新增决策擒龙和决策点金了
typedef NS_OPTIONS(NSUInteger, VIPReadAuthority) {
    VIPReadAuthorityDXQN    = 1 << 0, // 短线擒牛可看
    VIPReadAuthorityGSYJ    = 1 << 1,  // 股市赢家可看
    VIPReadAuthorityALL    = (VIPReadAuthorityDXQN | VIPReadAuthorityGSYJ)  // 都可看
};

typedef NS_ENUM(NSUInteger, CellLocation) {
    CellLocationTop,
    CellLocationMiddle,
    CellLocationBottom,
    CellLocationOnlyOne
};

//引导页枚举
typedef NS_ENUM(NSUInteger, GuideType) {
    GuideTypeHomePageView = 1,    //首页    (已废弃)
    GuideTypeLiveDetailView  = 2, //直播详情页 (已弃用)
    GuideTypeAskCodeView  = 3,  //问股页面   (已弃用)
    GuideTypeNoteDetailView = 4, //笔记详情  (已弃用)
    GuideTypeCommunitChoiceView = 5, // 社区精选页面
    GuideTypeStockView = 6 // 股票详情
};

//卡券使用引导页枚举
typedef NS_ENUM(NSUInteger, CouponGuideType) {
    CouponGuideTypeStep1,
    CouponGuideTypeStep2,
    CouponGuideTypeStep3,
    CouponGuideTypeStep4
};

//笔记,直播限时限量活动列表UI枚举
typedef NS_ENUM(NSUInteger, ActivityViewType) {
    NoteActivityViewType = 1,    //笔记限时限量活动
    LiveActivityViewType = 2,    //直播限时限量活动
};

//笔记详情,直播详情限时限量活动UI枚举
typedef NS_OPTIONS(NSUInteger, ActivityPurchaseType) {
    ActivityTimeLimitPurchase           = 1 << 0,  //活动限时购买
    ActivityNumberLimitPurchase         = 1 << 1,  //活动限量购买
    ActivityTimeAndNumberLimitPurchase  = 1 << 2,  //活动限时限量购买
};

// 发布内容
typedef NS_ENUM(NSInteger, PublishContentType) {
    PublishContentTypeMoment   = 1, //发布动态
    PublishContentTypeArticle = 2,      // 发布长文
    PublishContentTypeUnknown = 3      // 未知
};

// 发布内容
typedef NS_ENUM(NSInteger, SearchContentType) {
    SearchContentTypeComposite    = 0,  // 综合
    SearchContentTypeNote    = 1,  // 笔记
    SearchContentTypeLive    = 2,  // 视频
    SearchContentTypeAskCode = 3,  // 问股
    SearchContentTypeCourse  = 4,  // 课程
    SearchContentTypeStock   = 5,  // 股票
    SearchContentTypeDaka    = 6   // 投顾
};


// 课程分类页面类型
typedef NS_ENUM(NSInteger, CourseCategoryType) {
    CourseCategoryTypeAll    = 1,    // 优质和系列
    CourseCategoryTypeSingle = 2,   // 优质
    CourseCategoryTypeSeries = 3    // 系列
};

@interface ConstVariable : NSObject

UIKIT_EXTERN NSString *const AppNotFirstLaunch;                                // APP是否第一次启动
UIKIT_EXTERN NSString *const SelfStockGroupCacheKey;                              // 自选股分组缓存key
UIKIT_EXTERN NSString *const UserTaskProgressCacheKey;                              // 用户任务进度缓存key
UIKIT_EXTERN NSString *const FirstRechargeDiscountShowKey;                              // 首充展示key
UIKIT_EXTERN NSString *const PushDeviceIdentifier;                              // 设备推送标识


#pragma mark - 自定义通知NotificationName

UIKIT_EXTERN NSString *const kMyCreationCastToTopNoteNotification;        // 我的创作置顶通知
UIKIT_EXTERN NSString *const kWeXinLoginSuccess;                          // 微信登录成功通知
UIKIT_EXTERN NSString *const kNormalLoginSuccess;                         // 账号登录成功通知
UIKIT_EXTERN NSString *const kAuthLoginSuccess;                           // 静默登录成功通知
UIKIT_EXTERN NSString *const kAccoutLoginOut;                             // 账号注销通知
UIKIT_EXTERN NSString *const kRequestUserInfo;                         // 获取最新的用户信息


UIKIT_EXTERN NSString *const kFocusNumChanged;                            // 关注数量改变通知
UIKIT_EXTERN NSString *const kLiveRoomfocusNumChanged;                     // 直播间关注数量改变通知(区别于其他地方关注 直播间关注需要给直播间发消息)
UIKIT_EXTERN NSString *const kCollectNumChanged;                          // 收藏数量改变通知
UIKIT_EXTERN NSString *const kSubmitAnswerSuccess;                        // 问股回答提交成功通知
UIKIT_EXTERN NSString *const kALiMessageReceive;                          // 收到推送通知
UIKIT_EXTERN NSString *const kALiMessageAffirm;                           // 推送通知点击跳转通知
UIKIT_EXTERN NSString *const kMsgUnreadNumChanged;                        // 获得消息未读数通知
UIKIT_EXTERN NSString *const kAskCodeSuccess;                             // 问股提问成功通知
UIKIT_EXTERN NSString *const kShareSuccess;                               // 分享成功通知
UIKIT_EXTERN NSString *const kShareCancel;                                // 分享取消通知
UIKIT_EXTERN NSString *const kShareFailed;                                // 分享失败通知
UIKIT_EXTERN NSString *const kWXAuthResponse;                             // 微信授权回调通知
UIKIT_EXTERN NSString *const kWXLaunchMiniProgramResponse;                // 微信小程序回调通知
UIKIT_EXTERN NSString *const kWXLaunchFromWXReq;                          // 从微信启动App通知
UIKIT_EXTERN NSString *const kShareCourseAlbum;                           // 分享课程系列
UIKIT_EXTERN NSString *const kQuestionPaySuccess;                         // 问股付费成功通知
UIKIT_EXTERN NSString *const kRechargeSuccess;                            // 充值成功通知
UIKIT_EXTERN NSString *const kUserCoinUpdate;                            // 用户金币更新通知
UIKIT_EXTERN NSString *const kMemberProductPaySuccess;                    // VIP产品购买成功
UIKIT_EXTERN NSString *const kNoteWxPaySuccess;                    // 笔记微信购买成功
UIKIT_EXTERN NSString *const kAlbumPaySuccess;                            // 课程系列支付成功
UIKIT_EXTERN NSString *const kAskCodeDownRefresh;                         // 首页问股下拉刷新通知
UIKIT_EXTERN NSString *const kNoteDownRefresh;                            // 首页笔记下拉刷新通知
UIKIT_EXTERN NSString *const kRefreshTimeChanged;                         // 行情自动刷新时间间隔改变通知
UIKIT_EXTERN NSString *const kPayForChapterSuccess;                       // 训练营章节支付成功通知
UIKIT_EXTERN NSString *const kSimStockTradeAction;                       // 模拟交易买卖通知
UIKIT_EXTERN NSString *const kFinishEvaluate;                             // 点击确认书通知
UIKIT_EXTERN NSString *const kFinishFxcp;                              // 点击风险测评的确定
UIKIT_EXTERN NSString *const kImgClickNotification;                     // 图片浏览器图片点击通知
UIKIT_EXTERN NSString *const kGetHtmlString;                              // 写笔记编辑完成通知
UIKIT_EXTERN NSString *const kAliUpdateMp3Success;                        // mp3上传阿里云成功通知
UIKIT_EXTERN NSString *const kNetworkStatusChangedNotification;        // 网络状态监听通知
UIKIT_EXTERN NSString *const kNetworkStatusChangedToOnlineNotification;        // 从无网到有网的通知
UIKIT_EXTERN NSString *const kFMStartUpdateSearchDatabaseNotification;  // 开始更新搜索数据库
UIKIT_EXTERN NSString *const kFMEndUpdateSearchDatabaseNotification;    // 搜索数据库更新成功
UIKIT_EXTERN NSString *const kFMUpdateSelfStockDatabaseNotification;    // 自选股数据库更新通知
UIKIT_EXTERN NSString *const kFMMustHiddenKeyboardNotification;         // 隐藏键盘通知
UIKIT_EXTERN NSString *const kFMTabBarChangeNotification;               // 界面切换通知
UIKIT_EXTERN NSString *const kFMScrollTabBarSwipeNotification;          // 单滑动通知
UIKIT_EXTERN NSString *const kStockChartLongPressNotification;          // K线图长按
UIKIT_EXTERN NSString *const kStockRestoraitonRightChangeNotification;  // 复权切换
UIKIT_EXTERN NSString *const kKLineSettingSlideValueChanged;             // K线设置中SlideCell值变化
UIKIT_EXTERN NSString *const kKLineSettingIndexPeriodChanged;            // K线设置指标周期变化
UIKIT_EXTERN NSString *const kKLineSettingIndexOrderChanged;              // K线设置指标顺序变化
UIKIT_EXTERN NSString *const kKLineTouchInAuxiliaryView;                 // K线中点击在幅图
UIKIT_EXTERN NSString *const kReportAutoPlay;                            // 研报需要自动播放
UIKIT_EXTERN NSString *const kMP3MusicStop;
UIKIT_EXTERN NSString *const kMP3MusicPlay;
UIKIT_EXTERN NSString *const kMP3MusicPuase;

UIKIT_EXTERN NSString *const kAskCodePriasedNotification; // 问股点赞通知
UIKIT_EXTERN NSString *const kNotePriasedNotification; // 笔记点赞通知
UIKIT_EXTERN NSString *const kLiveAppointmentStatusChangeNotification; // 直播预约状态变化通知
UIKIT_EXTERN NSString *const kNotificationViewClickNotification;                          // 通知栏点击通知
UIKIT_EXTERN NSString *const kNotificationliveInteraction;               // 直播间交互
UIKIT_EXTERN NSString *const kNotificationLiveRelatedStockUpdate;        // 直播间聊天室相关股票更新通知
UIKIT_EXTERN NSString *const kNotificationLiveReplyMessage;              // 直播间聊天室回复我的消息
/// 投顾关注数 在线人数 点赞数  直播室名称 停播
UIKIT_EXTERN NSString *const kNotificationLiveRoomInfoUpdate;             // 直播间信息更新
UIKIT_EXTERN NSString *const kNotificationLiveRoomActiviteInfo;             // 直播间活动信息更新
UIKIT_EXTERN NSString *const kNotificationLiveRoomeOnlySeeDaka;             // 直播间文字直播 只看投顾
UIKIT_EXTERN NSString *const kNotificationLiveSendGiftMessage;             // 直播间送礼物消息
UIKIT_EXTERN NSString *const kNotificationActivityIsSale;             // 笔记详情不添加活动倒计时 列表倒计时结束的时候发送通知
UIKIT_EXTERN NSString *const kPublishContentSuccess;             // 发布内容成功通知
UIKIT_EXTERN NSString *const kCouponExchangeSuccess;             // 卡券兑换成功
UIKIT_EXTERN NSString *const kCommunityPageRefreshData;             // 社区页面刷新
UIKIT_EXTERN NSString *const kDeleteContent;             // 删除内容时首页关注页面
UIKIT_EXTERN NSString *const KNotePlayerCachePlayTime;             // 笔记播放器播放视频进度缓存
UIKIT_EXTERN NSString *const KNoteRewardPaySuccess;             // 笔记赞赏支付成功
UIKIT_EXTERN NSString *const kALLDakaLiveInfo;             // 所有投顾的直播信息
UIKIT_EXTERN NSString *const LiveBarrageUpdate;             // 追加直播弹幕

UIKIT_EXTERN NSString *const kSelfStockGroupChanged;             // 自选股分组改变通知
UIKIT_EXTERN NSString *const kRegistrationChange;             // 信息登记状态改变

UIKIT_EXTERN NSString *const kAppInitDataRefresh;             // 初始化接口数据更新

UIKIT_EXTERN NSString * const kNormalWebVCDealloc;

@end
