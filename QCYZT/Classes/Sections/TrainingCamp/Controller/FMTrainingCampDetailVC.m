//
//  FMTrainingCampDetailVC.m
//  QCYZT
//
//  Created by macPro on 2019/7/25.
//  Copyright © 2019 LZKJ. All rights reserved.
//  第一个添加到contentCell上的VC.view会有偏移的问题，原因未查出，所以多添加了一个子VC

#import "FMTrainingCampDetailVC.h"
#import "FMTrainingCampCommentVC.h"
#import "FMTrainingCampIntroductionVC.h"
#import "FMTrainingCampCatalogueVC.h"
#import "FMTrainingCampChapterDetailVC.h"
#import "FMOuterTableView.h"
#import "FMTrainingCampDetailModel.h"
#import "CourseAlbumBottomView.h"
#import "FMDetailBottomView.h"
#import "FMShareHelper.h"
#import "FMTrainingCampTool.h"
#import "FMTrainingCampChapterListModel.h"
#import "FMPayTool.h"
#import "PaymentView.h"
#import "FMTrainingCampDetailSectionHeader.h"
#import "FMCommonNavView.h"
#import "FMTaskConfigModel.h"
#import "HttpRequestTool+TrainingCamp.h"



@interface FMTrainingCampDetailVC () <UITableViewDelegate, UITableViewDataSource, FMInnerTableVCDelegate, CourseAlbumBottomViewDelegate, FMDetailBottomViewDelegate, FMTrainingCampDetailSectionHeaderDelegate>

@property (nonatomic, strong) FMCommonNavView *navBar;

@property (nonatomic,copy) NSString *bookId;
@property (nonatomic, strong) FMTrainingCampDetailModel *detailModel;

@property (nonatomic, strong) FMTrainingCampDetailSectionHeader *sectionHeader;
@property (nonatomic, strong) FMOuterTableView *tableView;
@property (nonatomic, strong) CourseAlbumBottomView *bottomView;
@property (nonatomic, strong) FMDetailBottomView *commentBottomView;
@property (nonatomic, strong) UITableViewCell *contentCell;
@property (nonatomic, weak) FMInnerTableViewController *showingVC;
@property (nonatomic, strong) MASConstraint *tableViewWithCommentBottomCons;

@property (nonatomic, assign) BOOL isFirstRequest;
@property (nonatomic, assign) BOOL needsRefresh;  // view显示后是否需要请求


@end

@implementation FMTrainingCampDetailVC

- (instancetype)initWithBookId:(NSString *)bookId {
    if (self = [super init]) {
        self.bookId = bookId;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.extendedLayoutIncludesOpaqueBars = YES;
    self.view.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    [self configNavigationBarViews];
    self.isFirstRequest = YES;

    [self.view addSubview:self.bottomView];
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(@(0));
        make.height.equalTo(@(CourseAlbumBottomHeight));
    }];
    self.bottomView.hidden = YES;
    
    [self.view addSubview:self.commentBottomView];
    [self.commentBottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        make.bottom.equalTo(@0);
        make.height.equalTo(@(DetailBottomViewHeight));
    }];
    self.commentBottomView.hidden = YES;
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(@(0));
        make.bottom.equalTo(self.bottomView.mas_top).priorityMedium();
        self.tableViewWithCommentBottomCons = make.bottom.equalTo(self.commentBottomView.mas_top).priorityHigh();
    }];

    [self requestData];
    [self addChildVC];
    
    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(handleLoginStatusNotification) monitorAuthLogin:NO];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleLoginStatusNotification) name:kRechargeSuccess object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(campChapterPaySuccess) name:@"campChapterPaySuccess" object:nil];
}

- (void)viewWillAppear:(BOOL)animated {
    self.selfNavigationBarHidden = YES;
    [super viewWillAppear:animated];
    if (self.detailModel) {  // 如果列表已经有数据了，调用该方法让nav变色
        [self scrollViewDidScroll:self.tableView];
    }
    
    if (self.needsRefresh) {
        [self requestData];
        self.needsRefresh = NO;
    }
}

- (void)configNavigationBarViews {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.view addSubview:self.navBar];
        [self.navBar mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.equalTo(@0);
            make.height.equalTo(@(UI_SAFEAREA_TOP_HEIGHT));
        }];
    });
}

- (void)viewWillLayoutSubviews {
    [super viewWillLayoutSubviews];
    
    [self scrollViewDidScroll:self.tableView];
}

#pragma mark - TableView Delegate/Datasource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    return self.contentCell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.sectionHeader.segmentedCtr.selectedSegmentIndex == 2) {
        return ceil(UI_SCREEN_HEIGHT - UI_SegmentControl_Height  - UI_SAFEAREA_TOP_HEIGHT - self.commentBottomView.height);
    } else {
        if (self.detailModel) {
            return ceil(UI_SCREEN_HEIGHT - UI_SegmentControl_Height  - UI_SAFEAREA_TOP_HEIGHT - self.bottomView.height);
        }
        return ceil(UI_SCREEN_HEIGHT  - UI_SAFEAREA_TOP_HEIGHT - self.bottomView.height);
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (self.detailModel) {
        return self.sectionHeader;
    }
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (self.detailModel) {
        return UI_SegmentControl_Height + UI_SAFEAREA_TOP_HEIGHT;
    }
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - UIScrollView Delegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView
{
    CGFloat offsetY = scrollView.contentOffset.y;
    CGFloat topCellOffset = [self.tableView rectForSection:0].origin.y;
    
    // 如果里层tableView的偏移量大于0，将外层tableView的偏移量定在tableTopViewHeight，保持悬停
    if (self.showingVC.tableView.contentOffset.y > 0) {
        self.tableView.contentOffset = CGPointMake(0, topCellOffset);
    }
    
    // 如果外层tableView偏移量小于tableTopViewHeight（也就是头部视图正在显示），发出通知让每个子tableView的偏移量变成0
    CGFloat offSetY = (self.tableView.contentOffset.y);
    if (offSetY < topCellOffset) {
        for (FMInnerTableViewController *VC in self.childViewControllers) {
            VC.tableView.contentOffset = CGPointZero;
        }
    }
    
    // 导航栏颜色
    CGFloat alpha = offSetY / ceil(kTrainingCampDetailSectionHeaderImgHeight - UI_SAFEAREA_TOP_HEIGHT);
    
    UIColor *navColor = [UIColor.fm_nav_color performSelector:@selector(resolvedColor)];
    self.navBar.backgroundColor = [navColor colorWithAlphaComponent:alpha];
}

#pragma mark - FMInnerTableVCDelegate
- (void)innerTableVCTableviewScroll:(UITableView *)innerTableview {
    CGFloat tableTopViewHeight = ([self.tableView rectForSection:0].origin.y);
    // 如果外层tableView偏移量小于tableTopViewHeight（也就是头部视图正在显示），将子tableView的偏移量变成0
    if ((self.tableView.contentOffset.y) < tableTopViewHeight) {
        innerTableview.contentOffset = CGPointZero;
        innerTableview.showsVerticalScrollIndicator = NO;
    } else {
        innerTableview.showsVerticalScrollIndicator = YES;
    }
}

#pragma mark - CourseAlbumBottomViewDelegate
- (void)courseAlbumBottomViewBtnClick:(UIButton *)btn {
    switch (btn.tag) {
        case CourseAlbumBottomViewBtnTagWatch: // 试读
        {
            [self trainingCampTryRead];
        }
            break;
            
        case CourseAlbumBottomViewBtnTagPay: // 支付
        {
            if ([FMHelper checkLoginStatus]) {
                if (self.detailModel.windowType == 2) {
                    [FMHelper showVIPAlertWithType:VIPReadTypeTraining needVip:self.detailModel.needVip authority:@"0" price:[self.detailModel.price floatValue] showAlert:YES clickSure:^{
                    } clickCancel:nil clickSureDismiss:YES];
                } else {
                    [self gotoPay];
                }
            }
        }
            break;
            
        default:
            break;
    }
}

- (void)judgeJumpToFirstTrailChapter {
    NSDictionary *dic = self.detailModel.firstTrialChapter;
    
    FMTrainingCampChapterDetailVC *vc = [[FMTrainingCampChapterDetailVC alloc] init];
    vc.campDetailModel = self.detailModel;
    FMTrainingCampChapterListModel *model = [FMTrainingCampChapterListModel new];
    model.bookid = [NSString stringWithFormat:@"%@", dic[@"bookid"]];
    model.perm =  [dic[@"perm"] boolValue];
    model.price = [NSString stringWithFormat:@"%@", dic[@"price"]];
    model.title = [NSString stringWithFormat:@"%@", dic[@"title"]];
    model.chapterId = [NSString stringWithFormat:@"%@", dic[@"id"]];
    model.lastUpdateTime = [NSString stringWithFormat:@"%@", dic[@"lastUpdateTime"]];
    vc.chapterListModel = model;
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - FMDetailBottomView Delegate
- (void)detailBottomViewCommentBtnDidClicked:(UIView *)bottomView btn:(UIButton *)commentBtn {
    FMTrainingCampCommentVC *vc = self.childViewControllers.lastObject;
    [vc detailBottomViewCommentBtnDidClicked:bottomView btn:commentBtn];
}

#pragma mark - Request
- (void)requestData {
    WEAKSELF;
    [HttpRequestTool trainingCampDetailWithBookId:self.bookId start:^{
        if (__weakSelf.isFirstRequest) {
            [SVProgressHUD show];
        }
    } failure:^{
        [SVProgressHUD dismiss];
        [__weakSelf.tableView.mj_header endRefreshing];
        UIView *networkView = [__weakSelf.view showReloadNetworkViewWithBlock:^{
            [__weakSelf requestData];
        }];
        [__weakSelf.view insertSubview:networkView belowSubview:__weakSelf.navBar];
        __weakSelf.navBar.titleLabel.text = @"训练营";
    } success:^(NSDictionary *dic) {
        [__weakSelf.tableView.mj_header endRefreshing];
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            __weakSelf.isFirstRequest = NO;
            [__weakSelf scrollViewDidScroll:__weakSelf.tableView];
            __weakSelf.navigationItem.rightBarButtonItem.enabled = YES;
            dispatch_async(dispatch_get_main_queue(), ^{
                [__weakSelf segmentedControlChangedValue:__weakSelf.sectionHeader.segmentedCtr];
            });
                        
            FMTrainingCampDetailModel *detailModel = [FMTrainingCampDetailModel modelWithJSON:dic[@"data"]];
            __weakSelf.detailModel = detailModel;
            __weakSelf.navBar.titleLabel.text = detailModel.name;
            
            [__weakSelf changeBottomView];
            if (__weakSelf.tableView.tableHeaderView == nil) {
                __weakSelf.tableView.tableHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, ceil(kTrainingCampDetailSectionHeaderImgHeight - UI_SAFEAREA_TOP_HEIGHT))];
            }
            __weakSelf.sectionHeader.model = detailModel;
            
            FMTrainingCampIntroductionVC *vc1 = __weakSelf.childViewControllers[1];
            vc1.detailModel = detailModel;
            FMTrainingCampCatalogueVC *vc2 = __weakSelf.childViewControllers[2];
            vc2.detailModel = detailModel;
            
            [[NSNotificationCenter defaultCenter] postNotificationName:@"campSubscribeSuccess" object:nil];
            __weakSelf.navBar.rightImgV.hidden = NO;
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            UIView *networkView = [__weakSelf.view showReloadNetworkViewWithBlock:^{
                [__weakSelf requestData];
            }];
            [__weakSelf.view insertSubview:networkView belowSubview:__weakSelf.navBar];
            __weakSelf.navBar.titleLabel.text = @"训练营";
            __weakSelf.navBar.rightImgV.hidden = YES;
        }
    }];
}

#pragma mark - NSNotification
- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}


- (void)campChapterPaySuccess {
    if ([FMHelper getCurrentVC] == self) {
        [self requestData];
    } else {
        self.needsRefresh = YES;
    }
}

#pragma mark - Private
- (void)share {
    [FMShareHelper shareTrainingCampDetailWithModel:self.detailModel];
}

- (void)addChildVC {
    FMInnerTableViewController *vc0 = [[FMInnerTableViewController alloc] init];
    vc0.delegate = self;
    [self addChildViewController:vc0];
    
    FMTrainingCampIntroductionVC *vc1 = [[FMTrainingCampIntroductionVC alloc] init];
    vc1.delegate = self;
    [self addChildViewController:vc1];
    
    FMTrainingCampCatalogueVC *vc2 = [[FMTrainingCampCatalogueVC alloc] initWithBookId:self.bookId];
    vc2.delegate = self;
    [self addChildViewController:vc2];
    
    FMTrainingCampCommentVC *vc3 = [[FMTrainingCampCommentVC alloc] initWithBookId:self.bookId];
    vc3.delegate = self;
    [self addChildViewController:vc3];
    
    for (FMInnerTableViewController *vc in self.childViewControllers) {
        vc.tableView.backgroundColor = UIColor.up_contentBgColor;
    }
}

- (void)segmentedControlChangedValue:(HMSegmentedControl*)sender {
    [_showingVC.view removeFromSuperview];
    
    NSInteger index = sender.selectedSegmentIndex;
    FMInnerTableViewController *subVC = self.childViewControllers[index + 1];
    if (!subVC.view.superview) {
        [self.contentCell.contentView addSubview:subVC.view];
        [subVC.view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self.contentCell.contentView);
        }];
    }
    [self.contentCell.contentView bringSubviewToFront:subVC.view];
    _showingVC = subVC;
    
    if (sender.selectedSegmentIndex == 1) {
        FMTrainingCampCatalogueVC *vc = (FMTrainingCampCatalogueVC *)subVC;
        vc.detailModel = self.detailModel;
    }
    
    [self changeBottomView];
}

- (void)changeBottomView {
    if (!self.detailModel) {
        self.commentBottomView.hidden = YES;
        self.bottomView.hidden = YES;
        return;
    }
    
    if (self.sectionHeader.segmentedCtr.selectedSegmentIndex == 2) { // 显示评论框
        self.commentBottomView.hidden = NO;
        self.bottomView.hidden = YES;
        [self.tableViewWithCommentBottomCons activate];
    } else {
        self.commentBottomView.hidden = YES;
        [self.tableViewWithCommentBottomCons deactivate];

        if (self.detailModel.windowType == 2) { // 需要升级
            NSString *perm = [self.detailModel.firstTrialChapter objectForKey:@"perm"];
            if (![perm integerValue]) { // 不可试读
                [self.bottomView.stackView removeArrangedSubview:self.bottomView.watchBtn];
                self.bottomView.watchBtn.hidden = YES;
            }
            [self.bottomView.payBtn setTitle:self.detailModel.payText forState:UIControlStateNormal];
            self.bottomView.hidden = NO;
        } else {
            if (!self.detailModel.perm) { // 没有权限
                NSString *perm = [self.detailModel.firstTrialChapter objectForKey:@"perm"];
                if (![perm integerValue]) { // 不可试读
                    [self.bottomView.stackView removeArrangedSubview:self.bottomView.watchBtn];
                    self.bottomView.watchBtn.hidden = YES;
                }
                [self.bottomView.payBtn setTitle:self.detailModel.payText forState:UIControlStateNormal];
                self.bottomView.hidden = NO;
            } else { // 有权限
                [self.bottomView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.equalTo(@(UI_SAFEAREA_BOTTOM_HEIGHT));
                }];
                self.bottomView.hidden = YES;
            }
        }
    }
    
    [self.tableView reloadData];
}

- (void)trainingCampTryRead {
    FMTrainingCampChapterListModel *model = [FMTrainingCampTool queryNewestChapterWithBookId:self.detailModel.bookid];
    if (model) {
        FMTrainingCampChapterDetailVC *vc = [[FMTrainingCampChapterDetailVC alloc] init];
        vc.campDetailModel = self.detailModel;
        vc.chapterListModel = model;
        [self.navigationController pushViewController:vc animated:YES];
    } else {
        NSDictionary *dic = self.detailModel.firstTrialChapter;
        BOOL perm = [dic[@"perm"] boolValue];
        
        if (perm) {
            [self judgeJumpToFirstTrailChapter];
        } else {
            if ([FMHelper checkLoginStatus]) {
                [FMTrainingCampTool getContentDetailWithChapterId:dic[@"id"] trainingCampDetailModel:self.detailModel forbiddenView:self.bottomView success:^(FMTrainingCampChapterDetailModel *model) {
                    [self judgeJumpToFirstTrailChapter];
                } failure:^{
                    
                }];
            }
        }
    }
}

- (void)checkPayStatus {
    if (self.detailModel.windowType == 2) {
        [FMHelper showVIPAlertWithType:VIPReadTypeListenCode needVip:self.detailModel.needVip authority:@"0" price:[self.detailModel.price floatValue] showAlert:YES clickSure:nil clickCancel:nil clickSureDismiss:YES];
    } else {
        [self gotoPay];
    }
}

- (void)gotoPay {
    [FMTrainingCampTool httpForPayTrainingCampWithTrainingCampDetailModel:self.detailModel forbiddenView:self.bottomView.payBtn success:^(FMTrainingCampChapterDetailModel *model) {
        [self requestData];
    } failure:^{
        
    }];
}


#pragma mark - Getter/Setter

- (FMOuterTableView *)tableView {
    if (!_tableView) {
        _tableView = [[FMOuterTableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.tableFooterView = [[UIView alloc] init];
        
        _tableView.bounces = NO;
    }
    
    return _tableView;
}

- (FMTrainingCampDetailSectionHeader *)sectionHeader {
    if (!_sectionHeader) {
        _sectionHeader = [[FMTrainingCampDetailSectionHeader alloc] init];
        _sectionHeader.delegate = self;
    }
    
    return _sectionHeader;
}

- (UITableViewCell *)contentCell {
    if (!_contentCell) {
        UITableViewCell *cell = [[UITableViewCell alloc] init];
        cell.contentView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        _contentCell = cell;
    }
    
    return _contentCell;
}

- (CourseAlbumBottomView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[CourseAlbumBottomView alloc] init];
        _bottomView.delegate = self;
        [_bottomView.stackView removeArrangedSubview:self.bottomView.shareBtn];
        _bottomView.shareBtn.hidden = YES;
        [_bottomView.watchBtn setTitle:@"免费试读" forState:UIControlStateNormal];
    }
    
    return _bottomView;
}

- (FMDetailBottomView *)commentBottomView {
    if (!_commentBottomView) {
        _commentBottomView = [[FMDetailBottomView alloc] init];
        _commentBottomView.delegate = self;
        _commentBottomView.commentBtn.titleLabel.font = FontWithSize(15);

        FMTaskConfigModel *taskConfig = [FMUserDefault getUnArchiverDataForKey:AppInit_Task];
        if (taskConfig.taskDic[@(FMTaskTypeComment)].isEnable) {
            [_commentBottomView.commentBtn setTitle:[NSString stringWithFormat:@"发表精彩评论免费获取%zd积分", taskConfig.taskDic[@(FMTaskTypeComment)].awardValue] forState:UIControlStateNormal];
        } else {
            [_commentBottomView.commentBtn setTitle:@"发表精彩评论" forState:UIControlStateNormal];
        }
    }
    return _commentBottomView;
}

- (void)setCurrentIndex:(NSInteger)currentIndex {
    _currentIndex = currentIndex;
    self.sectionHeader.segmentedCtr.selectedSegmentIndex = currentIndex;
}

- (FMCommonNavView *)navBar {
    if (!_navBar) {
        WEAKSELF
        _navBar = [[FMCommonNavView alloc] init];
        _navBar.backBlock = ^{
            [__weakSelf.navigationController popViewControllerAnimated:YES];
        };
        _navBar.rightClickBlock = ^{
            [__weakSelf share];
        };
        _navBar.rightImgV.hidden = YES;
    }
    
    return _navBar;
}

@end
