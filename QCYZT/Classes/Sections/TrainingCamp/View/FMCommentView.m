//
//  FMCommentView.m
//  QCYZT
//
//  Created by th on 17/2/23.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "FMCommentView.h"
#import "NSString+emoji.h"
#import "IQKeyboardManager.h"

@interface FMCommentView() <UITextViewDelegate>

@property (nonatomic,weak) UIView *contentView;
@property (nonatomic,weak) UIButton *cancelBtn;

@end


@implementation FMCommentView

- (id)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setUp];
    }
    return self;
}

- (void)willMoveToSuperview:(UIView *)newSuperview {
    [super willMoveToSuperview:newSuperview];

    [IQKeyboardManager sharedManager].enableAutoToolbar = false;
}

- (void)removeFromSuperview {
    [super removeFromSuperview];
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = true;
    [IQKeyboardManager sharedManager].enableAutoToolbar = true;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setUp {
    UIView *bgView = [[UIView alloc] init];
    [self addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    bgView.backgroundColor = ColorWithHexAlpha(0x000000, 0.5);
    [bgView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(removeFromSuperview)]];

    UIView *contentView = [[UIView alloc] init];
    [self addSubview:contentView];
    [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(@(0));
        make.height.equalTo(251);
    }];
    contentView.backgroundColor = UIColor.up_contentBgColor;
    [contentView layerAndBezierPathWithRect:CGRectMake(0, 0, UI_SCREEN_WIDTH, 281) cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight];
    self.contentView = contentView;

    UILabel *publishLb = [[UILabel alloc] init];
    [contentView addSubview:publishLb];
    [publishLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(10);
        make.top.equalTo(10);
        make.right.equalTo(@(-60));
    }];
    publishLb.textColor = UIColor.up_textSecondary2Color;
    publishLb.font = [UIFont boldSystemFontOfSize:15];
    self.publisLb = publishLb;

    UIButton *cancelBtn = [[UIButton alloc] init];
    [contentView addSubview:cancelBtn];
    [cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(publishLb);
        make.right.equalTo(@-10);
        make.size.equalTo(CGSizeMake(50, 30));
    }];
    [cancelBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentRight];
    cancelBtn.backgroundColor = [UIColor clearColor];
    [cancelBtn setTitleColor:UIColor.up_textPrimaryColor forState:UIControlStateNormal];
    [cancelBtn setTitle:@"取消" forState:UIControlStateNormal];
    cancelBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    [cancelBtn addTarget:self action:@selector(cancel) forControlEvents:UIControlEventTouchUpInside];
    self.cancelBtn = cancelBtn;

    FMTextView *textView = [[FMTextView alloc] init];
    textView.font = FontWithSize(16);
    textView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    textView.placehoderColor = UIColor.fm_BFBFBF_888888;
    textView.textColor = UIColor.up_textPrimaryColor;
    UI_View_BorderRadius(textView, 5, 1, ColorWithHex(0xe5e5e5));
    textView.delegate = self;
    textView.textContainerInset = UIEdgeInsetsMake(8, 10, 8, 0);
    [contentView addSubview:textView];
    [textView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.top.equalTo(publishLb.mas_bottom).offset(10);
        make.height.equalTo(150);
    }];
    self.textView = textView;

    UIButton *publishBtn = [[UIButton alloc] init];
    [contentView addSubview:publishBtn];
    [publishBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(textView.mas_bottom).offset(10);
        make.right.equalTo(@(-10));
        make.width.equalTo(75);
        make.height.equalTo(35);
        make.bottom.mas_equalTo(-10);
    }];
    publishBtn.layer.cornerRadius = 3.0f;
    publishBtn.backgroundColor = ColorWithHex(0xababab);
    publishBtn.titleLabel.font = [UIFont systemFontOfSize:18];
    [publishBtn setTitleColor:FMWhiteColor forState:UIControlStateNormal];
    [publishBtn setTitle:@"发送" forState:UIControlStateNormal];
    [publishBtn addTarget:self action:@selector(publish) forControlEvents:UIControlEventTouchUpInside];
    publishBtn.userInteractionEnabled = NO;
    self.publishBtn = publishBtn;

    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillChangeFrame:) name:UIKeyboardWillChangeFrameNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(removeFromSuperview) name:kALiMessageAffirm object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(uptheme_themeDidUpdate) name:kUPThemeDidChangeNotification object:nil];
}

- (void)didMoveToSuperview {
    [self.textView becomeFirstResponder];
}

- (void)cancel {
    [self removeFromSuperview];
}

- (void)publish {
    if ([FMHelper checkLoginStatus]) {
        if ([self characterJudge]) {
            [self removeFromSuperview];
            WEAKSELF;
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                if (__weakSelf.publishBlock) {
                    __weakSelf.publishBlock(__weakSelf.textView.text);
                }
            });
        }
    }
}

#pragma mark - UITextViewDelegate
-(void)textViewDidChange:(UITextView *)textView{
    if (textView.text.length<=0) {
        self.publishBtn.backgroundColor = ColorWithHex(0xababab);
        self.publishBtn.userInteractionEnabled = NO;
    }else{
        if ([textView.text isContainEmoji] && !self.supportEmoji) {
            [SVProgressHUD showErrorWithStatus:@"暂不支持输入表情"];
            return;
        }

        NSInteger limitNum = self.limitWordNum;
        if (limitNum == 0) {
            limitNum = 100;
        }
        if (textView.text.length > limitNum)
        {
            textView.text = [textView.text substringToIndex:limitNum];
            [SVProgressHUD showInfoWithStatus:[NSString stringWithFormat:@"最多输入%zd个字", limitNum]];
        }

        self.publishBtn.backgroundColor = FMNavColor;
        self.publishBtn.userInteractionEnabled = YES;
    }
}

- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    if ([text isContainEmoji] && !self.supportEmoji) {
        [textView resignFirstResponder];
        [SVProgressHUD showErrorWithStatus:@"暂不支持输入表情"];
        return NO;
    }
    return YES;;
}



#pragma mark - Notification
- (void)keyboardWillChangeFrame:(NSNotification *)notification
{
    NSDictionary *userInfo = notification.userInfo;
    double duration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    CGRect keyboardF = [userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    
    [UIView animateWithDuration:duration animations:^{
        if (keyboardF.origin.y < UI_SCREEN_HEIGHT) {
            self.transform = CGAffineTransformMakeTranslation(0, - keyboardF.size.height);
        } else {
            self.transform = CGAffineTransformMakeTranslation(0,  0);
        }
    }];
}

#pragma mark - Private
- (BOOL)characterJudge {
    NSString *inputStr = [self.textView.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if (!inputStr.length) {
        [SVProgressHUD showErrorWithStatus:@"输入内容不能为空~"];
        return NO;
    }

    if ([self.textView.text isContainEmoji] && !self.supportEmoji) {
        [SVProgressHUD showErrorWithStatus:@"暂不支持输入表情"];
        return NO;
    }

    return YES;
}

- (void)uptheme_themeDidUpdate {
    // 主题更新处理
}

#pragma mark - Setter/Getter
- (void)setPlaceholderText:(NSString *)placeholderText {
    _placeholderText = placeholderText;
    self.textView.placehoder = placeholderText;
}

@end

