//
//  FMMemberCenterConstants.h
//  QCYZT
//
//  Created by zeng on 2023/10/13.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface FMMemberCenterConstants : NSObject

// vip类型
typedef NS_ENUM(NSInteger, JCVIPType) {
    JCVIPTypeNone = 0,
    JCVIPTypeDXQNGJ, // 短线擒牛冠军版
    JCVIPTypeGSYJ, // 股市赢家
    JCVIPTypeDXQN, // 短线擒牛
    JCVIPTypeGSYJDZ, // 股市赢家定制版
    JCVIPTypeJCQL, // 决策擒龙
    JCVIPTypeJCDJ, // 决策点金
};

// 暂停类型
typedef NS_ENUM(NSUInteger, JCSuspendType) {
    JCSuspendTypeJCQL = 1 << 0, // 决策擒龙暂停
    JCSuspendTypeJCDJ = 1 << 1, // 决策点金暂停
};

// 股票池type
typedef NS_ENUM(NSInteger, JCStockPoolType) {
    JCStockPoolTypeLongTouQiSheng = 1,
    JCStockPoolTypeLongTouZaiZhang,
    JCStockPoolTypeLongTouYinZi,
    JCStockPoolTypeLongTouMiXun,
    JCStockPoolTypeJiaZhiLongTou = 11,
    JCStockPoolTypeLongHuZhuaYao,
    JCStockPoolTypeDaShiTouYan
};

// 股票池名称
NSString *PoolTypeName(JCStockPoolType type);

// 股池介绍
NSString *PoolTypeGCJS(JCStockPoolType type);
NSString *PoolTypeGCJSLink(JCStockPoolType type);

// 股票池主图指标名
NSString *PoolTypeZTZB(JCStockPoolType type);

// 股票池对应的行情图标
NSString *PoolTypeIcon(JCStockPoolType type);
NSString *PoolTypeIconString(JCStockPoolType type);

// 附图指标，返回@"“量能异动”、“换手热度”"这种已经拼接好的字符串
NSString *PoolTypeFTZB(JCStockPoolType type, NSArray<NSNumber *> *indices);
// 返回@"“量能异动”“换手热度”及“主力流向”"这种已经拼接好的字符串
NSString *PoolTypeFTZBReminder(JCStockPoolType type);

@end

NS_ASSUME_NONNULL_END
