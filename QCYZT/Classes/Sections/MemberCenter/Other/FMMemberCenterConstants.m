//
//  FMMemberCenterConstants.m
//  QCYZT
//
//  Created by zeng on 2023/10/13.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMMemberCenterConstants.h"

@implementation FMMemberCenterConstants

static NSDictionary<NSNumber *, NSDictionary *> *MemberCenterVIPJCConfigDic;

NSString *PoolTypeName(JCStockPoolType type) {
    return MemberCenterVIPJCConfigDic[@(type)][@"name"];
}

NSString *PoolTypeGCJS(JCStockPoolType type) {
    return MemberCenterVIPJCConfigDic[@(type)][@"gcjs"];
}

NSString *PoolTypeGCJSLink(JCStockPoolType type) {
    return MemberCenterVIPJCConfigDic[@(type)][@"gcjsLink"];
}

NSString *PoolTypeZTZB(JCStockPoolType type) {
    return MemberCenterVIPJCConfigDic[@(type)][@"ztzb"];
}

NSString *PoolTypeIcon(JCStockPoolType type) {
    return MemberCenterVIPJCConfigDic[@(type)][@"icon"];
}

NSString *PoolTypeIconString(JCStockPoolType type) {
    return MemberCenterVIPJCConfigDic[@(type)][@"iconString"];
}

NSString *PoolTypeFTZB(JCStockPoolType type, NSArray<NSNumber *> *indices) {
    NSArray *ftzbArray = MemberCenterVIPJCConfigDic[@(type)][@"ftzb"];
    NSMutableString *result = [NSMutableString string];
    
    for (NSInteger i = 0; i < indices.count; i++) {
        NSInteger idx = [indices[i] integerValue];
        if (idx >= 1 && idx <= ftzbArray.count) {
            [result appendFormat:@"“%@”", ftzbArray[idx-1]];
            if (i < indices.count - 1) {
                [result appendFormat:@"、"];
            }
        }
    }
    
    return [result copy];
}

NSString *PoolTypeFTZBReminder(JCStockPoolType type) {
    NSArray *ftzbArray = MemberCenterVIPJCConfigDic[@(type)][@"ftzb"];
    NSMutableString *result = [NSMutableString string];
    
    for (NSInteger i = 0; i < ftzbArray.count; i++) {
        [result appendFormat:@"“%@”", ftzbArray[i]];
        if (i == ftzbArray.count - 2) {
            [result appendFormat:@"及"];
        }
    }
    
    return [result copy];
}

__attribute__((constructor))
static void initializeGlobalConfigDic() {
    MemberCenterVIPJCConfigDic = @{
        @(JCStockPoolTypeLongTouQiSheng) : @{@"name" : @"龙头启升",
                                             @"gcjs" : @"龙头启升专注于龙头股初期阶段，以操盘线和决策线为基础，融入涨停因子、量能因子及K线形态等综合算法形成选股指标。每日入池股票按信号触发顺序展示，但需注意盘中信号可能发生改变，为了确保信息的准确性，建议在收盘信号确认后再次查看。", // 股池介绍
                                             @"gcjsLink" : @"/m/app/active/vipIntroduce/ltqs", // 股池介绍
                                             @"ztzb" : @"启动追踪",  // 主图指标
                                             @"icon" : @"MemberCenter_Rocket",
                                             @"iconString" : @"火箭",
                                             @"ftzb" : @[@"量能异动", @"换手热度", @"主力流向"], // 副图指标
        },
        @(JCStockPoolTypeLongTouZaiZhang) : @{@"name" : @"龙头再涨",
                                            @"gcjs" : @"龙头再涨专注于龙头股第二次启动阶段，以操盘线和决策线为基础，融入量能因子、资金因子及K线形态等综合算法形成选股指标。每日入池股票按信号触发顺序展示，但需注意盘中信号可能发生改变，为了确保信息的准确性，建议在收盘信号确认后再次查看。",
                                            @"gcjsLink" : @"/m/app/active/vipIntroduce/lteb",
                                            @"ztzb" : @"双龙狙击",
                                            @"icon" : @"MemberCenter_Diamond",
                                            @"iconString" : @"钻石",
                                            @"ftzb" : @[@"龙头量能", @"动能二号", @"换手热度"],
        },
        @(JCStockPoolTypeLongTouYinZi) : @{@"name" : @"龙头因子",
                                              @"gcjs" : @"龙头因子专注于挖掘潜在龙头股，以操盘线和决策线为基础，融入量能因子、随机指标因子及换手因子等综合算法形成选股指标。每日入池股票按信号触发顺序展示，但需注意盘中信号可能发生改变，为了确保信息的准确性，建议在收盘信号确认后再次查看。",
                                              @"gcjsLink" : @"/m/app/active/vipIntroduce/qlzy",
                                              @"ztzb" : @"擒龙追踪",
                                              @"icon" : @"MemberCenter_Dragon",
                                              @"iconString" : @"神龙",
                                              @"ftzb" : @[@"共振追涨", @"强势起爆", @"动能二号"],
        },
        @(JCStockPoolTypeJiaZhiLongTou) : @{@"name" : @"价值龙头",
                                              @"gcjs" : @"价值龙头专注于挖掘市场中具有价值投资潜力的股票，以波段理论为基础，融入基本面因子、波动因子及量能因子等综合算法形成选股指标。每日入池股票按信号触发顺序展示，但需注意盘中信号可能发生改变，为了确保信息的准确性，建议在收盘信号确认后再次查看。",
                                              @"gcjsLink" : @"/m/app/active/vipIntroduce/jcjt",
                                              @"ztzb" : @"波段牛熊",
                                              @"icon" : @"MemberCenter_Cattle",
                                              @"iconString" : @"",
                                              @"ftzb" : @[@"主力流向", @"量能异动", @"共振追涨"],
        },
        @(JCStockPoolTypeLongHuZhuaYao) : @{@"name" : @"龙虎抓妖",
                                              @"gcjs" : @"龙虎抓妖专注于分析龙虎榜上榜股票，通过对游资和机构交易数据的细致拆解，设置了包括超买、强买、弱买、巨买、爆买、连买、独大、控盘、游资及机游等多种买入模型，并将当日上榜股票按不同模型分类入池，每个交易日17:00后更新。",
                                              @"gcjsLink" : @"/m/app/active/vipIntroduce/jczy",
                                              @"ztzb" : @"游资模型",
                                              @"icon" : @"",
                                              @"iconString" : @"",
                                              @"ftzb" : @[@"席位资金", @"机构资金", @"龙虎动力"],
        }
    };
}

@end
