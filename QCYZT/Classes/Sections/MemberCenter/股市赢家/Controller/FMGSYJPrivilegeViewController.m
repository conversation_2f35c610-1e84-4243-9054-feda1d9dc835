//
//  FMGSYJPrivilegeViewController.m
//  QCYZT
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/7/22.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMGSYJPrivilegeViewController.h"
#import "FMGSYJPrivilegeVideoCell.h"
#import "FMGSYJPrivilegeReportCell.h"
#import "FMGSYJPrivilegeServiceCell.h"
#import "FMF10TradeMustReadSectionHeader.h"
#import "FMGSYJChiefInvestmentContactView.h"
#import "FMGSYJPrivilegeInformationModel.h"
#import "HttpRequestTool+MemberCenter.h"
#import "FMF10NoDataCell.h"
#import "FMGSYJPrivilegeVideoListViewController.h"
#import "FMGSYJPrivilegeReportListViewController.h"
#import "FMGSYJReprotViewController.h"  // For HTML content
#import "FMPDFReaderViewController.h" // For PDF content
#import "FMVIPXLZFDetailVC.h"
#import "YTGNormalWebVC.h"
#import "MemberCenterVisitManager.h"

typedef NS_ENUM(NSInteger, FMGSYJContentType) {
    FMGSYJContentTypeSmallClassMarket = 0,  // 小班看市
    FMGSYJContentTypeChiefClassroom,        // 首席课堂
    FMGSYJContentTypeStrategyReport,        // 策略报告
    FMGSYJContentTypeInvestmentReport,      // 投研报告
    FMGSYJContentTypeMacroEconomy,         // 宏观经济
    FMGSYJContentTypeWealthDaily,          // 财富日刊
    FMGSYJContentTypeService                // 服务区域
};

@interface FMGSYJPrivilegeViewController () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) NSArray<FMGSYJPrivilegeVideoModel *> *videoModels;
@property (nonatomic, strong) NSArray<FMGSYJPrivilegeReportModel *> *strategyReportModels;
@property (nonatomic, strong) NSArray<FMGSYJPrivilegeReportModel *> *investmentReportModels;
@property (nonatomic, strong) NSArray<FMGSYJPrivilegeReportModel *> *wealthReportModels;
@property (nonatomic, strong) NSArray<FMGSYJPrivilegeReportModel *> *smallClassMarketModels;
@property (nonatomic, strong) NSArray<FMGSYJPrivilegeReportModel *> *macroeconomicModels;

@property (nonatomic, strong) NSArray<FMGSYJServiceItemModel *> *serviceModels;

// 存储各section类型顺序
@property (nonatomic, strong) NSArray<NSNumber *> *sectionTypes;
// 存储各section标题
@property (nonatomic, strong) NSDictionary<NSNumber *, NSString *> *sectionTitles;
// 存储各section对应的接口类型
@property (nonatomic, strong) NSDictionary<NSNumber *, NSNumber *> *sectionApiTypes;



@end

@implementation FMGSYJPrivilegeViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.backgroundColor = UIColor.up_contentBgColor;
    [self setupSectionConfig];
    [self setupTableView];
    [self loadData];
}

- (void)setupSectionConfig {
    // 配置section标题
    self.sectionTitles = @{
        @(FMGSYJContentTypeSmallClassMarket): @"小班看市",
        @(FMGSYJContentTypeChiefClassroom): @"首席课堂",
        @(FMGSYJContentTypeStrategyReport): @"策略报告",
        @(FMGSYJContentTypeInvestmentReport): @"投研报告",
        @(FMGSYJContentTypeMacroEconomy): @"宏观经济",
        @(FMGSYJContentTypeWealthDaily): @"财富日刊"
    };
    
    // 配置section接口类型
    self.sectionApiTypes = @{
        @(FMGSYJContentTypeSmallClassMarket): @(8),
        @(FMGSYJContentTypeStrategyReport): @(1),
        @(FMGSYJContentTypeInvestmentReport): @(2),
        @(FMGSYJContentTypeMacroEconomy): @(12),
        @(FMGSYJContentTypeWealthDaily): @(4)
    };
    
    // 配置section顺序
    if (self.serverType == JCVIPTypeGSYJDZ) {
        self.sectionTypes = @[
            @(FMGSYJContentTypeSmallClassMarket),
            @(FMGSYJContentTypeChiefClassroom),
            @(FMGSYJContentTypeStrategyReport),
            @(FMGSYJContentTypeInvestmentReport),
            @(FMGSYJContentTypeMacroEconomy),
            @(FMGSYJContentTypeWealthDaily),
            @(FMGSYJContentTypeService)
        ];
    } else {
        self.sectionTypes = @[
            @(FMGSYJContentTypeChiefClassroom),
            @(FMGSYJContentTypeStrategyReport),
            @(FMGSYJContentTypeInvestmentReport),
            @(FMGSYJContentTypeWealthDaily),
            @(FMGSYJContentTypeService)
        ];
    }
}

- (void)setupTableView {
    [self.tableView registerCellClass:[FMGSYJPrivilegeVideoCell class]];
    [self.tableView registerCellClass:[FMGSYJPrivilegeReportCell class]];
    [self.tableView registerCellClass:[FMGSYJPrivilegeServiceCell class]];
    [self.tableView registerCellClass:[FMF10NoDataCell class]];
    [self.tableView registerViewClass:[FMF10TradeMustReadSectionHeader class]];
    
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.estimatedRowHeight = 120;
    self.tableView.rowHeight = UITableViewAutomaticDimension;
    self.tableView.backgroundColor = UIColor.up_contentBgColor;
    FMGSYJChiefInvestmentContactView *view = [FMGSYJChiefInvestmentContactView footerViewWithWidth:UI_SCREEN_WIDTH dakas:self.signDakas];
    self.tableView.tableFooterView = view;
    self.tableView.hidden = YES;
}

#pragma mark - 辅助方法
- (NSArray *)dataArrayForSectionType:(FMGSYJContentType)type {
    switch (type) {
        case FMGSYJContentTypeSmallClassMarket:
            return self.smallClassMarketModels;
        case FMGSYJContentTypeChiefClassroom:
            return self.videoModels;
        case FMGSYJContentTypeStrategyReport:
            return self.strategyReportModels;
        case FMGSYJContentTypeInvestmentReport:
            return self.investmentReportModels;
        case FMGSYJContentTypeMacroEconomy:
            return self.macroeconomicModels;
        case FMGSYJContentTypeWealthDaily:
            return self.wealthReportModels;
        default:
            return @[];
    }
}

#pragma mark - UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.sectionTypes.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    FMGSYJContentType sectionType = [self.sectionTypes[section] integerValue];
    if (sectionType == FMGSYJContentTypeService) {
        return 1;
    }
    NSArray *dataArray = [self dataArrayForSectionType:sectionType];
    return MAX(dataArray.count, 1); // Return 1 if dataArray is empty to show FMF10NoDataCell
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMGSYJContentType sectionType = [self.sectionTypes[indexPath.section] integerValue];
    NSArray *dataArray = [self dataArrayForSectionType:sectionType];

    if (sectionType == FMGSYJContentTypeService) {
        FMGSYJPrivilegeServiceCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass([FMGSYJPrivilegeServiceCell class]) forIndexPath:indexPath];
        [cell configWithServiceItems:self.serviceModels];
        return cell;
    }

    if (dataArray.count == 0) {
        FMF10NoDataCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass([FMF10NoDataCell class]) forIndexPath:indexPath];
        cell.tLabel.font = FontWithSize(12);
        // You can customize the no data message per section type if needed
        // For now, a generic message or specific messages as in FMGSYJChiefInvestmentListViewController.m
        if (sectionType == FMGSYJContentTypeSmallClassMarket) {
            cell.tLabel.text = @"暂无小班池内容";
        } else if (sectionType == FMGSYJContentTypeChiefClassroom) {
            cell.tLabel.text = @"暂无首席课堂内容";
        } else if (sectionType == FMGSYJContentTypeStrategyReport) {
            cell.tLabel.text = @"暂无策略报告内容";
        } else if (sectionType == FMGSYJContentTypeInvestmentReport) {
            cell.tLabel.text = @"暂无投研报告内容";
        } else if (sectionType == FMGSYJContentTypeMacroEconomy) {
            cell.tLabel.text = @"暂无宏观经济内容";
        } else if (sectionType == FMGSYJContentTypeWealthDaily) {
            cell.tLabel.text = @"暂无财富日刊内容";
        } else {
            cell.tLabel.text = @"暂无数据";
        }
        return cell;
    }
    
    // Original cell logic when data exists
    if (sectionType == FMGSYJContentTypeChiefClassroom) {
        FMGSYJPrivilegeVideoCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass([FMGSYJPrivilegeVideoCell class]) forIndexPath:indexPath];
        cell.model = self.videoModels[indexPath.row];
        return cell;
    } else {
        FMGSYJPrivilegeReportCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass([FMGSYJPrivilegeReportCell class]) forIndexPath:indexPath];
        cell.model = dataArray[indexPath.row]; // dataArray is already fetched and checked
        return cell;
    }
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    FMGSYJContentType sectionType = [self.sectionTypes[indexPath.section] integerValue];
    NSArray *dataArray = [self dataArrayForSectionType:sectionType];

    if (indexPath.row >= dataArray.count) {
        // This might happen if FMF10NoDataCell is shown, do nothing or handle as appropriate
        return;
    }

    // 会员中心访问埋点 - 点击具体功能
    MemberCenterFunctionType functionType = [self getFunctionTypeForSectionType:sectionType];

    id model = dataArray[indexPath.row];

    if (sectionType == FMGSYJContentTypeChiefClassroom) {
        if ([model isKindOfClass:[FMGSYJPrivilegeVideoModel class]]) {
            FMGSYJPrivilegeVideoModel *videoModel = (FMGSYJPrivilegeVideoModel *)model;
            FMVIPXLZFDetailVC *vc = [[FMVIPXLZFDetailVC alloc] initWithType:2 contentType:0];
            vc.contentId = videoModel.videoId.integerValue;
            vc.serverType = self.serverType;
            vc.memberCenterFunctionId = MemberCenterFunctionTypeChiefClassroom; // 设置首席课堂功能ID
            [self.navigationController pushViewController:vc animated:YES];
        }
    } else if (sectionType == FMGSYJContentTypeSmallClassMarket ||
               sectionType == FMGSYJContentTypeStrategyReport ||
               sectionType == FMGSYJContentTypeInvestmentReport ||
               sectionType == FMGSYJContentTypeMacroEconomy ||
               sectionType == FMGSYJContentTypeWealthDaily) {
        
        if ([model isKindOfClass:[FMGSYJPrivilegeReportModel class]]) {
            FMGSYJPrivilegeReportModel *reportModel = (FMGSYJPrivilegeReportModel *)model;
            
            if (!reportModel.content || reportModel.content.length == 0) {
                [SVProgressHUD showInfoWithStatus:@"内容为空"];
                NSLog(@"Report content is empty for model: %@", reportModel.title);
                return;
            }

            if (reportModel.contentType == 1) { // HTML content
                [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                    YTGNormalWebVC *vc = [[YTGNormalWebVC alloc] init];
                    NSString *path = self.serverType == JCVIPTypeGSYJDZ ? kAPI_MemberCenter_GSYJDZ_ReportDetail : kAPI_MemberCenter_GSYJ_ReportDetail;
                    vc.startPage = [NSString stringWithFormat:@"%@%@?id=%@",prefix, path, reportModel.reportId];
                    vc.memberCenterFunctionId = functionType; // 设置会员中心功能ID
                    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
                }];
            } else if (reportModel.contentType == 2) { // PDF content
                FMPDFReaderViewController *pdfVC = [[FMPDFReaderViewController alloc] init];
                pdfVC.pdfUrl = reportModel.content;
                pdfVC.title = reportModel.title;
                pdfVC.memberCenterFunctionId = functionType; // 设置会员中心功能ID
                [self.navigationController pushViewController:pdfVC animated:YES];
            } else {
                NSLog(@"Unsupported content type: %zd for report: %@", reportModel.contentType, reportModel.title);
            }
        }
    } else if (sectionType == FMGSYJContentTypeService) {
        // Handle service cell tap if needed
        NSLog(@"Tapped service cell");
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    FMGSYJContentType sectionType = [self.sectionTypes[section] integerValue];
    
    if (sectionType == FMGSYJContentTypeService) {
        return [UIView new]; // 服务区域不需要header
    }
    
    FMF10TradeMustReadSectionHeader *header = [tableView reuseViewClass:[FMF10TradeMustReadSectionHeader class]];
    header.index = section;
    header.title = self.sectionTitles[@(sectionType)];
    
    WEAKSELF
    header.seeMoreBlock = ^(NSInteger index) {
        [__weakSelf seeMoreSection:index];
    };
    
    return header;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    FMGSYJContentType sectionType = [self.sectionTypes[section] integerValue];
    
    if (sectionType == FMGSYJContentTypeService) {
        return CGFLOAT_MIN; // 服务区域不需要header
    }
    
    // 对于其他内容类型的section，无论是否有数据，都显示header
    return 44;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    UIView *footerView = [UIView new];
    footerView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    return footerView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    FMGSYJContentType sectionType = [self.sectionTypes[section] integerValue];
    
    if (sectionType == FMGSYJContentTypeService) {
        return CGFLOAT_MIN;
    }
    
    return 8;
}

#pragma mark - Actions
- (void)seeMoreSection:(NSInteger)section {
    FMGSYJContentType sectionType = [self.sectionTypes[section] integerValue];
    NSString *title = self.sectionTitles[@(sectionType)];
    NSInteger apiType = 0;
    if ([self.sectionApiTypes.allKeys containsObject:@(sectionType)]) { // Check if apiType exists for this sectionType
        apiType = [self.sectionApiTypes[@(sectionType)] integerValue];
    }
    
//    NSLog(@"查看更多 sectionType:%zd apiType:%zd title:%@", sectionType, apiType, title);

    // 会员中心访问埋点 - 点击查看更多
    MemberCenterFunctionType functionType = [self getFunctionTypeForSectionType:sectionType];

    if (sectionType == FMGSYJContentTypeChiefClassroom) {
        FMGSYJPrivilegeVideoListViewController *videoListVC = [[FMGSYJPrivilegeVideoListViewController alloc] init];
        videoListVC.title = title;
        videoListVC.serverType = self.serverType;
        [self.navigationController pushViewController:videoListVC animated:YES];
    } else if (sectionType == FMGSYJContentTypeSmallClassMarket ||
               sectionType == FMGSYJContentTypeStrategyReport ||
               sectionType == FMGSYJContentTypeInvestmentReport ||
               sectionType == FMGSYJContentTypeMacroEconomy ||
               sectionType == FMGSYJContentTypeWealthDaily) {
        FMGSYJPrivilegeReportListViewController *reportListVC = [[FMGSYJPrivilegeReportListViewController alloc] init];
        reportListVC.navTitle = title;
        reportListVC.reportType = apiType; // This is the 'type' for the report API
        reportListVC.serverType = self.serverType;
        [self.navigationController pushViewController:reportListVC animated:YES];
    }
}


#pragma mark - Data Loading
- (void)loadData {
    dispatch_group_t group = dispatch_group_create();
    __block NSInteger totalRequests = 0;
    __block NSInteger successfulRequests = 0;

    [SVProgressHUD show];

    // Helper to make entering group and incrementing totalRequests cleaner
    void (^enterGroupAndIncrementTotal)(void) = ^{
        dispatch_group_enter(group);
        totalRequests++;
    };

    // Helper for completion blocks
    void (^handleCompletion)(BOOL) = ^(BOOL success) {
        if (success) {
            successfulRequests++;
        }
        dispatch_group_leave(group);
    };

    // 加载视频数据
    enterGroupAndIncrementTotal();
    [self loadVideoDataWithCompletion:handleCompletion];

    // 加载策略报告
    enterGroupAndIncrementTotal();
    [self loadStrategyReportDataWithCompletion:handleCompletion];

    // 加载投研报告
    enterGroupAndIncrementTotal();
    [self loadInvestmentReportDataWithCompletion:handleCompletion];

    // 加载财富日刊
    enterGroupAndIncrementTotal();
    [self loadWealthReportDataWithCompletion:handleCompletion];

    if (self.serverType == JCVIPTypeGSYJDZ) {
        // 小班看市
        enterGroupAndIncrementTotal();
        [self loadSmallClassMarketDataWithCompletion:handleCompletion];

        // 宏观经济
        enterGroupAndIncrementTotal();
        [self loadMacroeconomicDataWithCompletion:handleCompletion];
    }

    // 所有请求完成后处理
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        if (totalRequests > 0 && successfulRequests == 0) { // 所有已发起的请求都失败了
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            WEAKSELF
            [self.view showReloadNetworkViewWithBlock:^{
                [__weakSelf loadData];
            }];
        } else { // 至少有一个请求成功，或者没有发起任何请求
            [SVProgressHUD dismiss];
            [self.view dismissNoNetWorkView];
            self.tableView.hidden = NO;
            [self.tableView reloadData];
        }
    });
}

// 通用请求报告数据的方法
- (void)loadReportDataWithType:(NSInteger)type completion:(void(^)(BOOL success, NSArray *models))completion {
    [HttpRequestTool requestGSYJPrivilegeReportsWithPageSize:1
                                                      pageNo:1
                                                        type:type
                                                  serverType:self.serverType
                                                       start:nil
                                                     failure:^{
        if (completion) completion(NO, nil);
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            NSArray *models = [NSArray modelArrayWithClass:[FMGSYJPrivilegeReportModel class] json:dic[@"data"]];
            if (completion) completion(YES, models);
        } else {
            if (completion) completion(NO, nil);
        }
    }];
}

// 加载首席课堂视频数据
- (void)loadVideoDataWithCompletion:(void(^)(BOOL success))completion {
    [HttpRequestTool requestGSYJPrivilegeCourseWithPageSize:1
                                                     pageNo:1
                                                       type:2
                                                 serverType:self.serverType
                                                      start:nil
                                                    failure:^{
        completion(NO);
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.videoModels = [NSArray modelArrayWithClass:[FMGSYJPrivilegeVideoModel class] json:dic[@"data"]];
            completion(YES);
        } else {
            completion(NO);
        }
    }];
}

// 加载策略报告数据
- (void)loadStrategyReportDataWithCompletion:(void(^)(BOOL success))completion {
    [self loadReportDataWithType:1 completion:^(BOOL success, NSArray *models) {
        if (success) self.strategyReportModels = models;
        completion(success);
    }];
}

// 加载投研报告数据
- (void)loadInvestmentReportDataWithCompletion:(void(^)(BOOL success))completion {
    [self loadReportDataWithType:2 completion:^(BOOL success, NSArray *models) {
        if (success) self.investmentReportModels = models;
        completion(success);
    }];
}

// 加载财富日刊数据
- (void)loadWealthReportDataWithCompletion:(void(^)(BOOL success))completion {
    [self loadReportDataWithType:4 completion:^(BOOL success, NSArray *models) {
        if (success) self.wealthReportModels = models;
        completion(success);
    }];
}

// 加载小班看市数据
- (void)loadSmallClassMarketDataWithCompletion:(void(^)(BOOL success))completion {
    [self loadReportDataWithType:8 completion:^(BOOL success, NSArray *models) {
        if (success) self.smallClassMarketModels = models;
        completion(success);
    }];
}

// 加载宏观经济数据
- (void)loadMacroeconomicDataWithCompletion:(void(^)(BOOL success))completion {
    [self loadReportDataWithType:12 completion:^(BOOL success, NSArray *models) {
        if (success) self.macroeconomicModels = models;
        completion(success);
    }];
}

- (NSArray<FMGSYJServiceItemModel *> *)serviceModels {
    if (!_serviceModels) {
        FMGSYJServiceItemModel *model1 = [FMGSYJServiceItemModel itemWithTitle:@"机构观点" desc:@"精选券商研究报告" imageName:@"gsyj_service_1"];
        model1.tapAction = ^{
            [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                YTGNormalWebVC *vc = [[YTGNormalWebVC alloc] init];
                vc.startPage = [NSString stringWithFormat:@"%@%@",prefix, kAPI_MemberCenter_JGGD];
                [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
            }];
        };
//        FMGSYJServiceItemModel *model2 = [FMGSYJServiceItemModel itemWithTitle:@"VIP交易佣金" desc:@"VIP专属佣金费率" imageName:@"gsyj_service_3"];
//        model2.tapAction = ^{
//            ShowConfirm(self, nil, @"您已开通VIP交易佣金，如有疑问或还未给您开通，请联系您的财富管家", @"取消", @"联系管家", nil, ^{
//                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"weixin://"] options:@{} completionHandler:nil];
//            });
//        };
        FMGSYJServiceItemModel *model3 = [FMGSYJServiceItemModel itemWithTitle:@"财富管家" desc:@"提供专业个性化服务" imageName:@"gsyj_service_2"];
        model3.tapAction = ^{
            [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"weixin://"] options:@{} completionHandler:nil];
        };
        if (self.serverType == JCVIPTypeGSYJDZ) {
            _serviceModels = @[model1, model3];
        } else if (self.serverType == JCVIPTypeGSYJ) {
            _serviceModels = @[model3];
        }
    }
    
    return _serviceModels;
}

#pragma mark - Private Methods

/**
 * 根据sectionType获取对应的功能ID
 */
- (MemberCenterFunctionType)getFunctionTypeForSectionType:(FMGSYJContentType)sectionType {
    if (sectionType == FMGSYJContentTypeSmallClassMarket) {
        // 小班看市功能ID
        return MemberCenterFunctionTypeSmallClassMarket;
    } else if (sectionType == FMGSYJContentTypeChiefClassroom) {
        // 首席课堂功能ID
        return MemberCenterFunctionTypeChiefClassroom;
    } else if (sectionType == FMGSYJContentTypeStrategyReport) {
        // 策略报告功能ID
        return MemberCenterFunctionTypeStrategyReport;
    } else if (sectionType == FMGSYJContentTypeInvestmentReport) {
        // 投研报告功能ID
        return MemberCenterFunctionTypeInvestmentReport;
    } else if (sectionType == FMGSYJContentTypeMacroEconomy) {
        // 宏观经济功能ID
        return MemberCenterFunctionTypeMacroEconomy;
    } else if (sectionType == FMGSYJContentTypeWealthDaily) {
        // 财富日刊功能ID
        return MemberCenterFunctionTypeWealthDaily;
    }

    return MemberCenterFunctionTypeNone; // 默认返回无功能ID
}

@end
