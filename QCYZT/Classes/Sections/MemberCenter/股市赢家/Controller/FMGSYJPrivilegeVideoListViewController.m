//
//  FMGSYJPrivilegeVideoListViewController.m
//  QCYZT
//
//  Created by Cursor on 2024/7/24.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMGSYJPrivilegeVideoListViewController.h"
#import "FMGSYJPrivilegeVideoCell.h"
#import "FMGSYJPrivilegeInformationModel.h"
#import "HttpRequestTool+MemberCenter.h"
#import "FMVIPXLZFDetailVC.h"
#import "MemberCenterVisitManager.h"

static NSInteger const kDefaultPageSize = 10;
static NSInteger const kVideoType = 2; // 首席课堂的类型定义

@interface FMGSYJPrivilegeVideoListViewController () <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray<FMGSYJPrivilegeVideoModel *> *dataArr;
@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;

@end

@implementation FMGSYJPrivilegeVideoListViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = @"首席课堂";
    self.view.backgroundColor = UIColor.up_contentBgColor;

    [self setupTableView];

    self.page = 1;
    self.currentPage = self.page;

    [self.tableView.mj_header beginRefreshing];

    // 会员中心访问埋点 - 进入页面
    [[MemberCenterVisitManager sharedManager] trackPageEnter:self functionId:MemberCenterFunctionTypeChiefClassroom];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self configNavWhiteColorWithCloseSEL:@selector(backAction)];
}

- (void)backAction {
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - Setup UI
- (void)setupTableView {
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(UIEdgeInsetsMake(0, 0, UI_SAFEAREA_BOTTOM_HEIGHT, 0));
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMGSYJPrivilegeVideoCell *cell = [tableView reuseCellClass:[FMGSYJPrivilegeVideoCell class]];
    cell.model = self.dataArr[indexPath.row];
    return cell;
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];

    FMVIPXLZFDetailVC *vc = [[FMVIPXLZFDetailVC alloc] initWithType:2 contentType:0];
    vc.contentId = self.dataArr[indexPath.row].videoId.integerValue;
    vc.serverType = self.serverType;
    vc.memberCenterFunctionId = MemberCenterFunctionTypeChiefClassroom; // 设置首席课堂功能ID
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - Data Loading
- (void)requestData {
    WEAKSELF
    [HttpRequestTool requestGSYJPrivilegeCourseWithPageSize:kDefaultPageSize
                                                     pageNo:self.page
                                                       type:kVideoType
                                                 serverType:self.serverType
                                                      start:nil
                                                    failure:^{
        [__weakSelf endRefreshForFailure];
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if (lz_HttpStatusCheck(dic)) {
            [__weakSelf endRefreshForSuccess];

            if (__weakSelf.page == 1) {
                [__weakSelf.tableView.mj_footer resetNoMoreData];
                [__weakSelf.dataArr removeAllObjects];
            }

            NSArray *newDataArray = [NSArray modelArrayWithClass:[FMGSYJPrivilegeVideoModel class] json:dic[@"data"]];
            if (newDataArray.count < kDefaultPageSize) {
                [__weakSelf.tableView.mj_footer endRefreshingWithNoMoreData];
            }
            [__weakSelf.dataArr addObjectsFromArray:newDataArray];
            
            if (!__weakSelf.dataArr.count) {
                [__weakSelf.tableView showNoDataViewWithImage:UPTImgInMarket2Module(@"个股/common_nodata") string:@"暂无首席课堂内容" attributes:nil offsetY:60];
                __weakSelf.tableView.mj_footer.hidden = YES;
            } else {
                [__weakSelf.tableView dismissNoDataView];
                __weakSelf.tableView.mj_footer.hidden = NO;
            }
            
            [__weakSelf.tableView reloadData];
        } else {
            [__weakSelf endRefreshForFailure];
            NSString *errMsg = dic[@"errmessage"] ?: @"数据加载失败";
            [SVProgressHUD showErrorWithStatus:errMsg];
            if (!__weakSelf.dataArr.count) { // 如果当前没有数据，也显示空页面提示
                 [__weakSelf.tableView showNoDataViewWithImage:ImageWithName(@"common_nodata") string:@"暂无首席课堂内容" attributes:nil offsetY:60];
                 __weakSelf.tableView.mj_footer.hidden = YES;
            }
        }
    }];
}

#pragma mark - Refresh Actions
- (void)headerAction {
    self.page = 1;
    [self requestData];
}

- (void)footerAction {
    self.page++;
    [self requestData];
}

- (void)endRefreshForFailure {
    [self.tableView.mj_header endRefreshing];
    [self.tableView.mj_footer endRefreshing];
    self.page = self.currentPage; // 失败时，page恢复到上次成功加载的页码
}

- (void)endRefreshForSuccess {
    [self.tableView.mj_header endRefreshing];
    [self.tableView.mj_footer endRefreshing];
    self.currentPage = self.page; // 成功时，更新当前页码
}

#pragma mark - Getters
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.backgroundColor = UIColor.up_contentBgColor;
        _tableView.separatorInset = UIEdgeInsetsMake(0, 15, 0, 15);
        _tableView.separatorColor = UIColor.fm_sepline_color;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.estimatedRowHeight = 100;
        
        [_tableView registerCellClass:[FMGSYJPrivilegeVideoCell class]];
        
        WEAKSELF
        _tableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            [__weakSelf headerAction];
        }];
        _tableView.mj_footer = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
            [__weakSelf footerAction];
        }];
        _tableView.mj_footer.hidden = YES;
    }
    return _tableView;
}

- (NSMutableArray<FMGSYJPrivilegeVideoModel *> *)dataArr {
    if (!_dataArr) {
        _dataArr = [NSMutableArray array];
    }
    return _dataArr;
}

- (void)dealloc {
    // 会员中心访问埋点 - 退出页面
    [[MemberCenterVisitManager sharedManager] trackPageExit:self functionId:MemberCenterFunctionTypeChiefClassroom];
}

@end
