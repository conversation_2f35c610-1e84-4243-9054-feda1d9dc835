//
//  FMGSYJPrivilegeReportListViewController.m
//  QCYZT
//
//  Created by Cursor on 2024/7/24.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMGSYJPrivilegeReportListViewController.h"
#import "FMGSYJPrivilegeReportCell.h"
#import "FMGSYJPrivilegeInformationModel.h" // Contains FMGSYJPrivilegeReportModel
#import "HttpRequestTool+MemberCenter.h"
#import "FMPDFReaderViewController.h" // For PDF content
#import "YTGNormalWebVC.h"
#import "MemberCenterVisitManager.h"

static NSInteger const kDefaultPageSize = 10;

@interface FMGSYJPrivilegeReportListViewController () <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray<FMGSYJPrivilegeReportModel *> *dataArr;
@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;

@end

@implementation FMGSYJPrivilegeReportListViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = self.navTitle ?: @"报告列表"; // Use navTitle or a default
    self.view.backgroundColor = UIColor.up_contentBgColor;

    [self setupTableView];

    self.page = 1;
    self.currentPage = self.page;

    [self.tableView.mj_header beginRefreshing];

    // 会员中心访问埋点 - 进入页面
    MemberCenterFunctionType functionType = [self getFunctionTypeForReportType:self.reportType];
    if (functionType != MemberCenterFunctionTypeNone) {
        [[MemberCenterVisitManager sharedManager] trackPageEnter:self functionId:functionType];
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self configNavWhiteColorWithCloseSEL:@selector(backAction)];
}

- (void)backAction {
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - Setup UI
- (void)setupTableView {
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(UIEdgeInsetsMake(0, 0, UI_SAFEAREA_BOTTOM_HEIGHT, 0));
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMGSYJPrivilegeReportCell *cell = [tableView reuseCellClass:[FMGSYJPrivilegeReportCell class]];
    cell.model = self.dataArr[indexPath.row];
    return cell;
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    FMGSYJPrivilegeReportModel *model = self.dataArr[indexPath.row];
    
    if (!model.content || model.content.length == 0) {
        [SVProgressHUD showInfoWithStatus:@"内容为空"];
        NSLog(@"Report content is empty for model: %@", model.title);
        return;
    }

    // 获取当前功能类型
    MemberCenterFunctionType functionType = [self getFunctionTypeForReportType:self.reportType];

    if (model.contentType == 1) { // HTML content
        [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
            YTGNormalWebVC *vc = [[YTGNormalWebVC alloc] init];
            NSString *path = self.serverType == JCVIPTypeGSYJDZ ? kAPI_MemberCenter_GSYJDZ_ReportDetail : kAPI_MemberCenter_GSYJ_ReportDetail;
            vc.startPage = [NSString stringWithFormat:@"%@%@?id=%@",prefix, path, model.reportId];
            vc.memberCenterFunctionId = functionType; // 设置会员中心功能ID
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
    } else if (model.contentType == 2) { // PDF content
        FMPDFReaderViewController *pdfVC = [[FMPDFReaderViewController alloc] init];
        pdfVC.pdfUrl = model.content; // This should be the URL string for the PDF
        pdfVC.title = model.title;
        pdfVC.memberCenterFunctionId = functionType; // 设置会员中心功能ID
        [self.navigationController pushViewController:pdfVC animated:YES];
    } else {
        NSLog(@"Unsupported content type: %zd for model: %@", model.contentType, model.title);
    }
}

#pragma mark - Data Loading
- (void)requestData {
    WEAKSELF
    [HttpRequestTool requestGSYJPrivilegeReportsWithPageSize:kDefaultPageSize
                                                      pageNo:self.page
                                                        type:self.reportType // Use the passed reportType
                                                  serverType:self.serverType
                                                       start:nil
                                                     failure:^{
        [__weakSelf endRefreshForFailure];
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if (lz_HttpStatusCheck(dic)) {
            [__weakSelf endRefreshForSuccess];

            if (__weakSelf.page == 1) {
                [__weakSelf.tableView.mj_footer resetNoMoreData];
                [__weakSelf.dataArr removeAllObjects];
            }

            NSArray *newDataArray = [NSArray modelArrayWithClass:[FMGSYJPrivilegeReportModel class] json:dic[@"data"]];
            if (newDataArray.count < kDefaultPageSize) {
                [__weakSelf.tableView.mj_footer endRefreshingWithNoMoreData];
            }
            [__weakSelf.dataArr addObjectsFromArray:newDataArray];
            
            if (!__weakSelf.dataArr.count) {
                NSString *noDataMessage = [NSString stringWithFormat:@"暂无%@内容", __weakSelf.navTitle ?: @"相关"];
                [__weakSelf.tableView showNoDataViewWithImage:UPTImgInMarket2Module(@"个股/common_nodata") string:noDataMessage attributes:nil offsetY:60];
                __weakSelf.tableView.mj_footer.hidden = YES;
            } else {
                [__weakSelf.tableView dismissNoDataView];
                __weakSelf.tableView.mj_footer.hidden = NO;
            }
            
            [__weakSelf.tableView reloadData];
        } else {
            [__weakSelf endRefreshForFailure];
            NSString *errMsg = dic[@"errmessage"] ?: @"数据加载失败";
            [SVProgressHUD showErrorWithStatus:errMsg];
            if (!__weakSelf.dataArr.count) { 
                 NSString *noDataMessage = [NSString stringWithFormat:@"暂无%@内容", __weakSelf.navTitle ?: @"相关"];
                 [__weakSelf.tableView showNoDataViewWithImage:ImageWithName(@"common_nodata") string:noDataMessage attributes:nil offsetY:60];
                 __weakSelf.tableView.mj_footer.hidden = YES;
            }
        }
    }];
}

#pragma mark - Refresh Actions
- (void)headerAction {
    self.page = 1;
    [self requestData];
}

- (void)footerAction {
    self.page++;
    [self requestData];
}

- (void)endRefreshForFailure {
    [self.tableView.mj_header endRefreshing];
    [self.tableView.mj_footer endRefreshing];
    self.page = self.currentPage; 
}

- (void)endRefreshForSuccess {
    [self.tableView.mj_header endRefreshing];
    [self.tableView.mj_footer endRefreshing];
    self.currentPage = self.page; 
}

#pragma mark - Getters
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.backgroundColor = UIColor.up_contentBgColor;
        _tableView.separatorInset = UIEdgeInsetsMake(0, 15, 0, 15);
        _tableView.separatorColor = UIColor.fm_sepline_color;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.estimatedRowHeight = 80; // Adjusted for FMGSYJPrivilegeReportCell
        
        [_tableView registerCellClass:[FMGSYJPrivilegeReportCell class]];
        
        WEAKSELF
        _tableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            [__weakSelf headerAction];
        }];
        _tableView.mj_footer = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
            [__weakSelf footerAction];
        }];
        _tableView.mj_footer.hidden = YES;
    }
    return _tableView;
}

- (NSMutableArray<FMGSYJPrivilegeReportModel *> *)dataArr {
    if (!_dataArr) {
        _dataArr = [NSMutableArray array];
    }
    return _dataArr;
}

- (void)dealloc {
    // 会员中心访问埋点 - 退出页面
    MemberCenterFunctionType functionType = [self getFunctionTypeForReportType:self.reportType];
    if (functionType != MemberCenterFunctionTypeNone) {
        [[MemberCenterVisitManager sharedManager] trackPageExit:self functionId:functionType];
    }
}

#pragma mark - Private Methods

/**
 * 根据reportType获取对应的功能ID
 */
- (MemberCenterFunctionType)getFunctionTypeForReportType:(NSInteger)reportType {
    if (reportType == 1) {
        // 策略报告
        return MemberCenterFunctionTypeStrategyReport;
    } else if (reportType == 8) {
        // 小班看市
        return MemberCenterFunctionTypeSmallClassMarket;
    } else if (reportType == 2) {
        // 投研报告
        return MemberCenterFunctionTypeInvestmentReport;
    } else if (reportType == 4) {
        // 财富日刊
        return MemberCenterFunctionTypeWealthDaily;
    } else if (reportType == 12) {
        // 宏观经济
        return MemberCenterFunctionTypeMacroEconomy;
    }
    
    return MemberCenterFunctionTypeNone; // 默认返回无效类型
}

@end
