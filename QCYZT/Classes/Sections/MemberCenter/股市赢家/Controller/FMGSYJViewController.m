//
//  FMGSYJViewController.m
//  QCYZT
//
//  Created by AI on 2024/7/23.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMGSYJViewController.h"
#import "FMOuterTableView.h"
#import "FMGSYJHeaderView.h"
#import "FMGSYJChiefInvestmentListViewController.h"
#import "FMGSYJStrategyPoolsViewController.h"
#import "FMGSYJPrivilegeViewController.h"
#import "ZLTagView.h"
#import "HttpRequestTool+MemberCenter.h"
#import "FMMemberCenterVIPServiceModel.h"
#import "FMGSYJChiefInvestmentOperationModel.h"
#import "MemberCenterVisitManager.h"

@interface FMGSYJViewController () <UITableViewDelegate, UITableViewDataSource, FMInnerTableVCDelegate>

@property (nonatomic, strong) FMGSYJHeaderView *headerView;
@property (nonatomic, strong) ZLTagView *tagView;
@property (nonatomic, strong) FMOuterTableView *tableView;
@property (nonatomic, strong) UITableViewCell *contentCell;
@property (nonatomic, weak) FMInnerTableViewController *showingVC;
@property (nonatomic, strong) NSArray *titleArray;

@property (nonatomic, strong) NSArray<FMChiefInvestmentSignDakaModel *> *signDakas; // 签约大咖

@end

@implementation FMGSYJViewController

- (void)viewDidLoad {
    [super viewDidLoad];

    self.view.backgroundColor = UIColor.up_contentBgColor;

    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(UIEdgeInsetsMake(0, 0, UI_SAFEAREA_BOTTOM_HEIGHT, 0));
    }];

    [self loadData];

    // 会员中心访问埋点 - 进入页面
    MemberCenterFunctionType functionType = (self.serverType == JCVIPTypeGSYJ) ? MemberCenterFunctionTypeGSYJ : MemberCenterFunctionTypeGSYJCustom;
    [[MemberCenterVisitManager sharedManager] trackPageEnter:self functionId:functionType];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];

    [self configNavWhiteColorWithCloseSEL:@selector(backAction)];
}

- (void)backAction {
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - Setup Methods
- (void)setupChildViewControllers {
    FMGSYJChiefInvestmentListViewController *chiefVC = [[FMGSYJChiefInvestmentListViewController alloc] init];
    chiefVC.serverType = self.serverType;
    chiefVC.signDakas = self.signDakas;
    chiefVC.delegate = self;
    [self addChildViewController:chiefVC];

    FMGSYJStrategyPoolsViewController *strategyVC = [[FMGSYJStrategyPoolsViewController alloc] init];
    strategyVC.serverType = self.serverType;
    strategyVC.signDakas = self.signDakas;
    strategyVC.delegate = self;
    [self addChildViewController:strategyVC];

    FMGSYJPrivilegeViewController *privilegeVC = [[FMGSYJPrivilegeViewController alloc] init];
    privilegeVC.serverType = self.serverType;
    privilegeVC.signDakas = self.signDakas;
    privilegeVC.delegate = self;
    [self addChildViewController:privilegeVC];

    // 默认显示第一个子控制器
    [self switchToChildViewControllerAtIndex:0];
}

#pragma mark - Private Methods

- (void)switchToChildViewControllerAtIndex:(NSInteger)index {
    if (index < 0 || index >= self.childViewControllers.count) {
        return;
    }

    self.showingVC = self.childViewControllers[index];

    [self.contentCell.contentView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self.contentCell.contentView addSubview:self.showingVC.view];
    [self.showingVC.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentCell.contentView);
    }];
}

#pragma mark - UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    return self.contentCell;
}

#pragma mark - UITableViewDelegate
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UI_SCREEN_HEIGHT - UI_SAFEAREA_TOP_HEIGHT - UI_SAFEAREA_BOTTOM_HEIGHT - 63;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return self.tagView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 63;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - UIScrollViewDelegate

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat topCellOffset = [self.tableView rectForSection:0].origin.y;

    // 如果里层tableView的偏移量大于0，将外层tableView的偏移量定在topCellOffset，保持悬停
    if (self.showingVC.tableView.contentOffset.y > 0) {
        self.tableView.contentOffset = CGPointMake(0, topCellOffset);
    }

    // 如果外层tableView偏移量小于topCellOffset（也就是头部视图正在显示），发出通知让每个子tableView的偏移量变成0
    CGFloat offSetY = self.tableView.contentOffset.y;
    if (offSetY < topCellOffset) {
        for (FMInnerTableViewController *VC in self.childViewControllers) {
            VC.tableView.contentOffset = CGPointZero;
        }
    }
}

#pragma mark - FMInnerTableVCDelegate
- (void)innerTableVCTableviewScroll:(UITableView *)innerTableview {
    CGFloat tableTopViewHeight = ([self.tableView rectForSection:0].origin.y);
    // 如果外层tableView偏移量小于tableTopViewHeight（也就是头部视图正在显示），将子tableView的偏移量变成0
    if (self.tableView.contentOffset.y < tableTopViewHeight) {
        innerTableview.contentOffset = CGPointZero;
        innerTableview.showsVerticalScrollIndicator = NO;
    } else {
        innerTableview.showsVerticalScrollIndicator = YES;
    }
}

#pragma mark - Data Loading
- (void)loadData {
    dispatch_group_t group = dispatch_group_create();
    __block BOOL hasError = NO;

    [SVProgressHUD show];

    // 签约大咖
    NSString *type = [NSString stringWithFormat:@"%zd", (long)self.serverType];
    dispatch_group_enter(group);
    [HttpRequestTool requestMemberCenterVIPSignDakaWithType:type serverType:type start:^{
    } failure:^{
        hasError = YES;
        dispatch_group_leave(group);
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.signDakas = [NSArray modelArrayWithClass:[FMChiefInvestmentSignDakaModel class] json:dic[@"data"]];
        }
        dispatch_group_leave(group);
    }];
    
    // 我的VIP信息
    dispatch_group_enter(group);
    [HttpRequestTool requestMyVIPInfoWithStart:^{
    } failure:^{
        hasError = YES;
        dispatch_group_leave(group);
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            NSArray<FMMemberCenterVIPServiceModel *> *models = [NSArray modelArrayWithClass:[FMMemberCenterVIPServiceModel class] json:dic[@"data"]];
            __block NSDate *startDate;
            __block NSDate *expireDate;
            [models enumerateObjectsUsingBlock:^(FMMemberCenterVIPServiceModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                if (obj.serverType == self.serverType) {
                    startDate = [NSDate dateWithTimeIntervalSince1970:obj.startTime / 1000.0];
                    expireDate = [NSDate dateWithTimeIntervalSince1970:obj.expireTime / 1000.0];
                    *stop = YES;
                }
            }];
            
            NSString *time = [NSString stringWithFormat:@"%@至%@", [startDate dateStringWithFormatString:@"yyyy-MM-dd"], [expireDate dateStringWithFormatString:@"yyyy-MM-dd"]];
            [self.headerView setupWithVIPInfo:@{
                @"title": [NSString stringWithFormat:@"您已获得大决策%@VIP特权", self.title],
                @"validity": time
            }];
        }
        
        dispatch_group_leave(group);
    }];
    
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        if (hasError) {
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            
            WEAKSELF
            [self.view showReloadNetworkViewWithBlock:^{
                [__weakSelf loadData];
            }];
        } else {
            [SVProgressHUD dismiss];
            [self.view dismissNoNetWorkView];
                        
            self.tableView.hidden = NO;
            [self.tableView reloadData];
            
            [self setupChildViewControllers];
        }
    });
}

#pragma mark - Getters
- (FMGSYJHeaderView *)headerView {
    if (!_headerView) {
        _headerView = [[FMGSYJHeaderView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 85)];
    }
    return _headerView;
}

- (ZLTagView *)tagView {
    if (!_tagView) {
        _tagView = [[ZLTagView alloc] initWithTitleArr:self.titleArray];
        _tagView.backgroundColor = UIColor.up_contentBgColor;
        _tagView.leftPadding = _tagView.rightPadding = 15;
        _tagView.middlePadding = 15.0f;
        _tagView.topPadding = 15.0f;
        _tagView.labelHeight = 33.0f;
        _tagView.labelWidth = floor((UI_SCREEN_WIDTH - 15 * (self.titleArray.count + 1)) / self.titleArray.count);
        _tagView.tagLabelCornerRadius = 2;
        _tagView.allowsSelection = YES;
        _tagView.allowsMultipleSelection = NO;
        _tagView.singSelectionCanNotInvertSelect = YES;
        _tagView.tagLabelTextColor = UIColor.up_riseColor;
        _tagView.selectedTextColor = UIColor.whiteColor;
        _tagView.tagLabelBgColor = UIColor.up_contentBgColor;
        _tagView.selectedBackgroundColor = UIColor.up_riseColor;
        _tagView.tagLabelBorderColor = UIColor.up_riseColor;
        _tagView.selectedBorderColor = UIColor.up_riseColor;
        _tagView.tagLabelBorderWidth = 1.0f;
        _tagView.tagLabelFont = FontWithSize(15);
        _tagView.selectedTagFont = BoldFontWithSize(15);
        [_tagView.selections addObject:@0]; // 默认选中第一个

        // 设置tagView的点击回调
        WEAKSELF;
        _tagView.didSelectedBlock = ^(NSArray *selections, NSInteger currentSelectedIndex) {
            [__weakSelf switchToChildViewControllerAtIndex:currentSelectedIndex];
        };
    }
    return _tagView;
}

- (FMOuterTableView *)tableView {
    if (!_tableView) {
        _tableView = [[FMOuterTableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.backgroundColor = UIColor.up_contentBgColor;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, CGFLOAT_MIN)];
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.tableHeaderView = self.headerView;
        _tableView.hidden = YES;
    }
    return _tableView;
}

- (UITableViewCell *)contentCell {
    if (!_contentCell) {
        UITableViewCell *cell = [[UITableViewCell alloc] init];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        _contentCell = cell;
    }

    return _contentCell;
}

#pragma mark - Setters
- (void)setServerType:(JCVIPType)serverType {
    _serverType = serverType;
    
    if (serverType == JCVIPTypeGSYJ) {
        self.title = @"股市赢家";
        self.titleArray = @[@"首席跟投", @"策略池", @"专享特权"];
    } else if (serverType == JCVIPTypeGSYJDZ) {
        self.title = @"股市赢家定制版";
        self.titleArray = @[@"定制跟投", @"小班池", @"专享特权"];
    }
}

- (void)dealloc {
    // 会员中心访问埋点 - 退出页面
    MemberCenterFunctionType functionType = (self.serverType == JCVIPTypeGSYJ) ? MemberCenterFunctionTypeGSYJ : MemberCenterFunctionTypeGSYJCustom;
    [[MemberCenterVisitManager sharedManager] trackPageExit:self functionId:functionType];
}

@end
