//
//  FMMemberCenterVIPJCSubViewController.m
//  QCYZT
//
//  Created by zeng on 2024/8/12.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMMemberCenterVIPJCSubViewController.h"
#import "BRDatePickerView.h"
#import "FMMemberCenterVIPJCSubVCTableHeader.h"
#import "FMMemberCenterVIPJCSubVCSectionHeader.h"
#import "FMMemberCenterVIPJCStockPoolModel.h"
#import "FMMemberCenterJCNoDataCell.h"
#import "FMMemberCenterVIPJCSubVCTableCell.h"
#import "HttpRequestTool+MemberCenter.h"

@interface FMMemberCenterVIPJCSubViewController ()<UITableViewDelegate, UITableViewDataSource, FMMemberCenterVIPJCSubVCSectionHeaderDelegate>

@property (nonatomic, strong) UIView *chooseDateView;
@property (nonatomic, strong) UIButton *preDayBtn;
@property (nonatomic, strong) UIButton *chooseDateBtn;
@property (nonatomic, strong) UIButton *nextDayBtn;
@property (nonatomic, strong) BRDatePickerView *datePicker;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) FMMemberCenterVIPJCSubVCTableHeader *tableHeaderView;
@property (nonatomic, strong) FMMemberCenterVIPJCSubVCSectionHeader *sectionHeader;
@property (nonatomic, strong) UIView *tableFooterView;

@property (nonatomic, strong) NSDate *choosedDate;
@property (nonatomic, assign) SortType sortType;             // 当前排序依据
@property (nonatomic, copy)   NSString *sortKey;             // 当前排序key
@property (nonatomic, strong) NSArray <FMMemberCenterVIPJCStockPoolModel *> *requestDatas; // 请求获取的数据
@property (nonatomic, strong) NSArray <FMMemberCenterVIPJCStockPoolModel *> *showDatas; // 请求获取的数据
@property (nonatomic, strong) NSString *currentCapitalName;
@property (nonatomic, strong) NSArray<NSDictionary *> *capitalBadges;

//// 行情数据相关
@property (nonatomic, strong) NSMutableArray<UPMarketCodeMatchInfo *> *stockArray;      // 所有相关股票数据
@property (nonatomic, strong) NSDictionary *stockhqCache;                        // stockHq缓存
@property (nonatomic, strong) UPMarketMonitor *monitor;                          // 请求数据定时器
@property (nonatomic, assign) BOOL isShowing;                                    // 当前页面是否正在显示

@end

@implementation FMMemberCenterVIPJCSubViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = UIColor.up_contentBgColor;

    [self.view addSubview:self.chooseDateView];
    [self.chooseDateView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.top.equalTo(0);
        make.height.equalTo(25);
    }];

    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.chooseDateView.mas_bottom);
        make.left.right.bottom.equalTo(0);
    }];

    if (self.poolType != JCStockPoolTypeLongHuZhuaYao) {
        self.choosedDate = [NSDate date];
        [self changeDayBtnShow];
    }

    self.tableView.hidden = YES;
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];

    self.isShowing = YES;
    if (self.isViewLoaded) {
        [self requestData];
    }
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    self.isShowing = NO;
    [self.monitor stopMonitor];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];

    [self.preDayBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5];
    [self.nextDayBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageRight imageTitleSpacing:5];
}

#pragma mark - UITableViewDelegate/DataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.showDatas.count ? self.showDatas.count : 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.showDatas.count) {
        FMMemberCenterVIPJCSubVCTableCell *cell = [tableView reuseCellClass:[FMMemberCenterVIPJCSubVCTableCell class]];
        cell.model = self.showDatas[indexPath.row];
        cell.isFirstCell = (indexPath.row == 0);
        // 设置股票列表和当前索引，支持左右滑动切换
        cell.stockList = self.showDatas;
        cell.currentIndex = indexPath.row;
        cell.refreshBlock = ^{
            [self.tableView reloadData];
        };
        return cell;;
    }

    FMMemberCenterJCNoDataCell *cell = [tableView reuseCellClass:[FMMemberCenterJCNoDataCell class]];
    if (self.poolType == JCStockPoolTypeLongHuZhuaYao) {
        ZLTagView *tagView = self.sectionHeader.tagView;
        NSInteger index = [tagView.selections.firstObject integerValue];
        if (![tagView.titleArray[index] isEqualToString:@"全部"]) {
            cell.titleLabel.text = @"暂无数据";
        } else {
            cell.titleLabel.text = @"股池的支撑数据尚未更新，可先查看其他日期";
        }
    } else {
        cell.titleLabel.text = @"股池的支撑数据尚未更新，可先查看其他日期";
    }
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.showDatas.count) {
        return UITableViewAutomaticDimension ;
    }

    return 300;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    self.sectionHeader.capitalBadges = self.capitalBadges;
    self.sectionHeader.poolType = self.poolType;
    return self.sectionHeader;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    self.sectionHeader.poolType = self.poolType;
    CGFloat headerHeight = [self.sectionHeader systemLayoutSizeFittingSize:UILayoutFittingCompressedSize].height + 1;
    return headerHeight;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - FMMemberCenterVIPJCSubVCSectionHeaderDelegate
- (void)didSelectColumnWithIndex:(NSInteger)index sortType:(SortType)sortType {
    NSArray *sortKeyArr = @[@"signalLevel", @"nowPrice", @"changeRatio"];
    NSString *sortKey = @"";
    if (sortKeyArr.count > index) {
        sortKey = sortKeyArr[index];
    }
    self.sortType = sortType;
    if (sortType == SortTypeNone) {
        sortKey = @"";
    }
    self.sortKey = sortKey;
    [self sortData];
}

- (void)chooseCaptialName:(NSString *)capitalName {
    self.currentCapitalName = capitalName;

    [self requestData];
}

#pragma mark - HTTP
- (void)requestData {
    if (self.poolType == JCStockPoolTypeJiaZhiLongTou) {
        [self requestJZLT];
    } else if (self.poolType == JCStockPoolTypeLongHuZhuaYao) {
        [self requestLHZY];
    } else {
        [self requestJCQL];
    }
}

- (void)requestJCQL {
    [HttpRequestTool requestMemberCenterJCQLWithDay:[self.choosedDate dateStringWithFormatString:@"yyyy-MM-dd"] type:self.poolType start:^{
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        [self endRefresh];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self dealDataWithDic:dic];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            [self dealDataWithDic:nil];
        }
        [self endRefresh];
    }];
}

// 价值龙头
- (void)requestJZLT {
    [HttpRequestTool requestMemberCenterJZLTWithDay:[self.choosedDate dateStringWithFormatString:@"yyyy-MM-dd"] start:^{
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        [self endRefresh];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self dealDataWithDic:dic];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            [self dealDataWithDic:nil];
        }
        [self endRefresh];
    }];
}

// 龙虎抓妖
- (void)requestLHZYBadges {
    [HttpRequestTool requestMemberCenterLHZYBadgesWithDay:[self.choosedDate dateStringWithFormatString:@"yyyy-MM-dd"] start:^{
    } failure:^{
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.capitalBadges = dic[@"data"];
            [self.tableView reloadData];
        }
    }];
}

- (void)requestLHZY {
    NSString *capitalName = self.currentCapitalName;
    if ([capitalName isEqualToString:@"全部"]) {
        capitalName = @"";
    }

    [HttpRequestTool requestMemberCenterLHZYWithDay:[self.choosedDate dateStringWithFormatString:@"yyyy-MM-dd"] capitalModel:capitalName start:^{
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        [self endRefresh];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self dealDataWithDic:dic];
            [self requestLHZYBadges];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            [self dealDataWithDic:nil];
        }
        [self endRefresh];
    }];
}

- (void)endRefresh {
    [self.tableView.mj_header endRefreshing];
}

// 处理数据
- (void)dealDataWithDic:(NSDictionary *)dic {
    NSArray *tmpArr = [NSArray modelArrayWithClass:[FMMemberCenterVIPJCStockPoolModel class] json:dic[@"data"]];
    if (self.poolType == JCStockPoolTypeLongHuZhuaYao) {
        if (tmpArr.count) {
            FMMemberCenterVIPJCStockPoolModel *model = tmpArr.firstObject;
            NSDate *date = [NSString dateFromFormatedString:model.day format:@"yyyy-MM-dd"];
            self.choosedDate = date;
            [self changeDayBtnShow];
        } else {
            if (!self.choosedDate) {
                self.choosedDate = [NSDate date];
                [self changeDayBtnShow];
            }
        }
    }

    [self.stockArray removeAllObjects];
    for (FMMemberCenterVIPJCStockPoolModel *model in tmpArr) {
        if (self.poolType != JCStockPoolTypeLongHuZhuaYao) {
            for (FMMemberCenterVIPJCStockPoolModel *m in self.showDatas) {
                if ([model.stockCode isEqualToString:m.stockCode]) {
                    model.unfold = m.unfold;
                    break;
                }
            }
        }
        model.dateString = [self.choosedDate dateStringWithFormatString:@"yyyy-MM-dd"];
        model.poolType = self.poolType;

        UPMarketCodeMatchInfo *info = [FMUPDataTool matchInfoWithSetCodeAndCode:model.stockCode];
        if (info) {
            [self.stockArray addObject:info];
        }
    }
    self.requestDatas = tmpArr;
    [self sortData];

    dispatch_async(dispatch_get_main_queue(), ^{
        self.tableView.hidden = NO;
        [self requestStockHq];
    });
}

// 请求行情数据
- (void)requestStockHq {
//    FMLog(@"想请求%zd条数据--%@", self.stockArray.count, [[self.stockArray valueForKeyPath:@"name"] componentsJoinedByString:@","]);
    if (self.stockArray.count) {
        NSMutableArray<UPHqStockUnique *> *stocksM = [NSMutableArray array];
        NSMutableDictionary *stockDic = self.stockhqCache.mutableCopy ?: [NSMutableDictionary new];
        [self.stockArray enumerateObjectsUsingBlock:^(UPMarketCodeMatchInfo *obj, NSUInteger idx, BOOL *_Nonnull stop) {
            UPHqStockUnique *stock = [UPHqStockUnique new];
            stock.setCode = obj.setCode;
            stock.code = obj.code;
            [stocksM addObject:stock];

            NSString *key = [FMUPDataTool oldDataWithNewSetCodeAndCode:[FMUPDataTool jointWithSetCode:obj.setCode code:obj.code]];
            if (!stockDic[key]) { // 可能存在拉不到行情的股票，先从从码表中获取基础信息
                UPMarketCodeMatchInfo *codeInfo = [UPMarketManager queryStockWithSetCode:stock.setCode code:stock.code];
                if (codeInfo) {
                    UPHqStockHq *stockHq = [UPHqStockHq new];
                    stockHq.setCode = codeInfo.setCode;
                    stockHq.code = codeInfo.code;
                    stockHq.name = codeInfo.name;
                    stockHq.category = codeInfo.category;
                    stockHq.origCategory = codeInfo.origCategory;
                    stockHq.precise = codeInfo.precise;
                    stockHq.tradeStatus = codeInfo.status;
                    stockDic[key] = stockHq;
                }
            }
        }];
        // 先触发一下set方法
        self.stockhqCache = stockDic.copy;

        if (UPTAFNetworkReachable && self.isShowing) { // 防止切到别的页面后收到通知或者滚动事件未停止触发请求
            UPMarketOptStockHqReq *hqReq = [[UPMarketOptStockHqReq alloc] initWithStockArray:stocksM.copy];
            hqReq.simpleData = YES;
            WeakSelf(weakSelf);

            [self.monitor startMonitorOptStockHq:hqReq tag:FMMemberCenterStockPoolTag completionHandler:^(UPMarketOptStockHqRsp *rsp, NSError *error) {
                NSArray *tmpArr = [rsp.dataArray valueForKeyPath:@"name"];
//                FMLog(@"筛选--请求到%zd条数据--%@", rsp.dataArray.count, [tmpArr componentsJoinedByString:@","]);
                if (IsValidateArray(rsp.dataArray)) {
                    NSMutableDictionary *cacheDic = weakSelf.stockhqCache.mutableCopy;
                    [rsp.dataArray enumerateObjectsUsingBlock:^(UPHqStockHq *_Nonnull obj,
                                                                NSUInteger idx,
                                                                BOOL *_Nonnull stop) {
                        NSString *key = [FMUPDataTool oldDataWithNewSetCodeAndCode:[FMUPDataTool jointWithSetCode:obj.setCode code:obj.code]];
                        cacheDic[key] = obj;
                    }];
                    // 再次触发set方法
                    weakSelf.stockhqCache = cacheDic.copy;
                }
            }];
        }
    } else {
        [self.monitor stopMonitor];
    }
}

#pragma mark - Private
- (void)sortData {
    self.showDatas = self.requestDatas;
    if (self.sortKey.length && self.sortType != SortTypeNone) {
        self.showDatas = [FMHelper sortedDatas:self.requestDatas withKey:self.sortKey ascending:self.sortType == SortTypeAscending];
    }

    [self.tableView reloadData];
}

- (void)changeDayBtnShow {
    [self.chooseDateBtn setTitle:[self.choosedDate dateStringWithFormatString:@"yyyy年M月d日"] forState:UIControlStateNormal];

    NSString *currentDay = [[NSDate date] dateStringWithFormatString:@"yyyy-MM-dd"];
    NSString *firstDay = @"1990-01-01";
    NSString *choosedDateString = [self.choosedDate dateStringWithFormatString:@"yyyy-MM-dd"];
    if ([choosedDateString isEqualToString:currentDay]) {
        self.nextDayBtn.enabled = NO;
        [self.tableHeaderView configPoolType:self.poolType isSameDay:YES];
    } else {
        self.nextDayBtn.enabled = YES;
        [self.tableHeaderView configPoolType:self.poolType isSameDay:NO];
    }

    if ([choosedDateString isEqualToString:firstDay]) {
        self.preDayBtn.enabled = NO;
    } else {
        self.preDayBtn.enabled = YES;
    }
}

- (NSDate *)dateByAddingDays:(NSInteger)days toDate:(NSDate *)originalDate {
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *dateComponents = [[NSDateComponents alloc] init];
    [dateComponents setDay:days];

    NSDate *newDate = [calendar dateByAddingComponents:dateComponents toDate:originalDate options:0];
    return newDate;
}

#pragma mark - Event
- (void)chooseDateBtnClicked:(UIButton *)btn {
    self.datePicker.selectDate = self.choosedDate;
    [self.datePicker show];
}

- (void)preDayBtnClicked:(UIButton *)btn {
    self.choosedDate = [self dateByAddingDays:-1 toDate:self.choosedDate];
    [self changeDayBtnShow];
    [self requestData];
}

- (void)nextDayBtnClicked:(UIButton *)btn {
    self.choosedDate = [self dateByAddingDays:1 toDate:self.choosedDate];
    [self changeDayBtnShow];
    [self requestData];
}

- (void)contactKF {
    NSString *str = [NSString stringWithFormat:@"tel://%@", [FMUserDefault getSeting:AppInit_ContactUs_Phone]];
    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str] options:@{} completionHandler:nil];
}

#pragma mark - Getter/Setter
- (UIView *)chooseDateView {
    if (!_chooseDateView) {
        _chooseDateView = [UIView new];
        _chooseDateView.backgroundColor = UIColor.up_contentBgColor;

        [_chooseDateView addSubview:self.preDayBtn];
        [self.preDayBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.centerY.equalTo(0);
            make.width.equalTo(52);
        }];

        [_chooseDateView addSubview:self.nextDayBtn];
        [self.nextDayBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.centerY.equalTo(0);
            make.width.equalTo(52);
        }];

        [_chooseDateView addSubview:self.chooseDateBtn];
        [self.chooseDateBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.top.equalTo(0);
        }];
    }

    return _chooseDateView;
}

- (UIButton *)preDayBtn {
    if (!_preDayBtn) {
        _preDayBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(13) normalTextColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor title:@"上一日" image:FMImgInBundle(@"行情/日历左箭头") target:self action:@selector(preDayBtnClicked:)];
        [_preDayBtn setTitleColor:UIColor.fm_stock_calendar_textDisabledColor forState:UIControlStateDisabled];
        [_preDayBtn setImage:FMImgInBundle(@"行情/日历左箭头禁用") forState:UIControlStateDisabled];
    }

    return _preDayBtn;
}

- (UIButton *)nextDayBtn {
    if (!_nextDayBtn) {
        _nextDayBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(13) normalTextColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor title:@"下一日" image:FMImgInBundle(@"行情/日历右箭头") target:self action:@selector(nextDayBtnClicked:)];
        [_nextDayBtn setImage:FMImgInBundle(@"行情/日历右箭头禁用") forState:UIControlStateDisabled];
        [_nextDayBtn setTitleColor:UIColor.fm_stock_calendar_textDisabledColor forState:UIControlStateDisabled];
    }

    return _nextDayBtn;
}

- (UIButton *)chooseDateBtn {
    if (!_chooseDateBtn) {
        _chooseDateBtn = [[UIButton alloc] initWithFrame:CGRectZero font:BoldFontWithSize(15) normalTextColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor title:nil image:nil target:self action:@selector(chooseDateBtnClicked:)];;
    }

    return _chooseDateBtn;
}

- (BRDatePickerView *)datePicker {
    if (!_datePicker) {
        _datePicker = [BRDatePickerView new];
        _datePicker.pickerMode = BRDatePickerModeYMD;
        _datePicker.minDate = [NSDate br_setYear:1990 month:1 day:1];
        _datePicker.maxDate = [NSDate date];
        _datePicker.isAutoSelect = NO;
        WEAKSELF
        _datePicker.resultBlock = ^(NSDate *selectDate, NSString *selectValue) {
            __weakSelf.choosedDate = selectDate;
            [__weakSelf changeDayBtnShow];
            [__weakSelf requestData];
        };
    }

    return _datePicker;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self headerTarget:self headerAction:@selector(requestData)];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_tableView registerCellClass:[FMMemberCenterVIPJCSubVCTableCell class]];
        [_tableView registerCellClass:[FMMemberCenterJCNoDataCell class]];
        _tableView.estimatedRowHeight = 100.0f;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.tableFooterView = self.tableFooterView;
        _tableView.tableHeaderView = self.tableHeaderView;
        _tableView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        _tableView.bounces = YES; // 允许弹性滚动，以支持下拉刷新
    }

    return _tableView;
}

- (UIView *)tableFooterView {
    if (!_tableFooterView) {
        _tableFooterView = [UIView new];
        _tableFooterView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;

        UILabel *reminderLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:UIColor.up_textSecondary2Color backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
        [_tableFooterView addSubview:reminderLabel];
        [reminderLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(15);
            make.right.equalTo(-15);
            make.top.equalTo(10);
        }];
        NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:@"温馨提示：投资有风险，入市需谨慎！入选标的，仅供当日参考，投资者自主决策，自负盈亏。所有推送标的，短期若有浮盈，稳健者可考虑落袋为安，激进者可减仓逐步止盈。\n免责声明：以上信息内容，均来源于市场公开信息和报道，我们力求信息内容客观、公正，但不保证准确性、完整性及在任何市场环境下长期不变。所述观点、建议，仅供参考和学习，投资者不能将其作为投资决策的唯一依据，并应自主决策，独立承担风险，我公司不会作出任何保本或收益承诺。投资有风险，入市需谨慎！四川大决策证券投资顾问有限公司，经营证券期货业务许可证号: 915101067130530143"];
        attrStr.yy_lineSpacing = 3.0f;
        attrStr.yy_paragraphSpacing = 5.0f;
        reminderLabel.attributedText = attrStr;

        UILabel *lxkfLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x666666) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        [_tableFooterView addSubview:lxkfLabel];
        [lxkfLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(reminderLabel.mas_bottom).offset(15);
            make.left.equalTo(reminderLabel);
        }];
        lxkfLabel.text = [NSString stringWithFormat:@"大决策客服电话：%@", [FMUserDefault getSeting:AppInit_ContactUs_Phone]];

        UIButton *btn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(12) normalTextColor:UIColor.up_riseColor backgroundColor:FMClearColor title:@"联系客服" image:nil target:self action:@selector(contactKF)];
        [_tableFooterView addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(-15);
            make.centerY.equalTo(lxkfLabel);
            make.width.equalTo(70);
            make.height.equalTo(25);
            make.bottom.equalTo(-20-UI_SAFEAREA_BOTTOM_HEIGHT);
        }];
        UI_View_BorderRadius(btn, 12.5, 1, UIColor.up_riseColor);

        // 设置临时宽度并触发布局
        _tableFooterView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, 0);
        [_tableFooterView layoutIfNeeded];
        CGFloat height = [_tableFooterView systemLayoutSizeFittingSize:UILayoutFittingCompressedSize].height;
        _tableFooterView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, height);
    }

    return _tableFooterView;
}

- (FMMemberCenterVIPJCSubVCTableHeader *)tableHeaderView {
    if (!_tableHeaderView) {
        _tableHeaderView = [[FMMemberCenterVIPJCSubVCTableHeader alloc] init];
    }

    return _tableHeaderView;
}

- (FMMemberCenterVIPJCSubVCSectionHeader *)sectionHeader {
    if (!_sectionHeader) {
        _sectionHeader = [[FMMemberCenterVIPJCSubVCSectionHeader alloc] init];
        _sectionHeader.delegate = self;
    }

    return _sectionHeader;
}

- (UPMarketMonitor *)monitor {
    if (!_monitor) {
        _monitor = [UPMarketMonitor monitorWithInterval:10];
    }

    return _monitor;
}

- (NSMutableArray<UPMarketCodeMatchInfo *> *)stockArray {
    if (!_stockArray) {
        _stockArray = [NSMutableArray array];
    }

    return _stockArray;
}

- (void)setStockhqCache:(NSDictionary *)stockhqCache {
    _stockhqCache = stockhqCache;

    for (FMMemberCenterVIPJCStockPoolModel *model in self.requestDatas) {
        UPHqStockHq *hq = stockhqCache[model.stockCode];

        model.tradeStatus = hq.tradeStatus;
        model.yClosePrice = hq.yClosePrice;
        model.nowPrice = hq.nowPrice;
        model.changeRatio = hq.changeRatio;

        if ([hq isStockOfKCB]) {
            model.typeStr = @"科创";
        } else if ([hq isStockOfCYB]) {
            model.typeStr = @"创";
        }
    }

    [self sortData];
}


@end
