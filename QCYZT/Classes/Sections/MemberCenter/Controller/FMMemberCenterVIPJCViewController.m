//
//  FMMemberCenterVIPJCViewController.m
//  QCYZT
//
//  Created by zeng on 2024/8/12.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMMemberCenterVIPJCViewController.h"
#import "FMMemberCenterVIPJCSubViewController.h"
#import "FMVIPXYViewController.h"
#import "MAScreenShieldView.h"

@interface FMMemberCenterVIPJCViewController ()<SGPageContentCollectionViewDelegate>

@property (nonatomic, strong) ZLTagView *tagView;
@property (nonatomic, strong) SGPageContentCollectionView *pageContentCollectionView;

@property (nonatomic, strong) UIViewController *showingVC;

@property (nonatomic, assign) JCVIPType jcType;            // 决策type
@property (nonatomic, assign) JCStockPoolType poolType; // 股票池type

@end

@implementation FMMemberCenterVIPJCViewController

- (void)loadView {
    if (@available(iOS 13.2, *)) {
        MAScreenShieldView *view = [MAScreenShieldView creactWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIScreen mainScreen].bounds.size.height)];
        view.banScreenshot = ![[FMUserDefault getUnArchiverDataForKey:App_User_CanScreenshot] boolValue];
        self.view = view;
    }else{
        [super loadView];
    }
    
    self.view.userInteractionEnabled = YES;
    self.view.backgroundColor = UIColor.up_contentBgColor;
}

- (instancetype)initWithType:(JCVIPType)type poolType:(JCStockPoolType)poolType {
    self = [super init];
    if (self) {
        self.poolType = poolType;
        self.jcType = type;
    }
    return self;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self configNavRedColor];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self.view addSubview:self.tagView];
    [self.tagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(0);
        make.height.equalTo(55);
    }];
    
    [self.view addSubview:self.pageContentCollectionView];
    [self.pageContentCollectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(0);
        make.top.equalTo(self.tagView.mas_bottom);
        make.bottom.equalTo(0);
    }];
    [self.pageContentCollectionView setNeedsLayout];
    [self.pageContentCollectionView layoutIfNeeded];
    
    NSInteger index = 0;
    if (self.poolType >= JCStockPoolTypeJiaZhiLongTou) {
        index = self.poolType - JCStockPoolTypeJiaZhiLongTou;
    } else if (self.poolType >= JCStockPoolTypeLongTouQiSheng) {
        index = self.poolType - JCStockPoolTypeLongTouQiSheng;
    }
    [self.pageContentCollectionView setPageContentCollectionViewCurrentIndex:index];
    self.showingVC = self.childViewControllers[index];
    
    // 处理侧滑返回失效
    [self.pageContentCollectionView.collectionView.panGestureRecognizer requireGestureRecognizerToFail:self.navigationController.interactivePopGestureRecognizer];
}

#pragma mark - SGPageContentCollectionViewDelegate
- (void)pageContentCollectionView:(SGPageContentCollectionView *)pageContentCollectionView progress:(CGFloat)progress originalIndex:(NSInteger)originalIndex targetIndex:(NSInteger)targetIndex {
    if (progress >= 1) {
        [self.tagView.selections removeAllObjects];
        [self.tagView.selections addObject:@(targetIndex)];
        [self.tagView setNeedsLayout];
        [self.tagView layoutIfNeeded];
    }
}

#pragma mark - Private
- (NSArray *)addChildVC {
    for (NSInteger i = 0; i < self.tagView.titleArray.count - 1; i++) {
        FMMemberCenterVIPJCSubViewController *vc = [[FMMemberCenterVIPJCSubViewController alloc] init];
        if (self.jcType == JCVIPTypeJCQL) {
            vc.poolType = i + JCStockPoolTypeLongTouQiSheng;
        } else {
            vc.poolType = i + JCStockPoolTypeJiaZhiLongTou;
        }
        [self addChildViewController:vc];
    }
    
    NSInteger poolType;
    if (self.jcType == JCVIPTypeJCQL) {
        poolType = JCStockPoolTypeLongTouMiXun;
    } else {
        poolType = JCStockPoolTypeDaShiTouYan;
    }
    FMVIPXYViewController *vipVC = [[FMVIPXYViewController alloc] initWithPoolType:poolType];
    [self addChildViewController:vipVC];
    
    return self.childViewControllers;
}

#pragma mark - Getter/Setter
- (void)setJcType:(JCVIPType)jcType {
    _jcType = jcType;
    
    if (jcType == JCVIPTypeJCDJ) {
        self.title = @"决策点金";
        self.tagView.titleArray = @[@"价值龙头", @"龙虎抓妖", @"大师投研"];
        if (self.poolType >= JCStockPoolTypeJiaZhiLongTou) {
            [self.tagView.selections addObject:@(self.poolType - JCStockPoolTypeJiaZhiLongTou)];
        } else {
            [self.tagView.selections addObject:@(0)];
        }
    } else {
        self.title = @"决策擒龙";
        self.tagView.titleArray = @[@"龙头启升", @"龙头再涨", @"龙头因子", @"龙头密训"];
        if (self.poolType >= JCStockPoolTypeLongTouQiSheng) {
            [self.tagView.selections addObject:@(self.poolType - JCStockPoolTypeLongTouQiSheng)];
        } else {
            [self.tagView.selections addObject:@(0)];
        }
    }
    self.tagView.labelWidth = floor((UI_SCREEN_WIDTH - 30 - (self.tagView.titleArray.count - 1) * 10) / (self.tagView.titleArray.count));
}

- (SGPageContentCollectionView *)pageContentCollectionView {
    if (!_pageContentCollectionView) {
        _pageContentCollectionView = [[SGPageContentCollectionView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 0) parentVC:self childVCs:[self addChildVC]];
        _pageContentCollectionView.delegatePageContentCollectionView = self;
    }
    
    return _pageContentCollectionView;
}

- (ZLTagView *)tagView {
    if (!_tagView) {
        _tagView = [[ZLTagView alloc] init];
        _tagView.backgroundColor = UIColor.up_contentBgColor;
        _tagView.leftPadding = _tagView.rightPadding = 15.0f;
        _tagView.middlePadding = 10.0f;
        _tagView.topPadding = 15.0;
        _tagView.labelHeight = 33;
        _tagView.tagLabelCornerRadius = 2;
        _tagView.allowsSelection = YES;
        _tagView.allowsMultipleSelection = NO;
        _tagView.singSelectionCanNotInvertSelect = YES;
        _tagView.tagLabelFont = FontWithSize(15);
        _tagView.selectedTagFont = BoldFontWithSize(15);
        _tagView.tagLabelTextColor = UIColor.up_riseColor;
        _tagView.selectedTextColor = FMWhiteColor;
        _tagView.tagLabelBgColor = UIColor.up_contentBgColor;
        _tagView.selectedBackgroundColor = UIColor.up_riseColor;
        _tagView.tagLabelBorderColor = UIColor.up_riseColor;
        _tagView.selectedBorderColor = UIColor.up_riseColor;
        _tagView.tagLabelBorderWidth = 1.0;
        _tagView.singSelectionCanNotInvertSelect = YES;
        _tagView.numberofLines = 1;
        WEAKSELF
        _tagView.didSelectedBlock = ^(NSArray *selections, NSInteger currentSelectedIndex) {
            [__weakSelf.pageContentCollectionView setPageContentCollectionViewCurrentIndex:currentSelectedIndex];
            
            [__weakSelf.showingVC viewDidDisappear:NO];
            __weakSelf.showingVC = __weakSelf.childViewControllers[currentSelectedIndex];
            if (__weakSelf.showingVC.isViewLoaded) {
                [__weakSelf.showingVC viewDidAppear:NO];
            }
        };
    }
    
    return _tagView;
}


@end
