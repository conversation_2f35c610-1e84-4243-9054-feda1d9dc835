//
//  FMMemberCenterProductPaySuccessViewController.m
//  QCYZT
//
//  Created by zeng on 2022/6/23.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMMemberCenterProductPaySuccessViewController.h"
#import "FMMemberCenterExitSignPopView.h"
#import "YTGOtherWebVC.h"

@interface FMMemberCenterProductPaySuccessViewController ()


@end

@implementation FMMemberCenterProductPaySuccessViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = FMWhiteColor;
    // Do any additional setup after loading the view.    
    UIImageView *imgV = [[UIImageView alloc] init];
    [self.view addSubview:imgV];
    [imgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(@90);
        make.width.height.equalTo(@60);
    }];
    imgV.image = ImageWithName(@"MemberCenter_ProductPaySuccess");
    
    UILabel *successLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(18) textColor:ColorWithHex(0x333333) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [self.view addSubview:successLabel];
    [successLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(imgV.mas_bottom).offset(15);
        make.height.equalTo(@25);
    }];
    successLabel.text = self.successStr;
    
    UILabel *descLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:ColorWithHex(0x666666) backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentCenter];
    [self.view addSubview:descLabel];
    [descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(successLabel.mas_bottom).offset(10);
        make.left.equalTo(@27.5);
    }];
    descLabel.attributedText = self.descAttrStr;
    
    UIButton *signBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(16) normalTextColor:FMWhiteColor backgroundColor:FMNavColor title:@"开始签约" image:nil target:self action:@selector(signBtnClicked)];
    [self.view addSubview:signBtn];
    [signBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(descLabel.mas_bottom).offset(60);
        make.left.equalTo(@37.5);
        make.height.equalTo(@45);
    }];
    UI_View_Radius(signBtn, 22.5);
    
    UIButton *noSignBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(16) normalTextColor:ColorWithHex(0x666666) backgroundColor:FMWhiteColor title:@"暂不签约" image:nil target:self action:@selector(noSignBtnClicked)];
    [self.view addSubview:noSignBtn];
    [noSignBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(signBtn.mas_bottom).offset(15);
        make.left.equalTo(@37.5);
        make.height.equalTo(@45);
    }];
    UI_View_BorderRadius(noSignBtn, 22.5, 1.0, ColorWithHex(0xdddddd));
    
    UILabel *lastLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [self.view addSubview:lastLabel];
    [lastLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(noSignBtn.mas_bottom).offset(20);
        make.left.equalTo(@27.5);
    }];
    lastLabel.text = @"您可以在“我的-我的订单”进行签约";
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self configNavWhiteColorWithCloseSEL:@selector(backBtnClick)];
    
    if ([self.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)]) {
        self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];

    [self configNavRedColor];
    
    if ([self.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)]) {
        self.navigationController.interactivePopGestureRecognizer.enabled = YES;
    }
}

- (void)backBtnClick {
    [FMMemberCenterExitSignPopView showWithMessage:@"您是否退出签约？\n签约成功后，服务生效" desc:@"退出后，可在“我的-我的订单”继续签约" sureTitle:@"继续签约" cancelTitle:@"暂不签约" clickSure:^{
        [self signBtnClicked];
    } clickCancel:^{
        BOOL canJumpVC = NO;
        for (UIViewController *vc in [FMHelper getCurrentVC].navigationController.viewControllers) {
            if ([vc isMemberOfClass:NSClassFromString(@"FMBigCastHomePageViewController")]) {
                [[FMHelper getCurrentVC].navigationController popToViewController:vc animated:YES];
                canJumpVC = YES;
                break;
            } else if ([vc isMemberOfClass:NSClassFromString(@"FMMemberCenterProductViewController")]) {
                [[FMHelper getCurrentVC].navigationController popToViewController:vc animated:YES];
                canJumpVC = YES;
                break;
            } else if ([vc isMemberOfClass:NSClassFromString(@"FMCouponViewController")]) {
                [[FMHelper getCurrentVC].navigationController popToViewController:vc animated:YES];
                canJumpVC = YES;
                break;
            }
        }
        if (!canJumpVC) {
            [[FMHelper getCurrentVC].navigationController popViewControllerAnimated:YES];
        }
    }];
}

- (void)signBtnClicked {
    [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
        YTGOtherWebVC *webVC = [[YTGOtherWebVC alloc] init];
        webVC.startPage = [NSString stringWithFormat:@"%@%@%@",prefix, kAPI_MemberCenter_Sign, self.orderNo];
        webVC.titleStr = @"签约";

        [self.navigationController pushViewController:webVC animated:YES];
        NSMutableArray *array = self.navigationController.viewControllers.mutableCopy;
        [array removeObjectAtIndex:array.count - 2];
        self.navigationController.viewControllers = array;
    }];
}

- (void)noSignBtnClicked {
    [self backBtnClick];
}

@end
