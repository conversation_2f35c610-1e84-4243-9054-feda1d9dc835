//
//  FMMemberCenterVIPJCSignalDetailView.m
//  QCYZT
//
//  Created by zeng on 2023/10/9.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMMemberCenterVIPJCSignalDetailView.h"
#import "FMMemberCenterVIPJCKlineView.h"

@interface FMMemberCenterVIPJCSignalDetailView()

@property (nonatomic, strong) UILabel *signalDetailLabel;
@property (nonatomic, strong) FMMemberCenterVIPJCKlineView *graphView;
@property (nonatomic, strong) YYLabel *reminderLabel;

@end

@implementation FMMemberCenterVIPJCSignalDetailView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    UILabel *label1 = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x888888) backgroundColor:FMWhiteColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self addSubview:label1];
    [label1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(0);
        make.top.equalTo(2);
    }];
    label1.text = @"信号详情：";
    
    UILabel *signalDetailLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:UIColor.up_riseColor backgroundColor:FMWhiteColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [self addSubview:signalDetailLabel];
    [signalDetailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(label1.mas_right);
        make.right.equalTo(0);
        make.top.equalTo(label1);
    }];
    self.signalDetailLabel = signalDetailLabel;
    [signalDetailLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    
    UILabel *label2 = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(11) textColor:ColorWithHex(0x888888) backgroundColor:FMWhiteColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self addSubview:label2];
    [label2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(10);
        make.top.equalTo(signalDetailLabel.mas_bottom).offset(15);
    }];
    label2.text = @"日线";
    
    UILabel *label3 = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(11) textColor:ColorWithHex(0x888888) backgroundColor:FMWhiteColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [self addSubview:label3];
    [label3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(-10);
        make.top.equalTo(label2);
    }];
    label3.text = @"点击查看行情";
    label3.userInteractionEnabled = YES;
    [label3 addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(jumpToStockDetail)]];
    
    FMMemberCenterVIPJCKlineView *graphView = [FMMemberCenterVIPJCKlineView new];
    [self addSubview:graphView];
    [graphView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(0);
        make.right.equalTo(0);
        make.top.equalTo(label2.mas_bottom).offset(2);
        make.height.equalTo(170);
    }];
    self.graphView = graphView;
    
    YYLabel *reminderLabel = [[YYLabel alloc] init];
    reminderLabel.numberOfLines = 0;
    reminderLabel.preferredMaxLayoutWidth = UI_SCREEN_WIDTH - 64;
    [self addSubview:reminderLabel];
    [reminderLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(graphView);
        make.top.equalTo(graphView.mas_bottom).offset(10);
        make.bottom.equalTo(0);
    }];
    self.reminderLabel = reminderLabel;
}

- (void)setModel:(FMMemberCenterVIPJCStockPoolModel *)model {
    _model = model;
    
    // 有具体信号
    NSString *str;
    if (model.signalIndex.count) {
        if (model.poolType == JCStockPoolTypeJiaZhiLongTou) {
            str = [NSString stringWithFormat:@"买入信号-“波段牛熊”由“熊”转“牛”，%@出现积极信号", PoolTypeFTZB(model.poolType, model.signalIndex)];
        } else {
            str = [NSString stringWithFormat:@"买入信号-“%@”出现%@图标，%@出现积极信号", PoolTypeZTZB(model.poolType), PoolTypeIconString(model.poolType), PoolTypeFTZB(model.poolType, model.signalIndex)];
        }
    } else {
        if (model.poolType == JCStockPoolTypeJiaZhiLongTou) {
            if (model.signalLevel.integerValue - 2 > 0) {
                str = [NSString stringWithFormat:@"买入信号-“波段牛熊”由“熊”转“牛”，副图指标出现%zd个积极信号", model.signalLevel.integerValue - 2];
            } else {
                str = @"买入信号-“波段牛熊”由“熊”转“牛”";
            }
        } else {
            if (model.signalLevel.integerValue - 2 > 0) {
                str = [NSString stringWithFormat:@"买入信号-“%@”出现%@图标，副图指标出现%zd个积极信号", PoolTypeZTZB(model.poolType), PoolTypeIconString(model.poolType), model.signalLevel.integerValue - 2];
            } else {
                str = [NSString stringWithFormat:@"买入信号-“%@”出现%@图标", PoolTypeZTZB(model.poolType), PoolTypeIconString(model.poolType)];
            }
        }
    }
    NSMutableAttributedString *attrStr1 = [[NSMutableAttributedString alloc] initWithString:str];
    attrStr1.yy_lineSpacing = 3.0f;
    self.signalDetailLabel.attributedText = attrStr1;


//    NSString *str2 = [NSString stringWithFormat:@"注意：当“%@”%@（后三者请查看副图指标）同时出现积极信号时，买入更稳妥。", PoolTypeZTZB(model.poolType), PoolTypeFTZBReminder(model.poolType)];
    NSString *str2 = @"注意：当所有副图指标同时出现积极信号时，买入更稳妥";
    NSMutableAttributedString *attrStr2 = [[NSMutableAttributedString alloc] initWithString:str2];
    attrStr2.yy_color = ColorWithHex(0x666666);
    [attrStr2 yy_setColor:UIColor.up_riseColor range:[attrStr2.string rangeOfString:@"注意："]];
//    NSRange range = [attrStr2.string rangeOfString:@"点击查看"];
//    [attrStr2 yy_setUnderlineColor:ColorWithHex(0x317ad0) range:range];
//    [attrStr2 yy_setUnderlineStyle:NSUnderlineStyleSingle range:range];
//    [attrStr2 yy_setTextHighlightRange:range color:ColorWithHex(0x317ad0) backgroundColor:FMWhiteColor tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
//        UPMarketCodeMatchInfo *info = [FMUPDataTool matchInfoWithSetCodeAndCode:self.model.stockCode];
//        [UPRouterUtil goMarketHQWithCode:info.code setCode:info.setCode category:info.category tab:nil trend:@"kline"];
//    }];
    attrStr2.yy_font = FontWithSize(12);
    attrStr2.yy_lineSpacing = 3.0f;
    self.reminderLabel.attributedText = attrStr2;
    
    self.graphView.model = model;
}

- (void)jumpToStockDetail {
    UPMarketCodeMatchInfo *matchInfo = [FMUPDataTool matchInfoWithSetCodeAndCode:self.model.stockCode];
    UPRouterNavigate([@"upchina://market/stock" up_buildURLWithQueryParams:@{
        @"setcode" : @(matchInfo.setCode),
        @"code" : matchInfo.code,
        @"trend": UPRouterMarketStockTrendDay
    }]);
}

@end
