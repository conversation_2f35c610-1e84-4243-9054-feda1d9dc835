//
//  FMMemberCenterVIPJCSubVCTableCell.m
//  QCYZT
//
//  Created by zeng on 2024/8/13.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMMemberCenterVIPJCSubVCTableCell.h"
#import "FMWinnerStockTagView.h"
#import "FMMemberCenterVIPJCBuySellView.h"
#import "HttpRequestTool+MemberCenter.h"

@interface FMMemberCenterVIPJCSubVCTableCell()

@property (nonatomic, strong) UIStackView *stackView;

@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) YYLabel *codeLabel;
@property (nonatomic, strong) UILabel *strengthLabel;
@property (nonatomic, strong) UILabel *priceLabel;
@property (nonatomic, strong) UILabel *changePercentLabel;
@property (nonatomic, strong) UILabel *maxPercentLabel;
@property (nonatomic, strong) UIButton *foldBtn;

@property (nonatomic, strong) ZLTagView *tagView;

@property (nonatomic, strong) FMMemberCenterVIPJCBuySellView *buySellView;
@property (nonatomic, strong) UIButton *bottomFoldBtn;

@property (nonatomic, strong) UIView *sepLine;

@end

@implementation FMMemberCenterVIPJCSubVCTableCell

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.bottomFoldBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageRight imageTitleSpacing:5];
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
    [self.contentView addSubview:self.stackView];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(10, 15, 10, 15));
    }];

    {
        UIView *topView = [UIView new];
        [self.stackView addArrangedSubview:topView];
        [topView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(46);
        }];
        topView.userInteractionEnabled = YES;
        [topView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(foldBtnClicked)]];
        
        UIView *stockView = [UIView new];
        [topView addSubview:stockView];
        [stockView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.bottom.top.equalTo(0);
            make.width.equalTo(UI_Relative_WidthValue(90));
        }];
        stockView.userInteractionEnabled = YES;
        [stockView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(jumpToStockDetail)]];
                
        [topView addSubview:self.nameLabel];
        [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.equalTo(0);
            make.height.equalTo(22.5);
        }];
        
        [topView addSubview:self.codeLabel];
        [self.codeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.bottom.equalTo(0);
            make.height.equalTo(18.5);
        }];
        
        [topView addSubview:self.strengthLabel];
        
        [topView addSubview:self.priceLabel];
        
        [topView addSubview:self.changePercentLabel];
        
        [topView addSubview:self.maxPercentLabel];
        
        [topView addSubview:self.foldBtn];
    }
        
    [self.stackView addArrangedSubview:self.tagView];
    [self.tagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(0);
    }];
    
    [self.stackView addArrangedSubview:self.buySellView];
    
    [self.stackView addArrangedSubview:self.bottomFoldBtn];
    [self.bottomFoldBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(26.5);
    }];
    
    self.sepLine = [self.contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.height.equalTo(0.5);
        make.top.equalTo(0);
    }];
    self.sepLine.backgroundColor = UIColor.fm_sepline_color;
}

- (void)foldBtnClicked {
    if (self.model.poolType != JCStockPoolTypeLongHuZhuaYao || self.model.seatModel) {
        self.model.unfold = !self.model.unfold;
        if (self.refreshBlock) {
            self.refreshBlock();
        }
    } else {
        [HttpRequestTool requestMemberCenterLHZYSeatsWithJoinPoolID:self.model.joinPoolID start:^{
            [SVProgressHUD show];
        } failure:^{
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                [SVProgressHUD dismiss];
                
                self.model.seatModel = [SeatModel modelWithDictionary:dic[@"data"]];
                self.model.unfold = !self.model.unfold;
                if (self.refreshBlock) {
                    self.refreshBlock();
                }
            } else {
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    }
}

- (void)jumpToStockDetail {
    NSMutableString *paramSetCode = [NSMutableString stringWithCapacity:64];
    NSMutableString *paramCode = [NSMutableString stringWithCapacity:64];

    for (FMMemberCenterVIPJCStockPoolModel *model in self.stockList) {
        UPMarketCodeMatchInfo *matchInfo = [FMUPDataTool matchInfoWithSetCodeAndCode:model.stockCode];
        if (matchInfo) {
            if (paramSetCode.length > 0) {
                [paramSetCode appendString:@"_"];
            }
            if (paramCode.length > 0) {
                [paramCode appendString:@"_"];
            }
            [paramSetCode appendFormat:@"%d", (int)matchInfo.setCode];
            [paramCode appendString:matchInfo.code];
        }
    }

    // 根据业务类型获取对应的模板名称
    NSString *templateName = [self getTemplateNameForPoolType:self.model.poolType];

    NSMutableDictionary *params = [@{
        @"setcode" : paramSetCode,
        @"code" : paramCode,
        @"current" : @(self.currentIndex),
        @"trend": UPRouterMarketStockTrendDay
    } mutableCopy];

    // 如果有对应的模板名称，添加到参数中
    if (templateName.length > 0) {
        params[@"templateName"] = templateName;
    }

    UPRouterNavigate([@"upchina://market/stock" up_buildURLWithQueryParams:params]);
}

#pragma mark - Private Methods
/// 根据业务类型获取对应的模板名称
/// @param poolType 业务类型
- (NSString *)getTemplateNameForPoolType:(JCStockPoolType)poolType {
    switch (poolType) {
        case JCStockPoolTypeLongTouQiSheng:
            return @"龙头启升";
        case JCStockPoolTypeLongTouZaiZhang:
            return @"龙头再涨";
        case JCStockPoolTypeLongTouYinZi:
            return @"龙头因子";
        case JCStockPoolTypeJiaZhiLongTou:
            return @"价值龙头";
        case JCStockPoolTypeLongHuZhuaYao:
            return @"龙虎抓妖";
        default:
            return @"";
    }
}

- (NSMutableAttributedString *)stockCodeImgAttachmentWithModel:(FMMemberCenterVIPJCStockPoolModel *)model {
    UIColor *bgColor;
    if ([model.typeStr isEqualToString:@"科创"]) {
       bgColor = ColorWithHex(0x732fe0);
    } else if ([model.typeStr isEqualToString:@"创"]) {
       bgColor = ColorWithHex(0x4D89FF);
    }
    CGSize textSize = [model.typeStr sizeWithAttributes:@{NSFontAttributeName : BoldFontWithSize(11)}];
    CGSize textImageSize = CGSizeMake(ceil(textSize.width + 4), 16);
    UIImage *bgImg = [UIImage imageWithColor:bgColor andSize:textImageSize];
    UIImage *textImg = [bgImg addTextWithImageSize:textImageSize text:model.typeStr textRect:CGRectMake((textImageSize.width - textSize.width) * 0.5, (textImageSize.height - textSize.height) * 0.5, textSize.width, textSize.height) textAttributes:@{NSFontAttributeName : BoldFontWithSize(11), NSForegroundColorAttributeName : FMWhiteColor}];
    return [NSAttributedString yy_attachmentStringWithContent:[textImg addCornerRadius:2.0] contentMode:UIViewContentModeCenter attachmentSize:textImageSize alignToFont:FontWithSize(13) alignment:YYTextVerticalAlignmentCenter];
}

- (void)setModel:(FMMemberCenterVIPJCStockPoolModel *)model {
    _model = model;
    
    self.nameLabel.text = model.stockName;
    
    if (model.typeStr.length) {
        NSMutableAttributedString *stockCodeAttrStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@" %@", [model.stockCode substringFromIndex:2]] attributes:@{NSFontAttributeName : FontWithSize(13), NSForegroundColorAttributeName : UIColor.up_textSecondary2Color}];
        [stockCodeAttrStr insertAttributedString:[self stockCodeImgAttachmentWithModel:model] atIndex:0];
        self.codeLabel.attributedText = stockCodeAttrStr;
    } else {
        NSMutableAttributedString *stockCodeAttrStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@", [model.stockCode substringFromIndex:2]] attributes:@{NSFontAttributeName : FontWithSize(13), NSForegroundColorAttributeName : UIColor.up_textSecondary2Color}];
        self.codeLabel.attributedText = stockCodeAttrStr;
    }
        
    if (model.highIncrease.length) {
        if (model.poolType == JCStockPoolTypeLongHuZhuaYao && [model.dateString isEqualToString:[[NSDate date] dateStringWithFormatString:@"yyyy-MM-dd"]]) {
            self.maxPercentLabel.text = @"--";
            self.maxPercentLabel.textColor = UIColor.up_riseColor;
        } else {
            if (model.highIncrease.floatValue > 0) {
                self.maxPercentLabel.text = [NSString stringWithFormat:@"+%@%%", model.highIncrease];
                self.maxPercentLabel.textColor = UIColor.up_riseColor;
            } else if (model.highIncrease.floatValue < 0) {
                self.maxPercentLabel.text = [NSString stringWithFormat:@"%@%%", model.highIncrease];
                self.maxPercentLabel.textColor = ColorWithHex(0x2d932b);
            } else {
                self.maxPercentLabel.text = [NSString stringWithFormat:@"%@%%", model.highIncrease];
                self.maxPercentLabel.textColor = ColorWithHex(0x5d6c7c);
            }
        }
    } else {
        self.maxPercentLabel.text = @"--";
        self.maxPercentLabel.textColor = UIColor.up_riseColor;
    }
    
    if (model.poolType == JCStockPoolTypeLongHuZhuaYao) {
        self.strengthLabel.hidden = YES;
        [self.priceLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(101));
            make.centerY.equalTo(0);
        }];
        [self.changePercentLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(173));
            make.centerY.equalTo(0);
        }];
        [self.maxPercentLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(253));
            make.centerY.equalTo(0);
        }];
        self.foldBtn.hidden = NO;
        [self.foldBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(-5);
            make.centerY.equalTo(0);
        }];
        
        if (model.capitalModel.count) {
            self.tagView.hidden = NO;
            self.tagView.titleArray = model.capitalModel;
            [self.tagView.selections removeAllObjects];
            NSInteger selectIndex = -1;
            for (NSInteger i = 0; i < model.capitalModel.count; i++) {
                NSString *cM = model.capitalModel[i];
                if ([cM isEqualToString:@"游资卖"]) {
                    selectIndex = i;
                    break;
                }
            }
            if (selectIndex != -1) {
                [self.tagView.selections addObject:@(selectIndex)];
            }
            [self.tagView setNeedsLayout];
            [self.tagView layoutIfNeeded];
            [self.tagView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.equalTo(self.tagView.viewHeight);
            }];
        } else {
            self.tagView.hidden = YES;
        }
        
        if (model.unfold) {
            self.buySellView.model = model;
            self.buySellView.hidden = NO;
            self.bottomFoldBtn.hidden = NO;
        } else {
            self.buySellView.hidden = YES;
            self.bottomFoldBtn.hidden = YES;
        }
    } else {
        self.strengthLabel.hidden = NO;
        self.strengthLabel.text = [NSString stringWithFormat:@"%zd星", model.signalLevel.integerValue];
        [self.strengthLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(91));
            make.centerY.equalTo(0);
        }];
        [self.priceLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(142));
            make.centerY.equalTo(0);
        }];
        [self.changePercentLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(205));
            make.centerY.equalTo(0);
        }];
        [self.maxPercentLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(280));
            make.centerY.equalTo(0);
        }];
        
        self.tagView.hidden = YES;
        self.buySellView.hidden = YES;
        self.bottomFoldBtn.hidden = YES;
    }
    
    if (model.unfold) {
        self.foldBtn.imageView.transform = CGAffineTransformMakeRotation(M_PI);
    } else {
        self.foldBtn.imageView.transform = CGAffineTransformIdentity;
    }
    
    if (model.tradeStatus == UPMarketTradeStatusUnknown && model.yClosePrice == 0) {
        self.priceLabel.text = self.changePercentLabel.text = @"--";
        self.priceLabel.textColor = self.changePercentLabel.textColor = ColorWithHex(0x5d6c7c);
        return;
    }
    
    BOOL isStopStatus = [UPCommonMarketUtil isStopStatus:model.tradeStatus];
    NSString *status = [UPMarketUIDataTool getStateWithTradeStatus:model.tradeStatus];
    if (isStopStatus) {
        self.priceLabel.text = self.changePercentLabel.text = status;
        self.priceLabel.textColor = self.changePercentLabel.textColor = ColorWithHex(0x5d6c7c);
    } else {        
        self.priceLabel.text = [UPMarketUICalculateUtil transPriceString:model.nowPrice precise:2 needsymbol:NO];
        self.changePercentLabel.text = [UPMarketUICalculateUtil transPercent:model.changeRatio needSymbol:YES];
        self.priceLabel.textColor = self.changePercentLabel.textColor = [UPMarketUICompareTool compareWithData:model.changeRatio baseData:0 precise:0];
    }
}

- (void)setIsFirstCell:(BOOL)isFirstCell {
    _isFirstCell = isFirstCell;
    
    self.sepLine.hidden = isFirstCell;
}

- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentFill distribution:UIStackViewDistributionEqualSpacing spacing:5 arrangedSubviews:nil];
    }
    
    return _stackView;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    }
    
    return _nameLabel;
}

- (UILabel *)strengthLabel {
    if (!_strengthLabel) {
        _strengthLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    }
    
    return _strengthLabel;
}

- (UILabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:ColorWithHex(0x5d6c7c) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    }
    
    return _priceLabel;
}

- (UILabel *)changePercentLabel {
    if (!_changePercentLabel) {
        _changePercentLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    }
    
    return _changePercentLabel;
}

- (UILabel *)maxPercentLabel {
    if (!_maxPercentLabel) {
        _maxPercentLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    }
    
    return _maxPercentLabel;
}

- (YYLabel *)codeLabel {
    if (!_codeLabel) {
        _codeLabel = [YYLabel new];
        _codeLabel.textAlignment = NSTextAlignmentLeft;
    }
    
    return _codeLabel;
}

- (UIButton *)foldBtn {
    if (!_foldBtn) {
        _foldBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:ImageWithName(@"MemberCenter_FoldArrow") target:nil action:nil];
        _foldBtn.userInteractionEnabled = NO;
        _foldBtn.lz_touchAreaInsets = UIEdgeInsetsMake(-10, -10, -10, -10);
    }
    
    return _foldBtn;
}

- (ZLTagView *)tagView {
    if (!_tagView) {
        _tagView = [[ZLTagView alloc] init];
        _tagView.tagLabelFont = FontWithSize(12);
        _tagView.tagLabelWidthPadding = 10.0f;
        _tagView.labelHeight = 17.0f;
        _tagView.middlePadding = 5.0f;
        _tagView.tagLabelTextColor = UIColor.up_riseColor;
        _tagView.tagLabelCornerRadius = 2.0f;
        _tagView.tagLabelBorderColor = UIColor.up_riseColor;
        _tagView.tagLabelBorderWidth = 1.0f;
        _tagView.selectedTextColor = ColorWithHex(0x2d932b);
        _tagView.selectedBorderColor = ColorWithHex(0x2d932b);
        _tagView.numberofLines = 0;
    }
    
    return _tagView;
}

- (FMMemberCenterVIPJCBuySellView *)buySellView {
    if (!_buySellView) {
        _buySellView = [[FMMemberCenterVIPJCBuySellView alloc] init];
    }
    
    return _buySellView;
}

- (UIButton *)bottomFoldBtn {
    if (!_bottomFoldBtn) {
        _bottomFoldBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(12) normalTextColor:UIColor.up_textSecondary1Color backgroundColor:UIColor.up_contentBgColor title:@"收起" image:ImageWithName(@"MemberCenter_FoldArrow") target:self action:@selector(foldBtnClicked)];
        _bottomFoldBtn.imageView.transform = CGAffineTransformMakeRotation(M_PI);
    }
    
    return _bottomFoldBtn;
}

@end
