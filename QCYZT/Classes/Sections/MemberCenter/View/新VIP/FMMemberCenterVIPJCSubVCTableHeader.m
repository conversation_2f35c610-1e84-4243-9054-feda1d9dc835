//
//  FMMemberCenterVIPJCSubVCTableHeader.m
//  QCYZT
//
//  Created by zeng on 2024/8/12.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMMemberCenterVIPJCSubVCTableHeader.h"

@interface FMMemberCenterVIPJCSubVCTableHeader()

@property (nonatomic, strong) UIStackView *stackView; 
@property (nonatomic, strong) UIView *grayView;
@property (nonatomic, strong) YYLabel *reminderLabel;
@property (nonatomic, strong) UIView *bottomView;

@property (nonatomic, assign) BOOL isSameDay;
@property (nonatomic, assign) JCStockPoolType poolType;

@end

@implementation FMMemberCenterVIPJCSubVCTableHeader

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    self.backgroundColor = UIColor.up_contentBgColor;
    
    [self addSubview:self.stackView];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(7.5, 15, 7.5, 15));
    }];

    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(20);
    }];
}

- (void)btnClick:(UIButton *)btn {
    [PushMessageView showWithTitle:nil message:PoolTypeGCJS(self.poolType) noticeImage:nil sureTitle:@"确定" cancelTitle:nil clickSure:nil clickCancel:nil];
}

- (void)configPoolType:(JCStockPoolType)poolType isSameDay:(BOOL)isSameDay {
    _poolType = poolType;
    _isSameDay = isSameDay;
    
    NSString *reminder;
    if (poolType == JCStockPoolTypeLongHuZhuaYao) {
        if (self.isSameDay) {
            reminder = @"注意：\n判断买卖点需辅助“三维擒龙”策略。\n盘中信号不稳定，可能出现信号消失情况，收盘后信号不在变动。";
        } else {
            reminder = @"注意：\n判断买卖点需辅助“三维擒龙”策略。";
        }
    } else {
        if (self.isSameDay) {
            reminder = @"注意：\n根据AI量化模型计算“信号强度”，强度越高信号越准确！\n判断买卖点需辅助“三维擒龙”策略。\n盘中信号不稳定，可能出现信号消失情况，收盘后信号不在变动。";
        } else {
            reminder = @"注意：\n根据AI量化模型计算“信号强度”，强度越高信号越准确！\n判断买卖点需辅助“三维擒龙”策略。";
        }
    }
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:reminder];
    attrStr.yy_font = FontWithSize(12);
    attrStr.yy_color = UIColor.up_textSecondaryColor;
    attrStr.yy_lineSpacing = 6.0f;
    NSRange range1 = [reminder rangeOfString:@"注意"];
    [attrStr yy_setFont:BoldFontWithSize(13) range:range1];
    NSRange range2 = [reminder rangeOfString:@"三维擒龙"];
    [attrStr yy_setColor:FMNavColor range:range2];
    NSRange range3 = [reminder rangeOfString:@"信号强度"];
    if (range3.location != NSNotFound) {
        [attrStr yy_setColor:FMNavColor range:range3];
    }
    self.reminderLabel.attributedText = attrStr;
    
    // 设置临时宽度并触发布局
    self.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, 0);
    [self layoutIfNeeded];
    CGFloat height = [self systemLayoutSizeFittingSize:UILayoutFittingCompressedSize].height;
    self.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, height);
}

- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentFill distribution:UIStackViewDistributionEqualSpacing spacing:13 arrangedSubviews:@[self.grayView, self.bottomView]];
    }
    
    return _stackView;
}

- (UIView *)grayView {
    if (!_grayView) {
        _grayView = [UIView new];
        _grayView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        UI_View_Radius(_grayView, 5);
        
        [_grayView addSubview:self.reminderLabel];
        [self.reminderLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.insets(UIEdgeInsetsMake(10, 12.5, 10, 12.5));
        }];
    }
    
    return _grayView;
}

- (YYLabel *)reminderLabel {
    if (!_reminderLabel) {
        _reminderLabel = [YYLabel new];
        _reminderLabel.numberOfLines = 0;
        _reminderLabel.preferredMaxLayoutWidth = UI_SCREEN_WIDTH - 55;
    }
    
    return _reminderLabel;
}

- (UIView *)bottomView {
    if (!_bottomView) {
        _bottomView = [UIView new];
        
        UIView *redLine = [UIView new];
        UI_View_Radius(redLine, 1.5);
        [_bottomView addSubview:redLine];
        [redLine mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.centerY.equalTo(0);
            make.width.equalTo(3);
            make.height.equalTo(12);
        }];
        redLine.backgroundColor = FMNavColor;
        
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        [_bottomView addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(redLine.mas_right).offset
            (6);
            make.centerY.equalTo(0);
        }];
        label.text = @"股票池";
        
        UIButton *btn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(14) normalTextColor:ColorWithHex(0x0074fa) backgroundColor:FMClearColor title:@"股池介绍" image:nil target:self action:@selector(btnClick:)];
        [_bottomView addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.centerY.equalTo(0);
        }];
    }
    
    return _bottomView;
}


@end
