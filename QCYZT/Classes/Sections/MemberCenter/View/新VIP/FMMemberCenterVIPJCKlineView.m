//
//  FMMemberCenterVIPJCKlineView.m
//  QCYZT
//
//  Created by zeng on 2023/10/11.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMMemberCenterVIPJCKlineView.h"
#import <UPMarketUISDK/UPMarketUIView.h>
#import "FMMemberCenterVIPJCKlineCustomLayer.h"
#import "UPMarketUIKlineMajorLayer+FMMarketUIKlineMajorLayerHelper.h"

@interface FMMemberCenterVIPJCKlineView()

// k线图
@property(nonatomic, strong) UPMarketUIView * marketUIView;
// 提示弹框
@property (nonatomic, strong) UIView *reminderView;
@property (nonatomic, strong) UILabel *reminderLabel;

@end

@implementation FMMemberCenterVIPJCKlineView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    self.backgroundColor = FMWhiteColor;

    self.marketUIView = [[UPMarketUIView alloc] initWithIndexID:UPMarketUIMajorIndexMALine isKline:YES classLayer:FMMemberCenterVIPJCKlineCustomLayer.class];
    self.marketUIView.fetcherKey = kUPMarketUIStockDataKeyKlineDaily; // 设置数据类型为日K
    [self.marketUIView enableGestures:UPMarketUIBaseStockViewGestureTap | UPMarketUIBaseStockViewGestureLongPress | UPMarketUIBaseStockViewGesturePan];

    [self addSubview:self.marketUIView];
    [self.marketUIView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(0, 0, 0, 10));
    }];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super touchesBegan:touches withEvent:event];
    
    UITouch *touch = [touches anyObject];
    CGPoint point = [touch locationInView:self];
    FMMemberCenterVIPJCKlineCustomLayer *layer = (FMMemberCenterVIPJCKlineCustomLayer *)self.marketUIView.stockLayer;
    // 让点击范围扩大一点
    if (CGRectContainsPoint(CGRectMake(layer.iconRect.origin.x - 10, layer.iconRect.origin.y - 10, layer.iconRect.size.width + 20, layer.iconRect.size.height + 20), point)) {
        if (!self.reminderView.superview) {
            [self showReminderView];
        }
    } 
    else {
        UPMarketCodeMatchInfo *info = [FMUPDataTool matchInfoWithSetCodeAndCode:self.model.stockCode];
        UPRouterNavigate([@"upchina://market/stock" up_buildURLWithQueryParams:@{
            @"setcode" : @(info.setCode),
            @"code" : info.code,
            @"trend": UPRouterMarketStockTrendDay
        }]);
    }
}

- (void)showReminderView {
    NSString *str;
    if (self.model.poolType == JCStockPoolTypeJiaZhiLongTou) {
        str = @"“波段牛熊”由“熊”转“牛”";
    } else {
        str = [NSString stringWithFormat:@"“%@”出现%@图标", PoolTypeZTZB(self.model.poolType), PoolTypeIconString(self.model.poolType)];
    }
    self.reminderLabel.text = str;
    CGSize strSize = [str sizeWithFont:FontWithSize(12) andSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX)];
    CGSize bgSize = CGSizeMake(strSize.width + 20, strSize.height + 8);

    FMMemberCenterVIPJCKlineCustomLayer *layer = (FMMemberCenterVIPJCKlineCustomLayer *)self.marketUIView.stockLayer;
    CGFloat iconToBgPadding = 5;
    CGFloat iconX = layer.iconRect.origin.x;
    CGFloat iconY = layer.iconRect.origin.y;
    if (CGRectGetMidX(layer.iconRect) >= self.width * 0.5) { // 显示在左边
        self.reminderView.frame = CGRectMake(iconX - iconToBgPadding - bgSize.width, iconY, bgSize.width, bgSize.height);
    } else { // 显示在右边
        self.reminderView.frame = CGRectMake(CGRectGetMaxX(layer.iconRect) + iconToBgPadding, iconY, bgSize.width, bgSize.height);
    }
    [self addSubview:self.reminderView];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.reminderView removeFromSuperview];
    });
}


- (void)setModel:(FMMemberCenterVIPJCStockPoolModel *)model {
    NSLog(@"%@-------%@", model.stockName, _model.stockName);
    _model = model;
    
//    if (!model.drawLineModels) {
    UPMarketCodeMatchInfo *info = [FMUPDataTool matchInfoWithSetCodeAndCode:model.stockCode];
    self.marketUIView.stockModel = [UPMarketUIBaseModel modelWithSetCode:info.setCode code:info.code];
    ((UPMarketUIKlineMajorLayer *)self.marketUIView.stockLayer).customDataArr = @[self.model];
    
    [self.marketUIView stockViewStart];
    //    } else {
//        [self setNeedsDisplay];
//    }
}


- (UIView *)reminderView {
    if (!_reminderView) {
        _reminderView = [UIView new];
        _reminderView.backgroundColor = ColorWithHexAlpha(0x000000, 0.6);
        UI_View_Radius(_reminderView, 5);
        
        UILabel *reminderLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        [_reminderView addSubview:reminderLabel];
        [reminderLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.insets(UIEdgeInsetsMake(4, 8, 4, 8));
        }];
        self.reminderLabel = reminderLabel;
    }
    
    return _reminderView;
}

@end
