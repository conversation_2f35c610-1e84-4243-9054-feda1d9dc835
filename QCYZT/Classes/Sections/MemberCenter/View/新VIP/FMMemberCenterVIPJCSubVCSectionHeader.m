//
//  FMMemberCenterVIPJCSubVCSectionHeader.m
//  QCYZT
//
//  Created by zeng on 2024/8/13.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMMemberCenterVIPJCSubVCSectionHeader.h"

static const NSInteger kButtonTagBase = 100;

@interface FMMemberCenterVIPJCSubVCSectionHeader()

@property (nonatomic, strong) UIStackView *stackView;

@property (nonatomic, strong) UIScrollView *scrollView;

@property (nonatomic, strong) UIView *bottomView;
@property (nonatomic, strong) UIButton *strengthBtn;
@property (nonatomic, strong) UIButton *priceBtn;
@property (nonatomic, strong) UIButton *changePercentBtn;
@property (nonatomic, strong) UIButton *maxPercentBtn;

@property (nonatomic, assign) NSInteger sortIndex;
@property (nonatomic, assign) SortType sortType;

@property (nonatomic, strong) UIButton *currentSortBtn;

@property (nonatomic, assign) CGFloat headerHeight;

@end

@implementation FMMemberCenterVIPJCSubVCSectionHeader

- (instancetype)initWithReuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithReuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    
    return self;
}

- (void)setUp {
    self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
    [self addSubview:self.stackView];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(10, 15, 5, 15));
    }];

    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(28);
    }];
    [self.scrollView addSubview:self.tagView];
    self.scrollView.hidden = YES;
    
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(20);
    }];
    UILabel *nameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.bottomView addSubview:nameLabel];
    [nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(0);
        make.centerY.equalTo(0);
    }];
    nameLabel.text = @"股票名称";
    
    UIButton *btn = [self createBtnWithName:@"强度" index:0];
    [self.bottomView addSubview:btn];
    self.strengthBtn = btn;
    
    UIButton *btn1 = [self createBtnWithName:@"最新价" index:1];
    [self.bottomView addSubview:btn1];
    self.priceBtn = btn1;
    
    UIButton *btn2 = [self createBtnWithName:@"涨跌幅" index:2];
    [self.bottomView addSubview:btn2];
    self.changePercentBtn = btn2;
    
    UIButton *btn3 = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(13) normalTextColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor title:@"入池涨幅" image:ImageWithName(@"MemberCenter_wenhao") target:self action:@selector(wenhaoClicked:)];
    [self.bottomView addSubview:btn3];
    self.maxPercentBtn = btn3;
    
    self.sortIndex = 2;
    self.sortType = SortTypeDescending;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.maxPercentBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageRight imageTitleSpacing:5];
}

- (void)autoScrollToIndex:(NSInteger)currentSelectedIndex {
    CGFloat scrollViewContentWidth = _scrollView.contentSize.width;
    if (scrollViewContentWidth <= _scrollView.width) {
        return;
    }
    
    ZLTagView *currentSelectedTagLabel = _tagView.subviews[currentSelectedIndex];
    CGFloat currentSelectedTagLabelWidth = currentSelectedTagLabel.width;
    CGFloat currentSelectedTagLabelX = currentSelectedTagLabel.x;
    
    if (currentSelectedTagLabelX < (_scrollView.width - currentSelectedTagLabelWidth) * 0.5) { // 滚动到最前面
        [_scrollView setContentOffset:CGPointMake(0, 0) animated:YES];
    } else if (scrollViewContentWidth < currentSelectedTagLabelX + (_scrollView.width + currentSelectedTagLabelWidth) * 0.5) { // 滚动到最后面
        [_scrollView setContentOffset:CGPointMake(scrollViewContentWidth - _scrollView.width, 0) animated:YES];
    } else { // 滚动到中间
        [_scrollView setContentOffset:CGPointMake(currentSelectedTagLabelX + (currentSelectedTagLabelWidth - _scrollView.width) * 0.5, 0) animated:YES];
    }
}

- (void)sortButtonClick:(UIButton *)button {
    NSArray *imgArr = @[ImageWithName(@"DN自选-默认排序"), ImageWithName(@"DN自选-降序"), ImageWithName(@"DN自选-升序")];
    [self.currentSortBtn setImage:imgArr[0] forState:UIControlStateNormal];
    
    NSInteger index = button.tag - kButtonTagBase;
    if (self.sortIndex == index) {
        self.sortType++;
        if (self.sortType > SortTypeAscending) {
            self.sortType = SortTypeDescending;
        }
    } else {
        self.sortIndex = index;
        self.sortType = SortTypeDescending;
    }
    [button setImage:imgArr[self.sortType] forState:UIControlStateNormal];
    
    self.currentSortBtn = button;
    if ([self.delegate respondsToSelector:@selector(didSelectColumnWithIndex:sortType:)]) {
        [self.delegate didSelectColumnWithIndex:self.sortIndex sortType:self.sortType];
    }
}

- (void)wenhaoClicked:(UIButton *)btn {
    [SVProgressHUD showImage:nil status:@"股票入选股池后至今最高涨幅"];
}

- (UIButton *)createBtnWithName:(NSString *)name index:(NSInteger)index {
    UIButton *button = [[UIButton alloc] init];
    button.tag = kButtonTagBase + index;
    [button setTitle:name forState:UIControlStateNormal];
    [button addTarget:self action:@selector(sortButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    button.titleLabel.font = FontWithSize(13);
    [button setTitleColor:UIColor.up_textSecondary1Color forState:UIControlStateNormal];
    if (index == 2) {
        [button setImage:ImageWithName(@"DN自选-降序") forState:UIControlStateNormal];
        self.currentSortBtn = button;
    } else {
        [button setImage:ImageWithName(@"DN自选-默认排序") forState:UIControlStateNormal];
    }
    
    CGSize size = [button.titleLabel sizeThatFits:CGSizeMake(CGFLOAT_MAX, 20)];
    [button setImageEdgeInsets:UIEdgeInsetsMake(0, size.width - 2, 0, 0)];
    [button setTitleEdgeInsets:UIEdgeInsetsMake(0, -22, 0, 0)];
    
     return button;
}

- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentFill distribution:UIStackViewDistributionEqualSpacing spacing:10 arrangedSubviews:@[self.scrollView, self.bottomView]];
    }
    
    return _stackView;
}

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [UIScrollView new];
        _scrollView.showsHorizontalScrollIndicator = NO;
        _scrollView.layer.masksToBounds = NO;
    }
    
    return _scrollView;
}

- (FMMemberCenterVIPJCCapitalChooseView *)tagView {
    if (!_tagView) {
        _tagView = [[FMMemberCenterVIPJCCapitalChooseView alloc] initWithFrame:CGRectMake(0, 0, CGFLOAT_MAX, 0)];
        _tagView.backgroundColor = FMClearColor;
        _tagView.tagLabelFont = FontWithSize(14);
        _tagView.middlePadding = 10.0f;
        _tagView.tagLabelWidthPadding = 30.0f;
        _tagView.labelHeight = 28.0f;
        _tagView.tagLabelCornerRadius = 4.0f;
        _tagView.allowsSelection = YES;
        _tagView.allowsMultipleSelection = NO;
        _tagView.singSelectionCanNotInvertSelect = YES;
        _tagView.tagLabelTextColor = UIColor.up_textSecondaryColor;
        _tagView.selectedTextColor = UIColor.up_riseColor;
        _tagView.tagLabelBgColor = UIColor.fm_F7F7F7_2E2F33;
        _tagView.selectedBackgroundColor = UIColor.fm_market_f10_tagView_label_selected_bgColor;
        [_tagView.selections addObject:@(0)]; // 默认选中
        _tagView.numberofLines = 1;
        WEAKSELF
        _tagView.didSelectedBlock = ^(NSArray *selections, NSInteger currentSelectedIndex) {
            [__weakSelf.delegate chooseCaptialName:__weakSelf.tagView.titleArray[currentSelectedIndex]];
            
            [__weakSelf autoScrollToIndex:currentSelectedIndex];
        };
    }
    
    return _tagView;
}

- (UIView *)bottomView {
    if (!_bottomView) {
        _bottomView = [UIView new];
    }
    
    return _bottomView;
}

- (void)setPoolType:(JCStockPoolType)poolType {
    _poolType = poolType;
    
    if (poolType == JCStockPoolTypeLongHuZhuaYao) {
        self.scrollView.hidden = NO;
        self.strengthBtn.hidden = YES;
        [self.priceBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(101));
            make.centerY.equalTo(0);
        }];
        [self.changePercentBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(173));
            make.centerY.equalTo(0);
        }];
        [self.maxPercentBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(253));
            make.centerY.equalTo(0);
        }];
    } else  {
        self.scrollView.hidden = YES;
        self.strengthBtn.hidden = NO;
        [self.strengthBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(91));
            make.centerY.equalTo(0);
        }];
        [self.priceBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(142));
            make.centerY.equalTo(0);
        }];
        [self.changePercentBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(205));
            make.centerY.equalTo(0);
        }];
        [self.maxPercentBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(280));
            make.centerY.equalTo(0);
        }];
    }
}

- (void)setCapitalBadges:(NSArray<NSDictionary *> *)capitalBadges {
    _capitalBadges = capitalBadges;
    
    NSArray *capitals = @[@"全部", @"超买", @"强买", @"弱买", @"巨买", @"爆买", @"连买", @"独大", @"控盘", @"游资", @"机游"];
    if (capitalBadges.count) {
        capitals = [capitalBadges valueForKeyPath:@"key"];
    }
    self.tagView.titleArray = capitals;
    [self.tagView setNeedsLayout];
    [self.tagView layoutIfNeeded];
    self.tagView.frame = CGRectMake(0, 0, self.tagView.singleLineViewWidth, 28);
    self.scrollView.contentSize = CGSizeMake(self.tagView.singleLineViewWidth, 28);
    
    self.tagView.badges = [capitalBadges valueForKeyPath:@"value"];
}

- (void)setDelegate:(id<FMMemberCenterVIPJCSubVCSectionHeaderDelegate>)delegate {
    _delegate = delegate;
    
    // 先来排序一下
    if ([delegate respondsToSelector:@selector(didSelectColumnWithIndex:sortType:)]) {
        [delegate didSelectColumnWithIndex:self.sortIndex sortType:self.sortType];
    }
}

@end
