//
//  FMVIPXLZFDetailVC.m
//  QCYZT
//
//  Created by shumi on 2023/10/17.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMVIPXLZFDetailVC.h"
#import "HttpRequestTool+MemberCenter.h"
#import "FMVIPXLModel.h"
#import "ZFPlayerController.h"
#import "ZFPlayerControlView.h"
#import "ZFAVPlayerManager.h"
#import "FMProgressHUD.h"
#import "FMPlayerManager.h"
#import "FMDetailHtmlCell.h"
#import "HTMLParseTool.h"
#import "MemberCenterVisitManager.h"

@interface FMVIPXLZFDetailVC ()<UITextViewDelegate>
@property (nonatomic,assign) MemberCenterJCRequestType requestType;
//contentType == 1 内容    contentType == 其他 战法视频
@property (nonatomic, assign) NSInteger contentType;
@property (nonatomic, strong) FMVIPXLModel *model;
@property (nonatomic, strong) UIImageView *coverImg;
@property (nonatomic, strong) UILabel *titleLB;
@property (nonatomic, strong) UILabel *timeLB;
//@property (nonatomic, strong) UILabel *summaryLB;
@property (nonatomic, strong) UITextView *contentTextView;
@property (nonatomic, strong) UILabel *bottomLB;
//@property (nonatomic, strong) ZFPlayerController *player;
@property (nonatomic, strong) ZFPlayerControlView *controlView;
//@property (nonatomic, strong) ZFAVPlayerManager *playerManager;
@property (nonatomic, strong) FMDetailHtmlModel *htmlModel;
/// HTML model加载完成
@property (nonatomic,assign) BOOL loadCompleted;
@end

@implementation FMVIPXLZFDetailVC

- (instancetype)initWithType:(NSInteger)requestType contentType:(NSInteger)contentType {
    self = [super init];
    if (self) {
        self.requestType = requestType;
        self.contentType = contentType;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.up_contentBgColor;
    [self requestData];
    [self setupUI];

    // 会员中心访问埋点 - 进入页面
    MemberCenterFunctionType functionType = [self getMemberCenterFunctionType];
    if (functionType != MemberCenterFunctionTypeNone) {
        [[MemberCenterVisitManager sharedManager] trackPageEnter:self functionId:functionType];
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    if ([FMPlayerManager shareManager].vipXLModel.contentId != self.contentId) {
        [FMPlayerManager dismissGlobalVideoPlayViewWithDestroyPlayer];
    }
    
    [self configNavWhiteColorWithCloseSEL:@selector(backArrowClicked)];
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    if (![FMPlayerManager shareManager].isSmallViewPlay) {
        [FMPlayerManager shareManager].player.viewControllerDisappear = YES;
    }
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    if (![FMPlayerManager shareManager].isSmallViewPlay) {
        [FMPlayerManager dismissGlobalVideoPlayViewWithDestroyPlayer];
    }

    // 会员中心访问埋点 - 退出页面
    MemberCenterFunctionType functionType = [self getMemberCenterFunctionType];
    if (functionType != MemberCenterFunctionTypeNone) {
        [[MemberCenterVisitManager sharedManager] trackPageExit:self functionId:functionType];
    }
}

//  是否支持自动转屏
- (BOOL)shouldAutorotate
{
    return NO;
}

// 支持哪些转屏方向
- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    if ([FMPlayerManager shareManager].player.isFullScreen) {
        return UIInterfaceOrientationMaskLandscape;
    }
    return UIInterfaceOrientationMaskPortrait;
}

- (void)setupUI {
    UIImageView *coverImg = [[UIImageView alloc] init];
    coverImg.hidden = self.contentType == 1;
    [self.view addSubview:coverImg];
    [coverImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(@0);
        if (self.contentType == 1) {
            make.height.equalTo(@(CGFLOAT_MIN));
        } else {
            make.height.equalTo(@(ceil(UI_SCREEN_WIDTH*9/16)));
        }
    }];
    coverImg.contentMode = UIViewContentModeScaleAspectFill;
    coverImg.layer.masksToBounds = YES;
    coverImg.backgroundColor = [UIColor grayColor];
    coverImg.userInteractionEnabled = YES;
    self.coverImg = coverImg;
    
    UIImageView *playIcon = [[UIImageView alloc] init];
    [coverImg addSubview:playIcon];
    [playIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.centerY.equalTo(coverImg);
        make.width.height.equalTo(@45);
    }];
    playIcon.image = [UIImage imageNamed:@"live_list_play"];
    
    
    UIScrollView *scrollView = [[UIScrollView alloc] init];
    scrollView.showsHorizontalScrollIndicator = NO;
    scrollView.showsVerticalScrollIndicator = NO;
    [self.view addSubview:scrollView];
    [scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(coverImg.mas_bottom);
        make.left.right.bottom.equalTo(0);
    }];
   
    UIView *contentView = [[UIView alloc] init];
    [scrollView addSubview:contentView];
    [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
        make.width.equalTo(UI_SCREEN_WIDTH);
    }];
    
    UILabel *titleLB = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(20) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:0];
    [contentView addSubview:titleLB];
    [titleLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(15);
        make.left.equalTo(15);
        make.right.equalTo(-15);
    }];
    self.titleLB = titleLB;
    
    UILabel *timeLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1];
    [contentView addSubview:timeLB];
    [timeLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleLB);
        make.top.equalTo(titleLB.mas_bottom).offset(10);
        make.height.equalTo(20);
    }];
    self.timeLB = timeLB;
    
    UITextView *contentTextView = [[UITextView alloc] init];
    [contentView addSubview:contentTextView];
    [contentTextView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(timeLB.mas_bottom).offset(15);
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.height.equalTo(CGFLOAT_MIN);
    }];
    contentTextView.textContainerInset = UIEdgeInsetsZero;
    contentTextView.textContainer.lineFragmentPadding = 0;
    contentTextView.scrollEnabled = NO;
    contentTextView.editable = NO;
    contentTextView.delegate = self;
    contentTextView.selectable = YES;
    contentTextView.textColor = UIColor.up_textPrimaryColor;
    contentTextView.layoutManager.allowsNonContiguousLayout = NO;
    contentTextView.backgroundColor = FMClearColor;
    self.contentTextView = contentTextView;
    
    
    UIView *line = [[UIView alloc] init];
    line.backgroundColor = UIColor.separatorColor;
    [contentView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(contentTextView.mas_bottom).offset(20);
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.height.equalTo(0.7);
    }];
    
    UILabel *bottomLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:0];
    [contentView addSubview:bottomLB];
    [bottomLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(line.mas_bottom).offset(20);
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.bottom.equalTo(-20-UI_SAFEAREA_BOTTOM_HEIGHT);
    }];
    self.bottomLB = bottomLB;
}


- (void)networkChanged {
    if ([FMNetworkStatusMonitor sharedMonitor].currentStatus == NetworkStatusCellular) {
        if ([[FMHelper getCurrentVC] isEqual:self]) {
            [FMProgressHUD showTextOnlyInView:self.controlView withText:@"当前处于非wifi环境,请注意流量消耗"];
        }
    }
}

- (void)configVideoPlayer {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(networkChanged) name:kNetworkStatusChangedNotification object:nil];
    
    [FMPlayerManager shareManager];
    [FMPlayerManager dismissGlobalVideoPlayView];
    [FMPlayerManager configZFPlayerContainerView:self.coverImg controlView:self.controlView videoUrl:self.model.url];
    [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = NO;
    self.controlView.portraitControlView.slider.isdragging = NO;
    self.controlView.portraitControlView.slider.isdragging = NO;
    
    [self showVideoPlayerInView:self.coverImg];
}

- (void)showVideoPlayerInView:(UIView *)view {
    if (self.model.url.length > 0) {
        self.controlView.prepareShowControlView = YES;
        [FMPlayerManager shareManager].vipXLModel = self.model;
        [FMPlayerManager play];
        [self.controlView showTitle:@""
                     coverURLString:self.model.cover
                     fullScreenMode:ZFFullScreenModeLandscape];
    }
  
}

- (void)configHtmlModel {
    FMDetailHtmlModel *htmlModel = [[FMDetailHtmlModel alloc] init];

    [HTMLParseTool shareInstance].textFontSize = 16.0f;
    [HTMLParseTool shareInstance].lineSpacing = 8.0f;
    [HTMLParseTool shareInstance].paragraphSpacing = 15.0f;
    [HTMLParseTool shareInstance].alignment = NSTextAlignmentLeft;
    [HTMLParseTool shareInstance].imgHeadIndent = NO;
    [HTMLParseTool shareInstance].needSupportBigFont = YES;
    [HTMLParseTool shareInstance].imageWidth = UI_SCREEN_WIDTH - 30;
    WEAKSELF
    __weak typeof(htmlModel) __weakHtmlModel = htmlModel;
    NSString *string = self.model.content.length > 0 ? self.model.content : self.model.summary;
    [[HTMLParseTool shareInstance] parseWithHTMLString:string contentId:[NSString stringWithFormat:@"%ld",self.model.contentId] topics:@[] completeWithAttrStr:^(NSMutableAttributedString *attrStr, NSArray *imgs, BOOL loadCompleted) {
        __weakHtmlModel.attrStr = attrStr;;
        __weakHtmlModel.imgArr = imgs;
        __weakSelf.loadCompleted = loadCompleted;
        
        if (!__weakHtmlModel.attrStr.length) {
            __weakHtmlModel.textHeight = 0;
        } else {
            CGSize size = [__weakHtmlModel.attrStr boundingRectWithSize:CGSizeMake(UI_SCREEN_WIDTH - 30, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading context:nil].size;
            __weakHtmlModel.textHeight = ceil(size.height) + 1;
        }
        
        if (loadCompleted) {
            __weakSelf.contentTextView.attributedText = __weakHtmlModel.attrStr;
            dispatch_async(dispatch_get_main_queue(), ^{
                [__weakSelf.contentTextView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.equalTo(__weakHtmlModel.textHeight);
                }];
            });
        }
    }];
    self.htmlModel = htmlModel;
}

#pragma mark - requestData
- (void)requestData {
    [HttpRequestTool requestZFAndDJDetailWithRequestType:self.requestType contentId:self.contentId serverType:(self.serverType ? [NSString stringWithFormat:@"%zd", self.serverType] : @"") start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD dismiss];
        [SVProgressHUD showImage:nil status:@"请检查您的网络..."];
    } success:^(NSDictionary *dic) {
        [SVProgressHUD dismiss];
        if ([dic[@"data"] isKindOfClass:[NSDictionary class]]) {
            FMVIPXLModel *model = [FMVIPXLModel modelWithDictionary:dic[@"data"]];
            model.requestType = self.requestType;
            model.pageContentType = self.contentType;
            self.title = model.title;
            self.model = model;
        }
    }];
}


- (BOOL)textView:(UITextView *)textView shouldInteractWithURL:(NSURL *)URL inRange:(NSRange)characterRange interaction:(UITextItemInteraction)interaction {
    if (interaction == UITextItemInteractionInvokeDefaultAction) {
        FMLog(@"%@", URL.absoluteString);
        if ([URL.absoluteString hasPrefix:@"img:"]) {
            NSString *imgUrlStr = [URL.absoluteString substringFromIndex:4];
            NSInteger index = [self.htmlModel.imgArr indexOfObject:imgUrlStr];
            HZPhotoBrowser *photoCtrl = [[HZPhotoBrowser alloc] init];
            photoCtrl.imageArray = self.htmlModel.imgArr;
            photoCtrl.currentImageIndex = (int)index;
            [photoCtrl show];
        } else {
            [ProtocolJump jumpWithUrl:URL.absoluteString];
        }
        return NO;
    }
//
    return YES;
}


#pragma mark - setter/getter
- (void)setModel:(FMVIPXLModel *)model {
    _model = model;
    [self.coverImg sd_setImageWithURL:[NSURL URLWithString:model.cover] placeholderImage:[UIImage imageNamed:@"sphc_placeholder"]];
    self.titleLB.text = model.title;
    
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:model.sendTime / 1000];
    if ([date isToday]) {
        self.timeLB.text = [NSString stringFromDate:date format:@"HH:mm"];
    } else if ([date isThisYear]) {
        self.timeLB.text = [NSString stringFromDate:date format:@"MM/dd HH:mm"];
    } else {
        self.timeLB.text = [NSString stringFromDate:date format:@"yyyy/MM/dd HH:mm"];
    }
    
    self.bottomLB.text = model.declareContent;
    
    [self configHtmlModel];
    
    if (self.contentType != 1) {
        [self configVideoPlayer];
    }
}

- (ZFPlayerControlView *)controlView {
    if (!_controlView) {
        _controlView = [[ZFPlayerControlView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_WIDTH*9/16)];
        _controlView.activity.center = _controlView.center;
        _controlView.prepareShowLoading = YES;
        _controlView.prepareShowControlView = YES;
        _controlView.landScapeControlView.isShowSpeed = YES;
        _controlView.portraitControlView.isShowSmallViewPlayBtn = YES;
        [_controlView.portraitControlView layoutSubviews];
        WEAKSELF
        _controlView.portraitControlView.smallBtnClickBlock = ^{
            [FMPlayerManager showGlobalVideoPlayView];
//            [FMPlayerManager shareManager].liveModel.passWord = self.passWord;
            /// 点击小窗口播放后 定位到首页
            [ProtocolJump jumpWithUrl:@"qcyzt://home"];
            [__weakSelf.navigationController popViewControllerAnimated:YES];
        };
    }
    return _controlView;
}

#pragma mark - Private Methods

/**
 * 获取会员中心功能类型
 * 优先使用memberCenterFunctionId，如果没有设置则根据requestType判断
 */
- (MemberCenterFunctionType)getMemberCenterFunctionType {
    // 如果明确设置了memberCenterFunctionId，直接使用
    if (self.memberCenterFunctionId > 0) {
        return (MemberCenterFunctionType)self.memberCenterFunctionId;
    }

    // 否则根据requestType判断
    return [self getFunctionTypeForRequestType:self.requestType];
}

/**
 * 根据requestType获取对应的功能ID
 */
- (MemberCenterFunctionType)getFunctionTypeForRequestType:(MemberCenterJCRequestType)requestType {
    if (requestType == MemberCenterJCRequestTypeNone) {
        return MemberCenterFunctionTypeDragonAnalysis;
    } else if (requestType == MemberCenterJCRequestTypeJCQLReport) {
        return MemberCenterFunctionTypeDragonTraining;
    } else if (requestType == MemberCenterJCRequestTypeJCDJReport) {
        return MemberCenterFunctionTypeMasterStrategy;
    } else if (requestType == MemberCenterJCRequestTypeJCDJReport2) {
        return MemberCenterFunctionTypeMasterResearch;
    } else if (requestType == MemberCenterJCRequestTypeJCQLCourse) {
        return MemberCenterFunctionTypeDragonTactics;
    } else if (requestType == MemberCenterJCRequestTypeJCDJCourse) {
        return MemberCenterFunctionTypeCatchDemonTactics;
    } else if (requestType == MemberCenterJCRequestTypeGSYJCourse) {
        return MemberCenterFunctionTypeChiefClassroom;
    }
    
    return MemberCenterFunctionTypeNone; 
}

@end
