//
//  FMVIPXYViewController.m
//  QCYZT
//
//  Created by shumi on 2023/10/8.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMVIPXYViewController.h"
#import "FMVIPXLANDZFTabCell.h"
#import "FMDakaLiveTabCell.h"
#import "FMLiveModel.h"
#import "FMNoteCell.h"
#import "FMNoteModel.h"
#import "FMVIPXYItemTabVC.h"
#import "HttpRequestTool+MemberCenter.h"
#import "FMAllNoteListModel.h"
#import "FMNoteDetailViewController.h"
#import "FMDakaLiveDetailVC.h"
#import "FMVIPXLModel.h"
#import "FMPDFReaderViewController.h"
#import "FMVIPXLZFDetailVC.h"
#import "NSObject+FBKVOController.h"
#import "MemberCenterVisitManager.h"

@interface FMVIPXYViewController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *bottomView;
@property (nonatomic, strong) NSMutableArray *dataArr;
@property (nonatomic,assign) JCStockPoolType poolType;

@property (nonatomic, assign) CGFloat contentHeight;
@property (nonatomic, strong) dispatch_group_t requestGroup;

@end

@implementation FMVIPXYViewController

- (instancetype)initWithPoolType:(JCStockPoolType)poolType {
    self = [super init];
    if (self) {
        self.poolType = poolType;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.up_contentBgColor;
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.bottom.equalTo(0);
    }];

    [self.tableView.mj_header beginRefreshing];

    // 会员中心访问埋点 - 进入页面
    MemberCenterFunctionType functionType = (self.poolType == JCStockPoolTypeLongTouMiXun) ? MemberCenterFunctionTypeDecisionDragon : MemberCenterFunctionTypeDecisionGold;
    [[MemberCenterVisitManager sharedManager] trackPageEnter:self functionId:functionType];
}

- (void)moreBtnClick:(UIButton *)sender {
    NSInteger requestType = MemberCenterJCRequestTypeNone;
    if (sender.tag > 0) {
        if (self.poolType == JCStockPoolTypeLongTouMiXun) {
            requestType = sender.tag == 1 ? MemberCenterJCRequestTypeJCQLReport : MemberCenterJCRequestTypeJCQLCourse;
        } else {
            if (sender.tag == 1) {
                requestType = MemberCenterJCRequestTypeJCDJReport;
            } else if (sender.tag == 2) {
                requestType = MemberCenterJCRequestTypeJCDJReport2;
            } else if (sender.tag == 3) {
                requestType = MemberCenterJCRequestTypeJCDJCourse;
            }
        }
    }

    FMVIPXYItemTabVC *vc = [[FMVIPXYItemTabVC alloc] initWithType:requestType poolType:self.poolType];
    NSDictionary *dic = self.dataArr[sender.tag];
    vc.title = dic[@"title"];
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - requestData
- (void)requestData {
    self.requestGroup = dispatch_group_create();

    NSInteger type;
    if (self.poolType == JCStockPoolTypeLongTouMiXun) {
        type = 0;
    } else {
        type = 1;
    }

    // 笔记：擒龙解盘、大师跟盘
    NSString *pushTime = [NSString stringFromDate:[NSDate date] format:@"yyyy-MM-dd"];
    dispatch_group_enter(self.requestGroup);
    [HttpRequestTool requestQLNoteListDataWithPageType:type PageSize:3 pageNo:1 pushTime:pushTime start:^{
    } failure:^{
        dispatch_group_leave(self.requestGroup);
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            NSArray *arr = [NSArray modelArrayWithClass:[FMAllNoteListModel class] json:dic[@"data"]];
            for (FMAllNoteListModel *model in arr) {
                model.noteApiDto.rtShowType = [self judgeRTShowTypeWithNoteModel:model];
            }
            NSDictionary *param = self.dataArr[0];
            NSMutableDictionary *dic =  [NSMutableDictionary dictionaryWithDictionary:param];
            [dic setObject:arr forKey:@"data"];
            [self.dataArr replaceObjectAtIndex:0 withObject:dic];
        }
        dispatch_group_leave(self.requestGroup);
    }];

    if (self.poolType == JCStockPoolTypeDaShiTouYan) {
        // 决策点金——大师投研——研报1
        dispatch_group_enter(self.requestGroup);
        [HttpRequestTool requestXLListDataWithRequesType:10  pageSize:1 pageNo:1 start:^{
        } failure:^{
            dispatch_group_leave(self.requestGroup);
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                NSArray *arr = [NSArray modelArrayWithClass:[FMVIPXLModel class] json:dic[@"data"]];
                NSDictionary *param = self.dataArr[1];
                NSMutableDictionary *dic =  [NSMutableDictionary dictionaryWithDictionary:param];
                [dic setObject:arr forKey:@"data"];
                [self.dataArr replaceObjectAtIndex:1 withObject:dic];
            }
            dispatch_group_leave(self.requestGroup);
        }];

        // 决策点金——大师投研——研报2
        dispatch_group_enter(self.requestGroup);
        [HttpRequestTool requestXLListDataWithRequesType:11  pageSize:1 pageNo:1 start:^{
        } failure:^{
            dispatch_group_leave(self.requestGroup);
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                NSArray *arr = [NSArray modelArrayWithClass:[FMVIPXLModel class] json:dic[@"data"]];
                NSDictionary *param = self.dataArr[2];
                NSMutableDictionary *dic =  [NSMutableDictionary dictionaryWithDictionary:param];
                [dic setObject:arr forKey:@"data"];
                [self.dataArr replaceObjectAtIndex:2 withObject:dic];
            }
            dispatch_group_leave(self.requestGroup);
        }];

        // 决策点金——大师投研——课程
        dispatch_group_enter(self.requestGroup);
        [HttpRequestTool requestXLListDataWithRequesType:4 pageSize:1 pageNo:1 start:^{
        } failure:^{
            dispatch_group_leave(self.requestGroup);
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                NSArray *arr = [NSArray modelArrayWithClass:[FMVIPXLModel class] json:dic[@"data"]];
                NSDictionary *param = self.poolType == JCStockPoolTypeLongTouMiXun ? self.dataArr[2] : self.dataArr[3];
                NSMutableDictionary *dic =  [NSMutableDictionary dictionaryWithDictionary:param];
                [dic setObject:arr forKey:@"data"];
                [self.dataArr replaceObjectAtIndex:self.poolType == JCStockPoolTypeLongTouMiXun ? 2 : 3 withObject:dic];
            }
            dispatch_group_leave(self.requestGroup);
        }];
    } else {
        dispatch_group_enter(self.requestGroup);
        // 决策擒龙——龙头密训——研报
        [HttpRequestTool requestXLListDataWithRequesType:9  pageSize:1 pageNo:1 start:^{
        } failure:^{
            dispatch_group_leave(self.requestGroup);
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                NSArray *arr = [NSArray modelArrayWithClass:[FMVIPXLModel class] json:dic[@"data"]];
                NSDictionary *param = self.dataArr[1];
                NSMutableDictionary *dic =  [NSMutableDictionary dictionaryWithDictionary:param];
                [dic setObject:arr forKey:@"data"];
                [self.dataArr replaceObjectAtIndex:1 withObject:dic];
            }
            dispatch_group_leave(self.requestGroup);
        }];
        // 决策擒龙——龙头密训——课程
        dispatch_group_enter(self.requestGroup);
        [HttpRequestTool requestXLListDataWithRequesType:3 pageSize:1 pageNo:1 start:^{
        } failure:^{
            dispatch_group_leave(self.requestGroup);
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                NSArray *arr = [NSArray modelArrayWithClass:[FMVIPXLModel class] json:dic[@"data"]];
                NSDictionary *param = self.poolType == JCStockPoolTypeLongTouMiXun ? self.dataArr[2] : self.dataArr[3];
                NSMutableDictionary *dic =  [NSMutableDictionary dictionaryWithDictionary:param];
                [dic setObject:arr forKey:@"data"];
                [self.dataArr replaceObjectAtIndex:self.poolType == JCStockPoolTypeLongTouMiXun ? 2 : 3 withObject:dic];
            }
            dispatch_group_leave(self.requestGroup);
        }];
    }

    // 所有请求完成后结束刷新
    dispatch_group_notify(self.requestGroup, dispatch_get_main_queue(), ^{
        [self endRefresh];
        [self.tableView reloadData];
        self.tableView.tableFooterView = self.bottomView;
    });
}

/// 判断笔记Cell右上角显示类型
- (NoteListCellRightTopShowType)judgeRTShowTypeWithNoteModel:(FMAllNoteListModel *)model {
    if (model.type == 2 || model.type == 1) { // 直播或课程
        if ([[FMUserDefault getUserId] integerValue] == model.noteApiDto.bignameDto.userId.integerValue) {
            return NoteListCellRightTopShowTypeNone;
        } else {
            return NoteListCellRightTopShowTypeFocusBtn;
        }
    } else { // 笔记
        if (model.noteApiDto.deleteFlag.integerValue < 0) { // 已删除
            if ([[FMUserDefault getUserId] integerValue] == model.noteApiDto.bignameDto.userId.integerValue) {
                if (model.noteApiDto.deleteFlag.integerValue == -1) {
                    return NoteListCellRightTopShowTypeMoreBtn;
                } else {
                    return NoteListCellRightTopShowTypeNone;
                }
            } else {
                return NoteListCellRightTopShowTypeNone;
            }
        } else {
            return NoteListCellRightTopShowTypeMoreBtn;
        }
    }
}

- (void)endRefresh {
    [self.tableView.mj_header endRefreshing];
}

#pragma mark - tableViewDelegate
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.dataArr.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSDictionary *dic = self.dataArr[section];
    NSArray *arr = dic[@"data"];
    return arr.count;
}

-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSDictionary *dic = self.dataArr[indexPath.section];
    NSArray *arr = dic[@"data"];
    if (indexPath.section == 0) {
        FMAllNoteListModel *model = arr[indexPath.row];
        model.noteApiDto.indexPath = indexPath;
        FMNoteCell *cell = [tableView reuseCellClass:[FMNoteCell class]];
        cell.model = model.noteApiDto;
        cell.isLastCell = (indexPath.row == arr.count - 1);
        WEAKSELF
        cell.deleteBlock = ^{
            NSMutableArray *mutableArr = [NSMutableArray arrayWithArray:dic[@"data"]];
            [mutableArr removeObjectAtIndex:indexPath.row];
            NSMutableDictionary *cellDic = [NSMutableDictionary dictionaryWithDictionary:__weakSelf.dataArr[indexPath.section]];
            [cellDic setObject:mutableArr forKey:@"data"];
            [__weakSelf.dataArr replaceObjectAtIndex:indexPath.section withObject:cellDic];
            [tableView reloadData];
        };
        return cell;
    }
    
    FMVIPXLANDZFTabCell *cell = [tableView reuseCellClass:[FMVIPXLANDZFTabCell class]];
    cell.playIcon.hidden = !(indexPath.section == self.dataArr.count - 1);
    cell.timeLB.hidden = !(indexPath.section == self.dataArr.count - 1);
    cell.model = arr[indexPath.row];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        NSDictionary *dic = self.dataArr[indexPath.section];
        NSArray *arr = dic[@"data"];
        FMAllNoteListModel *model = arr[indexPath.row];
        model.noteApiDto.indexPath = indexPath;
        NSArray *commentIds = [model.noteApiDto.topComments valueForKeyPath:@"commentId"];
        NSString *commentCache = [commentIds componentsJoinedByString:@"-"];
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMNoteCell class]) cacheByKey:[NSString stringWithFormat:@"%@-%@-%ld-%@",model.noteApiDto.topType,model.noteApiDto.noteId,model.noteApiDto.updateTime,commentCache] configuration:^(FMNoteCell *cell) {
            cell.model = model.noteApiDto;
        }];
    }
    
    return 120;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    NSDictionary *dic = self.dataArr[indexPath.section];
    NSArray *arr = dic[@"data"];
    if (indexPath.section == 0) {
        FMAllNoteListModel *model = arr[indexPath.row];
        FMNoteDetailViewController *vc = [[FMNoteDetailViewController alloc] init];
        vc.noteId = model.noteApiDto.noteId;
        vc.deleteBlock = ^{
            [self.dataArr removeObjectAtIndex:indexPath.row];
            [self.tableView reloadData];
        };
        if (self.poolType == JCStockPoolTypeLongTouMiXun) {
            vc.memberCenterFunctionId = MemberCenterFunctionTypeDragonAnalysis;
        } else {
            vc.memberCenterFunctionId = MemberCenterFunctionTypeMasterFollow;
        }
        [self.navigationController pushViewController:vc animated:YES];
    } else {
        FMVIPXLModel *model = arr[indexPath.row];
        if (model.contentType.integerValue == 2) {
            FMPDFReaderViewController *vc = [[FMPDFReaderViewController alloc] init];
            vc.title = model.title;
            vc.pdfUrl = model.content;
            // 根据section获取对应的功能ID
            MemberCenterFunctionType functionType = [self getFunctionTypeForSection:indexPath.section];
            vc.memberCenterFunctionId = functionType;
            [self.navigationController pushViewController:vc animated:YES];
        } else {
            NSInteger requestType = MemberCenterJCRequestTypeNone;
            if (self.poolType == JCStockPoolTypeLongTouMiXun) {
                requestType = indexPath.section == 1 ? MemberCenterJCRequestTypeJCQLReport : MemberCenterJCRequestTypeJCQLCourse;
            } else {
                if (indexPath.section == 1) {
                    requestType = MemberCenterJCRequestTypeJCDJReport;
                } else if (indexPath.section == 2) {
                    requestType = MemberCenterJCRequestTypeJCDJReport2;
                } else if (indexPath.section == 3) {
                    requestType = MemberCenterJCRequestTypeJCDJCourse;
                }
            }
            FMVIPXLZFDetailVC *vc = [[FMVIPXLZFDetailVC alloc] initWithType:requestType contentType:model.contentType.integerValue];
            vc.contentId = model.contentId;
            [self.navigationController pushViewController:vc animated:YES];
        }
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    NSDictionary *dataDic = self.dataArr[section];
    NSArray *arr = dataDic[@"data"];
    if(arr.count == 0) {
        return [UIView new];
    }
    CGFloat heigth = section == 0 ? 40 : 40 + 7.5;
    CGFloat origionY = section == 0 ? 0 : 7.5;
    UIView *backView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, heigth)];
    backView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, origionY, UI_SCREEN_WIDTH, 40)];
    view.backgroundColor = UIColor.up_contentBgColor;
    [backView addSubview:view];

    UIView *line = [[UIView alloc] init];
    line.backgroundColor = FMNavColor;
    [view addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(-5);
        make.left.equalTo(15);
        make.size.equalTo(CGSizeMake(3, 12));
    }];
    UI_View_Radius(line, 1.5);

    UILabel *title = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:UIColor.up_textPrimaryColor backgroundColor:UIColor.up_contentBgColor numberOfLines:1];
    NSDictionary *dic = self.dataArr[section];
    title.text = dic[@"title"];
    [view addSubview:title];
    [title mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(line.mas_right).offset(8);
        make.centerY.equalTo(line);
    }];

    UIButton *moreBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [moreBtn setTitle:@"查看更多" forState:UIControlStateNormal];
    [moreBtn setTitleColor:UIColor.up_textSecondary1Color forState:UIControlStateNormal];
    [moreBtn setImage:[UIImage imageWithTintColor:ColorWithHex(0x888888) blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"daily_read_right_arrow")] forState:UIControlStateNormal];
    moreBtn.titleLabel.font = FontWithSize(12);
    moreBtn.tag = section;
    [moreBtn addTarget:self action:@selector(moreBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [view addSubview:moreBtn];
    [moreBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(title);
        make.right.equalTo(-15);
        make.height.equalTo(30);
    }];

    [moreBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageRight imageTitleSpacing:5];

    return backView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    NSDictionary *dic = self.dataArr[section];
    NSArray *arr = dic[@"data"];
    if( arr.count == 0) {
        return CGFLOAT_MIN;
    }
    return section == 0 ? 40 : 40 + 7.5;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - Event
- (void)contactKF {
    NSString *str = [NSString stringWithFormat:@"tel://%@", [FMUserDefault getSeting:AppInit_ContactUs_Phone]];
    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str] options:@{} completionHandler:nil];
}

#pragma mark - setter/getter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped delegate:self dataSource:self viewController:self headerTarget:self headerAction:@selector(requestData)];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_tableView registerCellClass:[FMVIPXLANDZFTabCell class]];
        [_tableView registerCellClass:[FMDakaLiveTabCell class]];
        [_tableView registerCellClass:[FMNoteCell class]];
        _tableView.bounces = YES; // 允许弹性滚动，以支持下拉刷新
        _tableView.backgroundColor = UIColor.up_contentBgColor;
    }
    return _tableView;
}

- (UIView *)bottomView {
    if (!_bottomView) {
        _bottomView = [UIView new];

        UILabel *reminderLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:UIColor.up_textSecondary2Color backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
        [_bottomView addSubview:reminderLabel];
        [reminderLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(15);
            make.right.equalTo(-15);
            make.top.equalTo(15);
        }];
        NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:@"温馨提示：投资有风险，入市需谨慎！入选标的，仅供当日参考，投资者自主决策，自负盈亏。所有推送标的，短期若有浮盈，稳健者可考虑落袋为安，激进者可减仓逐步止盈。\n免责声明：以上信息内容，均来源于市场公开信息和报道，我们力求信息内容客观、公正，但不保证准确性、完整性及在任何市场环境下长期不变。所述观点、建议，仅供参考和学习，投资者不能将其作为投资决策的唯一依据，并应自主决策，独立承担风险，我公司不会作出任何保本或收益承诺。投资有风险，入市需谨慎！四川大决策证券投资顾问有限公司，经营证券期货业务许可证号: 915101067130530143"];
        attrStr.yy_lineSpacing = 3.0f;
        attrStr.yy_paragraphSpacing = 5.0f;
        reminderLabel.attributedText = attrStr;

        UILabel *lxkfLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        [_bottomView addSubview:lxkfLabel];
        [lxkfLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(reminderLabel.mas_bottom).offset(15);
            make.left.equalTo(reminderLabel);
        }];
        lxkfLabel.text = [NSString stringWithFormat:@"大决策客服电话：%@", [FMUserDefault getSeting:AppInit_ContactUs_Phone]];

        UIButton *btn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(12) normalTextColor:UIColor.up_riseColor backgroundColor:FMClearColor title:@"联系客服" image:nil target:self action:@selector(contactKF)];
        [_bottomView addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(-15);
            make.centerY.equalTo(lxkfLabel);
            make.width.equalTo(70);
            make.height.equalTo(25);
            make.bottom.equalTo(-20-UI_SAFEAREA_BOTTOM_HEIGHT);
        }];
        UI_View_BorderRadius(btn, 12.5, 1, UIColor.up_riseColor);

        // 设置临时宽度并触发布局
        _bottomView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, 0);
        [_bottomView layoutIfNeeded];
        CGFloat height = [_bottomView systemLayoutSizeFittingSize:UILayoutFittingCompressedSize].height;
        _bottomView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, height);
    }

    return _bottomView;
}

- (NSMutableArray *)dataArr {
    if (!_dataArr) {
        if (self.poolType == JCStockPoolTypeLongTouMiXun) {
            NSArray *arr = @[
                         @{@"title":@"擒龙解盘",@"data":@[]},
                         @{@"title":@"擒龙训练",@"data":@[]},
                         @{@"title":@"擒龙战法",@"data":@[]}];
            _dataArr = [NSMutableArray arrayWithArray:arr];
        } else {
            NSArray *arr = @[
                             @{@"title":@"大师跟盘",@"data":@[]},
                             @{@"title":@"大师策略",@"data":@[]},
                             @{@"title":@"大师精研",@"data":@[]},
                             @{@"title":@"捉妖战法",@"data":@[]}];
            _dataArr = [NSMutableArray arrayWithArray:arr];
        }

    }
    return _dataArr;
}

- (void)dealloc {
    // 会员中心访问埋点 - 退出页面
    MemberCenterFunctionType functionType = (self.poolType == JCStockPoolTypeLongTouMiXun) ? MemberCenterFunctionTypeDecisionDragon : MemberCenterFunctionTypeDecisionGold;
    [[MemberCenterVisitManager sharedManager] trackPageExit:self functionId:functionType];
}

#pragma mark - Private Methods

/**
 * 根据section获取对应的功能ID
 */
- (MemberCenterFunctionType)getFunctionTypeForSection:(NSInteger)section {
    if (section == 0) {
        return MemberCenterFunctionTypeDragonAnalysis;
    }
    
    if (self.poolType == JCStockPoolTypeLongTouMiXun) {
        if (section == 1) {
            return MemberCenterFunctionTypeDragonTraining;
        } else if (section == 2) {
            return MemberCenterFunctionTypeDragonTactics;
        }
    } else {
        if (section == 1) {
            return MemberCenterFunctionTypeMasterStrategy;
        } else if (section == 2) {
            // 点金投研
            return MemberCenterFunctionTypeMasterResearch;
        } else if (section == 3) {
            // 捉妖战法
            return MemberCenterFunctionTypeCatchDemonTactics;
        }
    }
    
    return MemberCenterFunctionTypeNone;
}

@end
