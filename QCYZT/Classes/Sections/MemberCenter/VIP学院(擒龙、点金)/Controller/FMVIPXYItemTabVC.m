//
//  FMVIPXYItemTabVC.m
//  QCYZT
//
//  Created by shumi on 2023/10/9.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMVIPXYItemTabVC.h"
#import "FMVIPXLANDZFTabCell.h"
#import "FMNoteCell.h"
#import "FMDakaLiveTabCell.h"
#import "FMLiveModel.h"
#import "FMNoteCell.h"
#import "FMNoteModel.h"
#import "HttpRequestTool+MemberCenter.h"
#import "FMDakaLiveDetailVC.h"
#import "FMNoteDetailViewController.h"
#import "FMVIPXLModel.h"
#import "FMVIPXLZFDetailVC.h"
#import "FMPDFReaderViewController.h"
#import "FMMemberCenterConstants.h"
#import "MemberCenterVisitManager.h"

@interface FMVIPXYItemTabVC ()<UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *dataArr;
// 擒龙 点金
@property (nonatomic, assign) JCStockPoolType poolType;
@property (nonatomic,assign) MemberCenterJCRequestType requestType;
@property (nonatomic,assign) NSInteger pageSize;
@property (nonatomic,assign) NSInteger page;
@property (nonatomic,assign) NSInteger currentPage;

@end

@implementation FMVIPXYItemTabVC

- (instancetype)initWithType:(MemberCenterJCRequestType)requestType poolType:(JCStockPoolType)poolType {
    self = [super init];
    if (self) {
        self.requestType = requestType;
        self.poolType = poolType;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.up_contentBgColor;
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];

    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 20;
    [self.tableView.mj_header beginRefreshing];

    // 会员中心访问埋点 - 进入页面
    MemberCenterFunctionType functionType = [self getFunctionTypeForRequestType:self.requestType];
    if (functionType != MemberCenterFunctionTypeNone) {
        [[MemberCenterVisitManager sharedManager] trackPageEnter:self functionId:functionType];
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self configNavWhiteColorWithCloseSEL:@selector(backArrowClicked)];
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)headerAction {
    self.page = 1;
    [self requestData];
}

- (void)footerAction {
    self.page++;
    [self requestData];
}

- (void)endRefreshForFailure {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.page = self.currentPage;
}

- (void)endRefreshForSuccess {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.currentPage = self.page;
}

#pragma mark - request
- (void)requestData {
    NSInteger type;
    if (self.poolType == JCStockPoolTypeLongTouMiXun) {
        type = 0;
    } else {
        type = 1;
    }
    
    if (self.requestType == MemberCenterJCRequestTypeNone) {
        // 擒龙笔记 点金笔记
        [HttpRequestTool requestQLNoteListDataWithPageType:type PageSize:self.pageSize pageNo:self.page pushTime:@"" start:^{
        } failure:^{
            [self endRefreshForFailure];
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                [self endRefreshForSuccess];
                // 根据total总数 确定分页的页码
                NSInteger pageNum = (([dic[@"total"] integerValue] + self.pageSize - 1) / self.pageSize);
                
                if (self.page == 1) {
                    [self.tableView.mj_footer resetNoMoreData];
                    [self.dataArr removeAllObjects];
                }
                NSArray *dataArr = [NSArray modelArrayWithClass:[FMAllNoteListModel class] json:dic[@"data"]];
                // 移除重复数据
                [self removeRepeatDataWithArray:dataArr];
                
                if (self.page == pageNum) {
                    [self.tableView.mj_footer endRefreshingWithNoMoreData];
                }
                
                // 占位显示
                if (self.dataArr.count) {
                    [self.tableView dismissNoDataView];
                } else {
                    [self.tableView showNoDataViewWithImage:ImageWithName(@"common_nodata") string:@"暂无内容" attributes:nil offsetY:60];
                }
                self.tableView.mj_footer.hidden = !self.dataArr.count;
                [self.tableView reloadData];
            } else {
                [self endRefreshForFailure];
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    }  else {
        [HttpRequestTool requestXLListDataWithRequesType:self.requestType pageSize:self.pageSize pageNo:self.page start:^{
        } failure:^{
            [self endRefreshForFailure];
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                [self endRefreshForSuccess];
                if ([dic[@"data"] isKindOfClass:[NSArray class]]) {
                    // 根据total总数 确定分页的页码
                    NSInteger pageNum = (([dic[@"total"] integerValue] + self.pageSize - 1) / self.pageSize);
                    if (self.page == 1) {
                        [self.tableView.mj_footer resetNoMoreData];
                        [self.dataArr removeAllObjects];
                    }
                    
                    if (self.page == pageNum) {
                        [self.tableView.mj_footer endRefreshingWithNoMoreData];
                    }
                    NSArray *arr = [NSArray modelArrayWithClass:[FMVIPXLModel class] json:dic[@"data"]];
                    [self.dataArr addObjectsFromArray:arr];
                    [self.tableView reloadData];
                    
                    // 占位显示
                    if (self.dataArr.count) {
                        [self.tableView dismissNoDataView];
                    } else {
                        [self.tableView showNoDataViewWithImage:ImageWithName(@"common_nodata") string:@"暂无内容" attributes:nil offsetY:60];
                    }
                    self.tableView.mj_footer.hidden = !self.dataArr.count;
                    [self.tableView reloadData];
                } else {
                    [self endRefreshForFailure];
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }
        }];
    }
}

- (void)removeRepeatDataWithArray:(NSArray *)array {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    for (FMAllNoteListModel *model in array) {
        model.noteApiDto.rtShowType = [self judgeRTShowTypeWithNoteModel:model];
        [dic setObject:model.noteApiDto forKey:model.noteApiDto.noteId];
    }
    
    for (FMAllNoteListModel *model in self.dataArr.reverseObjectEnumerator) {
        if ([dic valueForKey:model.noteApiDto.noteId]) {
            [self.dataArr removeObject:model];
        }
    }
    [self.dataArr addObjectsFromArray:array];
}

/// 判断笔记Cell右上角显示类型
- (NoteListCellRightTopShowType)judgeRTShowTypeWithNoteModel:(FMAllNoteListModel *)model {
    if (model.type == 2 || model.type == 1) { // 直播或课程
        if ([[FMUserDefault getUserId] integerValue] == model.noteApiDto.bignameDto.userId.integerValue) {
            return NoteListCellRightTopShowTypeNone;
        } else {
            return NoteListCellRightTopShowTypeFocusBtn;
        }
    } else { // 笔记
        if (model.noteApiDto.deleteFlag.integerValue < 0) { // 已删除
            if ([[FMUserDefault getUserId] integerValue] == model.noteApiDto.bignameDto.userId.integerValue) {
                if (model.noteApiDto.deleteFlag.integerValue == -1) {
                    return NoteListCellRightTopShowTypeMoreBtn;
                } else {
                    return NoteListCellRightTopShowTypeNone;
                }
            } else {
                return NoteListCellRightTopShowTypeNone;
            }
        } else {
            return NoteListCellRightTopShowTypeMoreBtn;
        }
    }
}

#pragma mark - tableViewDelegate
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  if (self.requestType == MemberCenterJCRequestTypeNone) {
        FMAllNoteListModel *model = self.dataArr[indexPath.row];
        model.noteApiDto.indexPath = indexPath;
        FMNoteCell *cell = [tableView reuseCellClass:[FMNoteCell class]];
        cell.model = model.noteApiDto;
        cell.isLastCell = (indexPath.row == self.dataArr.count - 1);
        WEAKSELF;
        cell.deleteBlock = ^{
            [__weakSelf.dataArr removeObjectAtIndex:indexPath.row];
            if (__weakSelf.dataArr == 0) {
                [tableView.mj_header beginRefreshing];
            }
            [tableView reloadData];
        };
        return cell;
    }
    FMVIPXLANDZFTabCell *cell = [tableView reuseCellClass:[FMVIPXLANDZFTabCell class]];
    cell.playIcon.hidden = cell.timeLB.hidden = !(self.requestType == MemberCenterJCRequestTypeJCQLCourse || self.requestType == MemberCenterJCRequestTypeJCQLCourse);
    cell.model = self.dataArr[indexPath.row];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
 if (self.requestType == MemberCenterJCRequestTypeNone) {
        FMAllNoteListModel *model = self.dataArr[indexPath.row];
        model.noteApiDto.indexPath = indexPath;
        NSArray *commentIds = [model.noteApiDto.topComments valueForKeyPath:@"commentId"];
        NSString *commentCache = [commentIds componentsJoinedByString:@"-"];
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMNoteCell class]) cacheByKey:[NSString stringWithFormat:@"%@-%ld-%@",model.noteApiDto.noteId,model.noteApiDto.updateTime,commentCache] configuration:^(FMNoteCell *cell) {
            cell.model = model.noteApiDto;
        }];
    }
    return 120;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
   if (self.requestType == MemberCenterJCRequestTypeNone) {
        FMAllNoteListModel *model = self.dataArr[indexPath.row];
        if (model.type == 1) {
            //进直播详情
            [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
                FMDakaLiveDetailVC *vc = [[FMDakaLiveDetailVC alloc] initWithRoomId:model.liveroomApiDto.liveId];
                vc.liveStatusBlock = ^(NSString *status) {
                    FMNoteCell *cell = [tableView cellForRowAtIndexPath:indexPath];
                    model.liveroomApiDto.status = status;
                    cell.model = model.noteApiDto;
                };
                [self.navigationController pushViewController:vc animated:YES];
            }];
        } else {
            FMNoteDetailViewController *vc = [[FMNoteDetailViewController alloc] init];
            vc.noteId = model.noteApiDto.noteId;
            if (self.poolType == JCStockPoolTypeLongTouMiXun) {
                vc.memberCenterFunctionId = MemberCenterFunctionTypeDragonAnalysis;
            } else {
                vc.memberCenterFunctionId = MemberCenterFunctionTypeMasterFollow;
            }
            [self.navigationController pushViewController:vc animated:YES];
        }
    } else {
        FMVIPXLModel *model = self.dataArr[indexPath.row];
        if (model.contentType.integerValue == 2) {
            FMPDFReaderViewController *vc = [[FMPDFReaderViewController alloc] init];
            vc.title = model.title;
            vc.pdfUrl = model.content;
            // 设置会员中心功能ID
            MemberCenterFunctionType functionType = [self getFunctionTypeForRequestType:self.requestType];
            vc.memberCenterFunctionId = functionType;
            [self.navigationController pushViewController:vc animated:YES];
        } else {
            FMVIPXLZFDetailVC *vc = [[FMVIPXLZFDetailVC alloc] initWithType:self.requestType contentType:model.contentType.integerValue];
            vc.contentId = model.contentId;
            [self.navigationController pushViewController:vc animated:YES];
        }
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - setter/getter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped delegate:self dataSource:self viewController:self headerTarget:self headerAction:@selector(headerAction) footerTarget:self footerAction:@selector(footerAction)];
        _tableView.backgroundColor = UIColor.up_contentBgColor;
        _tableView.separatorColor = UIColor.fm_sepline_color;
        _tableView.separatorInset = UIEdgeInsetsMake(0, 15, 0, 15);
        [_tableView registerCellClass:[FMVIPXLANDZFTabCell class]];
        [_tableView registerCellClass:[FMNoteCell class]];
        [_tableView registerCellClass:[FMDakaLiveTabCell class]];
        _tableView.tableFooterView = [UIView new];
        _tableView.mj_footer.hidden = YES;
    }
    return _tableView;
}

- (NSMutableArray *)dataArr {
    if (!_dataArr) {
        _dataArr = [NSMutableArray array];
    }
    return _dataArr;
}

- (void)dealloc {
    // 会员中心访问埋点 - 退出页面
    MemberCenterFunctionType functionType = [self getFunctionTypeForRequestType:self.requestType];
    if (functionType != MemberCenterFunctionTypeNone) {
        [[MemberCenterVisitManager sharedManager] trackPageExit:self functionId:functionType];
    }
}

#pragma mark - Private Methods

/**
 * 根据requestType获取对应的功能ID
 */
- (MemberCenterFunctionType)getFunctionTypeForRequestType:(NSInteger)requestType {
    if (requestType == MemberCenterJCRequestTypeNone) {
        if (self.poolType == JCStockPoolTypeLongTouMiXun) {
            return MemberCenterFunctionTypeDragonAnalysis;
        } else {
            return MemberCenterFunctionTypeMasterFollow;
        }
    } else if (requestType == MemberCenterJCRequestTypeJCQLReport) {
        return MemberCenterFunctionTypeDragonTraining;
    } else if (requestType == MemberCenterJCRequestTypeJCDJReport) {
        return MemberCenterFunctionTypeMasterStrategy;
    } else if (requestType == MemberCenterJCRequestTypeJCDJReport2) {
        return MemberCenterFunctionTypeMasterResearch;
    } else if (requestType == MemberCenterJCRequestTypeJCQLCourse) {
        return MemberCenterFunctionTypeDragonTactics;
    } else if (requestType == MemberCenterJCRequestTypeJCDJCourse) {
        return MemberCenterFunctionTypeCatchDemonTactics;
    }
    
    return MemberCenterFunctionTypeNone; // 默认返回无功能类型
}

@end
