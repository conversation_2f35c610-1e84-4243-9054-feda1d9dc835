//
//  FMVIPXLZFDetailVC.h
//  QCYZT
//
//  Created by shumi on 2023/10/17.
//  Copyright © 2023 LZKJ. All rights reserved.
//  训练战法详情

#import <UIKit/UIKit.h>
#import "FMMemberCenterConstants.h"

NS_ASSUME_NONNULL_BEGIN

@interface FMVIPXLZFDetailVC : UIViewController
/// 初始化
/// - Parameters:
///   - contentType: contentType == 1 内容    contentType == 其他 战法视频
- (instancetype)initWithType:(NSInteger)requestType contentType:(NSInteger)contentType;

@property (nonatomic,assign) NSInteger contentId;

// 从股市赢家和股市赢家定制版进来要传
@property (nonatomic, assign) JCVIPType serverType;

// 会员中心访问埋点功能ID（可选，如果不设置则根据requestType自动判断）
@property (nonatomic, assign) NSInteger memberCenterFunctionId;


- (instancetype)init UNAVAILABLE_ATTRIBUTE;
@end

NS_ASSUME_NONNULL_END
