//
//  FMNoteDetailViewController.m
//  QCYZT
//
//  Created by th on 16/12/21.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import "FMNoteDetailViewController.h"
#import "FMNoteModel.h"
#import "MemberCenterVisitManager.h"
#import "FMCommentFrameModel.h"
#import "FMCommentCell.h"
#import "FMCommentModel.h"
#import "FMNoteDetailHeaderCell.h"
#import "FMNoteDetailLiveHeaderTabCell.h"
#import "FMNoteRetweetsDetailTopCell.h"
#import "FMNoteDetailHeaderThirdCell.h"
#import "FMDetailBottomView.h"
#import "EnablePayModel.h"
#import "FMPayTool.h"
#import "PaymentView.h"
#import "HttpRequestTool+DailyTask.h"
#import "FMTaskConfigModel.h"
#import "FMDetailHtmlCell.h"
#import "HTMLParseTool.h"
#import "FMNoteMentionStockView.h"
#import "FMNoteDetailCommentInputView.h"
#import "FMSearchImportStockTipView.h"
#import "FMNoteDetailNavView.h"
#import "FMNoteDetailRecommendedNoteCell.h"
#import "FMDakaLiveDetailVC.h"
#import "FMPaySuccessPopView.h"
#import "FMNoteStatisticsCacheTool.h"
#import "NSObject+FBKVOController.h"
#import "FMBigCastFocusReminderView.h"

//视频播放
#import "ZFPlayerController.h"
#import "ZFAVPlayerManager.h"
#import "ZFPlayerControlView.h"
#import "FMProgressHUD.h"
#import "FMNoteDetailPlayerControlView.h"
#import "CountDown.h"
#import "CircleBottomAlertController.h"
#import "FMTopicConvertTool.h"
#import "FMNoteConst.h"
#import "HttpRequestTool+Pay.h"
#import "FMPlayerManager.h"
#import "FMNoteRewardAmountView.h"
#import "FMSendCouponView.h"
#import "HttpRequestTool+UserCenter.h"
#import "FMCouponUseGuideVC.h"
#import "FMCouponTableModel.h"
#import "FMAskCodePopView.h"


@interface FMNoteDetailViewController() <UITableViewDelegate, UITableViewDataSource, FMNoteDetailHeaderCellDelegate, FMCommentCellDelegate, FMDetailBottomViewDelegate,NoteDetailPlayerControlViewDelegate>

@property (nonatomic, strong) FMNoteDetailNavView *navView;
/// 表视图
@property (nonatomic, strong) UITableView *tableView;
/// 提及股票
@property (nonatomic, strong) ZLTagLabel *mentionStockLabel;
/// 提及股票
@property (nonatomic, strong) FMNoteMentionStockView *mentionStockView;
/// 底部视图 (赞赏 评论输入框 分享 收藏 点赞)
@property (nonatomic, strong) FMDetailBottomView *bottomView;
/// 评论UI
@property (nonatomic, strong) FMNoteDetailCommentInputView *commentView;
/// 赞赏UI
@property (nonatomic, strong) FMNoteRewardAmountView *rewardView;
/// 笔记详情
@property (nonatomic, strong) FMNoteModel *detailModel;
/// 内容html模型
@property (nonatomic, strong) FMDetailHtmlModel *htmlModel;
/// 推荐笔记
@property (nonatomic, strong) NSMutableArray<FMAllNoteListModel *> *recommendNotes;
/// 评论数据源
@property (nonatomic, strong) NSMutableArray<FMCommentFrameModel *> *commentFrames;
/// 页码
@property (nonatomic, assign) NSUInteger page;
/// 当前缓存页码
@property (nonatomic, assign) NSUInteger currentPage;
/// 分页size
@property (nonatomic, assign) NSUInteger pageSize;
/// 总评论数
@property (nonatomic, assign) NSInteger totalComments;
/// 在接收到通知（登录、退出登录、支付等）后刷新页面（只刷新详情、不刷新评论）的标识
@property (nonatomic, assign) BOOL needsRefresh;
/// 判断数值为2，即详情接口和评论接口都请求到数据（不论成功或失败）才刷新一次页面
@property (nonatomic, assign) NSInteger refreshStatistilNum;
/// HTML model加载完成
@property (nonatomic,assign) BOOL loadCompleted;
/// HTML model加载完成 且评论数据获取完成 用来定位页面是否需要滚动评论区的回调
@property (nonatomic,copy) void (^reloadPageBlock)();

@property (nonatomic, assign) CGFloat scrollOffsetY;

// 提问输入框
@property (nonatomic, strong) FMAskCodePopView *askCommentView;
@property (nonatomic,copy) NSString *questionContent;
@property (nonatomic, strong) FMSearchStockModel *choosedStockModel;

// 关注提醒
@property (nonatomic, strong) FMBigCastFocusReminderView *focusReminderView;

//播放相关
/// 播放器
@property (nonatomic, strong) ZFPlayerController *player;
/// 播放器控制层
@property (nonatomic, strong) FMNoteDetailPlayerControlView *controlView;
/// 播放管理类
@property (nonatomic, strong) ZFAVPlayerManager *playerManager;
/// 登录状态改变时,试看结束或者重新试看时 当前试看播放状态记录字段
@property (nonatomic,assign) NSInteger currentPalyStatus;

// 页面浏览计时
/// 计时数
@property (nonatomic, assign) NSInteger pageBrowseTime;
/// 是否需要增加计数标志，1表示需要，2表示当前不需要，0表示不再需要，这里需要三个状态。
@property (nonatomic, assign) NSInteger needAddTimeCountFlag;
/// 关注提醒Flag， 0 停止  1运行 2暂停
@property (nonatomic, assign) NSInteger focusReminderFlag;


// 是否正在展示提及股票
@property (nonatomic, assign) BOOL isShowingMentionView;

@end

@implementation FMNoteDetailViewController

#pragma mark - 生命周期
- (void)viewDidLoad {
    [super viewDidLoad];

    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 20;
    self.refreshStatistilNum = 0;
    self.view.backgroundColor = UIColor.up_contentBgColor;
    
    [self.view addSubview:self.navView];
    [self.navView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(0);
        make.height.equalTo(@(UI_SAFEAREA_TOP_HEIGHT));
    }];
        
    [self.view addSubview:self.bottomView];
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(0);
        make.height.equalTo(@(DetailBottomViewHeight));
    }];
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(0);
        make.top.equalTo(self.navView.mas_bottom);
        make.bottom.equalTo(self.bottomView.mas_top);
    }];
    self.bottomView.delegate = self;
    self.bottomView.hidden = YES;
    self.tableView.hidden = YES;

    
    [self.view addSubview:self.mentionStockLabel];
    [self.mentionStockLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@3);
        make.bottom.equalTo(self.bottomView.mas_top).offset(-10);
        make.height.equalTo(@36);
    }];
    self.mentionStockLabel.hidden = YES;
    
    [self.view addSubview:self.focusReminderView];
    [self.focusReminderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.bottom.equalTo(self.tableView.mas_bottom).offset(-20);
        make.height.equalTo(70);
    }];
    self.focusReminderView.hidden = YES;
    
    WEAKSELF;
    
    self.reloadPageBlock = ^() {
        if (__weakSelf.detailModel.noteType.integerValue == 5) {
            return;
        }
        if (__weakSelf.loadCompleted && __weakSelf.commentFrames.count > 0  && __weakSelf.scrollToComment) {
            __weakSelf.scrollToComment = NO;
            // 即使commentFrames有值 但是在numberOfRowsInSection代理方法执行前调用下面执行滚动方法会产生奔溃 所以在执行之前先判断分区中是否存在cell
            NSInteger rows = [__weakSelf.tableView numberOfRowsInSection:1];
            if (rows > 0) {
                [__weakSelf.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:1] atScrollPosition:UITableViewScrollPositionTop animated:NO];
            }
        }
    };
    
    [self requestNoteDetail];
    [self requestRecommendNoteList];
    [self requestNoteComment];
    
    
    self.pageBrowseTime = 0;
    self.needAddTimeCountFlag = 1;
    self.focusReminderFlag = 1;
    
    // 注册通知
    [self registerNotification];
    
    // 统计阅读
    if (![FMNoteStatisticsCacheTool isCompletedWithType:1 noteId:self.noteId]) {
        [self noteStatisticsWithType:1];
    }

    // 会员中心访问埋点 - 进入页面
    if (self.memberCenterFunctionId > 0) {
        [[MemberCenterVisitManager sharedManager] trackPageEnter:self functionId:(MemberCenterFunctionType)self.memberCenterFunctionId];
    }
}

/// 注册通知
- (void)registerNotification {
    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(handleLoginStatusNotification) monitorAuthLogin:NO];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleLoginStatusNotification) name:kRechargeSuccess object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(notificationViewClick:) name:kNotificationViewClickNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appWillResignActive) name:UIApplicationWillResignActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appDidBecomeActive) name:UIApplicationDidBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(requestRewardRecord) name:KNoteRewardPaySuccess object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(wxPayNoteSuccess:) name:kNoteWxPaySuccess object:nil];
}

- (void)dealloc {
    FMLog(@"%s", __func__);

    // 会员中心访问埋点 - 退出页面
    if (self.memberCenterFunctionId > 0) {
        [[MemberCenterVisitManager sharedManager] trackPageExit:self functionId:(MemberCenterFunctionType)self.memberCenterFunctionId];
    }

    if (self.player) {
        NSMutableDictionary *playTimeDic = [NSMutableDictionary dictionaryWithDictionary:[FMUserDefault getUnArchiverDataForKey:KNotePlayerCachePlayTime]];
        NSString *key = [NSString stringWithFormat:@"note%@",self.detailModel.noteId];
        [playTimeDic setObject:[NSNumber numberWithDouble:self.player.currentTime] forKey:key];
        // 记录当前播放进度
        [FMUserDefault setArchiverData:playTimeDic forKey:KNotePlayerCachePlayTime];
    }
    self.player = nil;
    /// 如果存在小窗口播放 退出页面时 显示小窗口播放窗口
    if([FMPlayerManager shareManager].isSmallViewPlay) {
        [FMPlayerManager play];
    } 
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewWillAppear:(BOOL)animated {
    self.selfNavigationBarHidden = YES;
    [super viewWillAppear:animated];
    self.player.viewControllerDisappear = NO;
    if (self.needsRefresh) {
        self.needsRefresh = NO;
        
        self.refreshStatistilNum = 0;
        self.page = 1;
        [self requestNoteDetail];
        [self requestRecommendNoteList];
        [self requestNoteComment];
    }
    
    // 修改状态栏颜色
    [self scrollViewDidScroll:self.tableView];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];    
    if (self.needAddTimeCountFlag == 2) {
        self.needAddTimeCountFlag = 1;
        self.focusReminderFlag = 1;
    }
    
    if (self.isShowingMentionView) {
        [_mentionStockView showWithAnimation:NO];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    if (![FMHelper getCurrentVC].presentingViewController) { //不是推出控制器
        self.player.viewControllerDisappear = YES;
    }
    
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    
    if (self.needAddTimeCountFlag == 1) {
        self.needAddTimeCountFlag = 2;
        self.focusReminderFlag = 2;
    }
}

#pragma  mark - private
/// 收到通知 点击通知栏协议跳转  如果是横屏 需要回到竖屏 并做相应跳转
- (void)notificationViewClick:(NSNotification *)noti {
    self.player.viewControllerDisappear = YES;
    if (self.player.isFullScreen) {
        [self.player enterFullScreen:NO animated:YES];
    }
}

/// 配置播放器
- (void)configPlayer {
    if (!self.player) {
        FMNoteDetailLiveHeaderTabCell *cell = [self.tableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:0]];
        self.playerManager = [[ZFAVPlayerManager alloc] init];
        self.player = [[ZFPlayerController alloc] initWithPlayerManager:self.playerManager containerView:cell.liveBgImageV];
        self.player.controlView = self.controlView;
        self.player.shouldAutoPlay = NO;
        self.player.allowOrentitaionRotation = NO;
        @zf_weakify(self)
        // 播放结束回调
        self.player.playerDidToEnd = ^(id  _Nonnull asset) {
            @zf_strongify(self)
            [self.player seekToTime:0 completionHandler:nil];
            [self.player.currentPlayerManager pause];
        };
        // 旋转完成监听回调
        self.player.orientationDidChanged = ^(ZFPlayerController * _Nonnull player, BOOL isFullScreen) {
            @zf_strongify(self)
            if (!isFullScreen) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self scrollViewDidScroll:self.tableView];
                });
            }
            if (self.currentPalyStatus == 2) { //试看结束
                if (isFullScreen) {
                    [self.player enterFullScreen:NO animated:YES];
                }
            }
        };
        /// 展示播放器
        [self showVideoPlayerInView:cell.liveBgImageV];
    }
    self.controlView.model = self.detailModel;
    
    // 播放状态监听
    self.player.playerPlayStateChanged = ^(id<ZFPlayerMediaPlayback>  _Nonnull asset, ZFPlayerPlaybackState playState) {
        if (playState == ZFPlayerPlayStatePlaying) {
            [FMPlayerManager pause];
        } else if (playState == ZFPlayerPlayStatePaused) {
            [FMPlayerManager play];
        }
    };
    
    WEAKSELF
    // FMPlayerManager播放类 播放状态监听
    [FMPlayerManager shareManager].player.playerPlayStateChanged = ^(id<ZFPlayerMediaPlayback>  _Nonnull asset, ZFPlayerPlaybackState playState) {
        STRONGSELF
        if(strongSelf.player.isFullScreen) {
            // 全屏状态下 小窗口停止播放
            [FMPlayerManager pause];
        } else {
            if (playState == ZFPlayerPlayStatePlaying) {
                [strongSelf.player.currentPlayerManager pause];
            } else if (playState == ZFPlayerPlayStatePaused) {
                [strongSelf.player.currentPlayerManager play];
            }
        }
    };
    
}

/// 构造htmlModel
- (void)createHtmlModel {
    FMDetailHtmlModel *model = [[FMDetailHtmlModel alloc] init];
    if (self.detailModel.noteType.integerValue == 3 || self.detailModel.noteType.integerValue == 1) { // 长文
        if (self.detailModel.authority.integerValue != 1) { // 未付费
            model.HTMLString = @"";
        } else {
            if (self.detailModel.noteContent.length) {
                model.HTMLString = [self combineContent:self.detailModel.noteContent declare:self.detailModel.declareContent];
            } else if (self.detailModel.noteContent.length) {
                model.HTMLString = [self combineContent:self.detailModel.noteContent declare:self.detailModel.declareContent];
            } else {
                model.HTMLString = @"";
            }
        }
    } else if (self.detailModel.noteType.integerValue == 5) { //视频 取note_summry
        model.HTMLString = [self combineContent:self.detailModel.noteSummary declare:self.detailModel.declareContent];
    }
        
//    NSString *path = [[NSBundle mainBundle] pathForResource:@"index" ofType:@"html"];
//    NSString *htmlString = [NSString stringWithContentsOfFile:path encoding:NSUTF8StringEncoding error:nil];
//    model.HTMLString = htmlString;
    
    [HTMLParseTool shareInstance].textFontSize = 18.0f;
    [HTMLParseTool shareInstance].lineSpacing = 10.0f;
    [HTMLParseTool shareInstance].paragraphSpacing = 0.0f;
    [HTMLParseTool shareInstance].alignment = NSTextAlignmentLeft;
    [HTMLParseTool shareInstance].imgHeadIndent = NO;
    [HTMLParseTool shareInstance].needSupportBigFont = YES;
    [HTMLParseTool shareInstance].imageWidth = UI_SCREEN_WIDTH - 30;
    WEAKSELF
    __weak typeof(model) __weakModel = model;
    
    [[HTMLParseTool shareInstance] parseWithHTMLString:[FMTopicConvertTool replaceTopicIdToTopicTagWithHtmlStr:model.HTMLString topics:self.detailModel.topicList] contentId:self.detailModel.noteId topics:self.detailModel.topicList completeWithAttrStr:^(NSMutableAttributedString *attrStr, NSArray *imgs, BOOL loadCompleted) {
        __weakModel.attrStr = attrStr;;
        __weakModel.imgArr = imgs;
        __weakSelf.loadCompleted = loadCompleted;
        
        if (!__weakModel.attrStr.length) {
            __weakModel.textHeight = 0;
            [__weakSelf.tableView reloadData];
        } else {
            CFAbsoluteTime startTime = CFAbsoluteTimeGetCurrent();
            // 在iOS17上，boundingRectWithSize计算耗时太久
            dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                CGSize size = [__weakModel.attrStr boundingRectWithSize:CGSizeMake(UI_SCREEN_WIDTH - 30, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading context:nil].size;
                __weakModel.textHeight = size.height;
                dispatch_async(dispatch_get_main_queue(), ^{
                    [__weakSelf.tableView reloadData];
                });
            });
            
            if (loadCompleted && __weakSelf.scrollToComment) {
                if (__weakSelf.reloadPageBlock) {
                    __weakSelf.reloadPageBlock();
                }
            }
        }
    }];
    self.htmlModel = model;
}

// 将笔记内容（note_content），笔记的免责声明（declare_content），合并成初始的html
/**
 2022.1.6 翟书觅
 笔记详情直播信息展示在内容下方 免责申明上方  所以将笔记免责声明分离出来了
 */
- (NSString *)combineContent:(NSString *)content declare:(NSString *)declare {
    if (!declare.length) {
        return content;
    }

    if (self.detailModel.kfInfo.tpl.length) { // 笔记中要拼接添加客服
        NSData *wxData = [NSData dataWithContentsOfFile:[[NSBundle mainBundle] pathForResource:@"wxzl" ofType:@"html"]];
        NSString *wxStr = [[NSString alloc] initWithData:wxData encoding:NSUTF8StringEncoding];
        NSString *wxNum;
        if (self.detailModel.kfInfo.number) {
            wxNum = [self.detailModel.kfInfo.number stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
        }
        NSString *kfNumberStr = [NSString stringWithFormat:@"qcyzt://wxkf?number=%@", wxNum];
        NSString *appendStr = [NSString stringWithFormat:wxStr, kfNumberStr, self.detailModel.kfInfo.tpl];
        
        NSString *fullStr = [NSString stringWithFormat:@"%@%@", content, appendStr];
        return fullStr;
    } else {
        NSString *str = [NSString stringWithFormat:@"%@<div style=\"height:5px;width:100%%\"></div>", content];
        return str;
    }
}

- (void)endRefreshForFailure {
    if (self.page > 1) { // 上拉失败
        [self.tableView.mj_footer endRefreshing];
    } else {
        [self.tableView.mj_header endRefreshing];
    }
    
    self.page = self.currentPage;
}

// 发起问股
- (void)startAskCode:(UIButton *)sender {
    WEAKSELF;
    // 2.余额够的话判断风险提示书
    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:__weakSelf.detailModel.bignameDto.userId certCode:__weakSelf.detailModel.bignameDto.certCode clickView:sender confirmOperation:^{
        // 3.显示提问框
        UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
        [keyWindow addSubview:__weakSelf.askCommentView];
        __weakSelf.askCommentView.publisLb.textColor = UIColor.up_textPrimaryColor;
        __weakSelf.askCommentView.publisLb.text = [NSString stringWithFormat:@"向%@老师提问", __weakSelf.detailModel.bignameDto.userName];
        __weakSelf.askCommentView.placeholderText = @"输入您的问题。若已买入该股票，可告知“买入价格”和“仓位情况”，更便于老师根据您的实际情况给予建议。\n若超过预期时间未解答，提问费用或卡券将会自动退回。";
        __weakSelf.askCommentView.askPrice = __weakSelf.detailModel.bignameDto.answerPrice;
        
        __weakSelf.askCommentView.bignameId = __weakSelf.detailModel.bignameDto.userId;
        __weakSelf.askCommentView.consumeType = 2;
        [__weakSelf.askCommentView requestCouponList];
        __weakSelf.askCommentView.publishAskCodeBlock = ^(NSString *content, FMSearchStockModel *stockModel) {
            __weakSelf.questionContent = content;
            __weakSelf.choosedStockModel = stockModel;
            [__weakSelf checkWaitTime];
        };
    }];
}

/// 页面浏览计时
- (void)pageBrowseTimeCountDownWithStartTime:(long long)startTime {
    [self.KVOController unobserve:[CountDownShareInstance shareInstance] keyPath:DifferentValue];
    self.pageBrowseTime = 0;
    WEAKSELF
    [self.KVOController observe:[CountDownShareInstance shareInstance] keyPath:DifferentValue options:NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if ([change objectForKey:NSKeyValueChangeNewKey]) {
            __weakSelf.pageBrowseTime ++;
//            FMLog(@"笔记页面浏览时长%zd秒", __weakSelf.pageBrowseTime);
            if (__weakSelf.pageBrowseTime >= 10) {
                [__weakSelf judgeShowFocusReminder];
            }

            // 未登录或者没有权限，返回
            if (![FMHelper isLogined] || __weakSelf.detailModel.authority.integerValue != 1) {
                return;
            }

            __weakSelf.pageBrowseTime ++;
//            FMLog(@"页面浏览时长%zd秒", __weakSelf.pageBrowseTime);
            [__weakSelf dealPageBrowseStatistics];
            
            if (__weakSelf.needAddTimeCountFlag == 1) {
                [__weakSelf dealPageBrowseTask:startTime];
            }
        }
    }];
} 

// 处理页面浏览完成统计
- (void)dealPageBrowseStatistics {
    if (![FMNoteStatisticsCacheTool isCompletedWithType:3 noteId:self.noteId]) {
        if ((self.detailModel.noteType.integerValue == 1 && self.detailModel.audioLength > 0 && self.pageBrowseTime >= self.detailModel.audioLength * 0.5) || (self.detailModel.noteType.integerValue == 5 && self.detailModel.videoTime.length && self.pageBrowseTime >= [self videoLengthFromString:self.detailModel.videoTime])) {
            [self noteStatisticsWithType:3];
            // 先设置为已完整阅读，免得重复调用
            [FMNoteStatisticsCacheTool updateStatusWithType:3 completed:YES noteId:self.noteId];
        }
    }
}

- (CGFloat)videoLengthFromString:(NSString *)time {
    NSArray *components = [time componentsSeparatedByString:@":"];
    NSInteger hour = 0;
    NSInteger minute = 0;
    NSInteger second = 0;
    
    if (components.count == 3) {
        hour = [components[0] integerValue];
        minute = [components[1] integerValue];
        second = [components[2] integerValue];
    } else if (components.count == 2) {
        minute = [components[0] integerValue];
        second = [components[1] integerValue];
    } else {
        // Handle invalid format
        return 0.0;
    }
    
    return hour * 3600.0 + minute * 60.0 + second;
}

// 处理页面每日浏览任务
- (void)dealPageBrowseTask:(long long)startTime {
    // 任务未开启，返回
    FMTaskConfigModel *taskConfig = [FMUserDefault getUnArchiverDataForKey:AppInit_Task];
    if (!taskConfig.taskDic[@(FMTaskTypeReadNote)].isEnable) {
        return;
    }
    
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    NSDate *lastCompleteDate = [NSDate dateWithTimeIntervalSince1970:userModel.lastCompleteReadNoteServiceTime/1000]; // 上次完成笔记任务的时间
    NSDate *currentCompleteDate = [NSDate dateWithTimeIntervalSince1970:(startTime/1000 + taskConfig.taskDic[@(FMTaskTypeReadNote)].taskRequire)]; // 理想的完成本次笔记任务的时间，真正完成时间只会比这大
    
    // 如果本次完成笔记任务和上次完成笔记任务是同一天
    if ([currentCompleteDate isSameDayWithDate:lastCompleteDate]) {
        // 如果本地任务计数显示已完成，返回
        if (userModel.readNoteCompleteNum >= taskConfig.taskDic[@(FMTaskTypeReadNote)].taskNeedNum) {
            return;
        }
    } else { // 完成任务时是第二天了，将本地任务计数清0
        userModel.readNoteCompleteNum = 0;
        userModel.lastCompleteReadNoteServiceTime = 0;
        [MyKeyChainManager save:kUserModel data:userModel];
    }
    
    if (self.pageBrowseTime >= taskConfig.taskDic[@(FMTaskTypeReadNote)].taskRequire) {
        [HttpRequestTool requestCompleteReadNoteTaskWithNoteId:self.detailModel.noteId start:^{
        } failure:^{
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                self.needAddTimeCountFlag = 0;
                FMSubTask *subTask = [FMSubTask modelWithDictionary:dic[@"data"]];
                if (subTask.completeNum >= taskConfig.taskDic[@(FMTaskTypeReadNote)].taskNeedNum) {
                    [SVProgressHUD showImage:nil status:@"任务完成！奖励已放入您的账户"];
                }
                FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
                userModel.readNoteCompleteNum = subTask.completeNum;
                userModel.lastCompleteReadNoteServiceTime = [dic[@"systemTime"] longLongValue];
                [MyKeyChainManager save:kUserModel data:userModel];
            } else {
                if ([dic[@"errcode"] isEqualToString:@"4101"]) { // 任务之前就已完成，容错处理
                    self.needAddTimeCountFlag = 0;
                    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
                    userModel.readNoteCompleteNum = taskConfig.taskDic[@(FMTaskTypeReadNote)].taskNeedNum;
                    userModel.lastCompleteReadNoteServiceTime = [dic[@"systemTime"] longLongValue];
                    [MyKeyChainManager save:kUserModel data:userModel];
                }
            }
        }];
    }
}

- (void)headerAction {
    self.page = 1;
    [self requestNoteComment];
    [self requestRecommendNoteList];
    if (self.detailModel == nil) {
        self.refreshStatistilNum = 0;
        [self requestNoteDetail];
    }
}

- (void)footerAction {
    self.page++;
    self.refreshStatistilNum = 2;
    [self requestNoteComment];
}

- (void)gotoPay {
    if (self.detailModel.notePrice.integerValue >= 30) {
        [HttpRequestTool getRandomDiscountStart:^{
        } failure:^{
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                NSDictionary *discountInfo = [NSDictionary dictionaryWithDictionary:dic[@"data"]];
                if ([discountInfo[@"isOpen"] boolValue]) {
                    [self configPaymentView:discountInfo];
                } else {
                    [self configPaymentView:nil];
                }
            }
        }];
    } else {
        [self configPaymentView:nil];
    }
}

/// 设置支付弹窗
- (void)configPaymentView:(NSDictionary *)discountInfo {
    if ([FMHelper checkLoginStatus]) {
        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];

        EnablePayModel *enablePayModel = [[EnablePayModel alloc] init];
        enablePayModel.paydesc = [NSString stringWithFormat:@"您当前拥有%zd金币，确认支付", userModel.coin];
        enablePayModel.name = @"金币";
        enablePayModel.num = [NSString stringWithFormat:@"%zd", userModel.coin];
        enablePayModel.type = PaymentTypeCoin;
        enablePayModel.freeDesc = self.detailModel.freeDesc;
        enablePayModel.discountInfo = discountInfo;
        enablePayModel.bignameId = self.detailModel.bignameDto.userId;
        enablePayModel.consumeType = 1;
        enablePayModel.contentId = self.detailModel.noteId.integerValue;
        
        EnablePayModel *wxPayModel = [[EnablePayModel alloc] init];
        wxPayModel.name = @"微信";
        wxPayModel.type = PaymentTypeWechat;
        wxPayModel.consumeType = 1;
        wxPayModel.bignameId = self.detailModel.bignameDto.userId;
        wxPayModel.contentId = self.detailModel.noteId.integerValue;

        self.detailModel.enablePayModel = @[enablePayModel, wxPayModel];
        NSString *dakaId =  self.detailModel.bignameDto.userId;
        NSString *price = self.detailModel.notePrice;
        /// 标题存在取标题 如果是取内容  因为内容中可能带有话题 所以取htmlModel 中处理好的本文
        NSString *title = self.detailModel.noteTitle.length ? self.detailModel.noteTitle : self.htmlModel.attrStr.string;;
        WEAKSELF;
        [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:dakaId certCode:self.detailModel.bignameDto.certCode clickView:nil confirmOperation:^{
            PaymentView *payView = [PaymentView showWithEnablePayList:@[enablePayModel, wxPayModel] payPrice:price productName:title bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
                if (selectedModel.type == PaymentTypeWechat) {
                    [__weakSelf wxPayGoodsId:selectedModel.couponId];
                } else {
                    [__weakSelf httpForPayNote:[NSString stringWithFormat:@"%zd", selectedModel.type] goodsId:selectedModel.couponId usePoints:selectedModel.usePoints];
                }
            } dismissBlock:^{
            }];
            
            NSDictionary *couponDic = [FMUserDefault getUnArchiverDataForKey:@"BeginnerUserCoupon"];
            FMCouponTableModel *couponModel = couponDic[[FMUserDefault getUserId]];
            NSDictionary *dic = [FMUserDefault getUnArchiverDataForKey:@"CouponGuideTypeStep1"];
            NSNumber *isStep1 = dic[[FMUserDefault getUserId]];
             if (couponModel.couponId.length > 0 && !isStep1.boolValue) {
                 FMCouponUseGuideVC *vc = [[FMCouponUseGuideVC alloc] initWithGuideType:CouponGuideTypeStep1];
                 vc.view.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
                 [[FMAppDelegate shareApp].window addSubview:vc.view];
                 
                 __weak PaymentView *weakPayView = payView;
                 payView.getCouponInfo = ^{
                     vc.targetRect = [weakPayView couponBgViewCoverWindowFrame];
                 };
             }
            __weak PaymentView *weakPayView = payView;
            payView.couponSelected = ^{
                NSDictionary *couponDic = [FMUserDefault getUnArchiverDataForKey:@"BeginnerUserCoupon"];
                FMCouponTableModel *couponModel = couponDic[[FMUserDefault getUserId]];
                NSDictionary *dic = [FMUserDefault getUnArchiverDataForKey:@"CouponGuideTypeStep4"];
                NSNumber *isStep4 = dic[[FMUserDefault getUserId]];
                if (couponModel.couponId.length > 0 && !isStep4.boolValue) {
                    FMCouponUseGuideVC *vc = [[FMCouponUseGuideVC alloc] initWithGuideType:CouponGuideTypeStep4];
                    vc.view.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
                    [[FMAppDelegate shareApp].window addSubview:vc.view];
                    vc.targetRect = [weakPayView headerViewCoverWindowFrame];
                }
            };
        }];       
    }
}

- (void)wxPayGoodsId:(NSString *)goodsId {
    [HttpRequestTool wxPayNoteWithNoteId:self.detailModel.noteId goodsId:goodsId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self wechatPay:dic];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

/// 视频权限支付
- (void)httpForPayNote:(NSString *)payType goodsId:(NSString *)goodsId usePoints:(NSInteger)usePoints {
    WEAKSELF
    [HttpRequestTool payNoteWithNoteId:self.detailModel.noteId type:payType goodsId:goodsId usePoints:usePoints start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"] && [dic[@"data"] isKindOfClass:[NSDictionary class]]) {
            [SVProgressHUD dismiss];
            if ([dic[@"data"][@"discountPrice"] floatValue] > 0) {
                NSString *discountPrice = [NSString stringWithFormat:@"%@",dic[@"data"][@"discountPrice"]];
                FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
                popView.minusNum = discountPrice.integerValue;
                [popView show];
            } else {
                FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
                [popView show];
            }

            [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];

            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                //更新权限 更新播放视频组件状态
                __weakSelf.detailModel.authority = @"1";
                __weakSelf.detailModel.tryPlaySecond = @"0";
                __weakSelf.currentPalyStatus = 0;
                __weakSelf.detailModel.playStatus = 0;
                __weakSelf.controlView.model = self.detailModel;
                [__weakSelf.tableView reloadData];
                [__weakSelf pageBrowseTimeCountDownWithStartTime:[dic[@"systemTime"] longLongValue]];
            });
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

/// 处理评论model
- (void)dealCommentFramesWithComments:(NSArray *)comments {
    NSMutableArray *tmpFrameArr = [NSMutableArray array];
    for (FMCommentModel *commentModel in comments) {        
        FMCommentFrameModel *frameModel = [[FMCommentFrameModel alloc] init];
        frameModel.allowBigFont = YES;
        frameModel.commentModel = commentModel;
        [tmpFrameArr addObject:frameModel];
    }
    
    [self removeRepeatDataWithArray:tmpFrameArr];
}

/// 移除重复数据
- (void)removeRepeatDataWithArray:(NSArray *)array {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    for (FMCommentFrameModel *frameModel in array) {
        [dic setObject:frameModel forKey:frameModel.commentModel.commentId];
    }
    
    for (FMCommentFrameModel *frameModel in self.commentFrames.reverseObjectEnumerator) {
        if ([dic valueForKey:frameModel.commentModel.commentId]) {
            [self.commentFrames removeObject:frameModel];
        }
    }
    [self.commentFrames addObjectsFromArray:array];
}

/// 判断是否显示关注提醒
- (void)judgeShowFocusReminder {
    if (self.detailModel && ![[FMUserDefault getUserId] isEqualToString:self.detailModel.bignameDto.userId] && ![[FMUserDataSyncManager sharedManager] isDakaNoticed:self.detailModel.bignameDto.userId]) {
        self.focusReminderView.hidden = NO;
        
        BigCastDetailModel *model = [BigCastDetailModel new];
        model.userName = self.detailModel.bignameDto.userName;
        model.userid = self.detailModel.bignameDto.userId;
        model.userIco = self.detailModel.bignameDto.userIco;
        self.focusReminderView.model = model;
    } else {
        self.focusReminderView.hidden = YES;
    }
}

/// 调起wx支付
- (void)wechatPay:(NSDictionary *)dic {
    NSDictionary *data = dic[@"data"][@"prepay"];
    NSString *wechatAppidForPay = [FMUserDefault getSeting:AppInit_PayAppid];
    [WXApi registerApp:wechatAppidForPay universalLink:UNIVERSAL_LINK];
    
    if (![WXApi isWXAppInstalled]) {
        [SVProgressHUD showInfoWithStatus:@"您还没有安装微信，请先安装"];
        return;
    }
    
    if (![WXApi isWXAppSupportApi]) {
        [SVProgressHUD showInfoWithStatus:@"您的微信版本太低，建议更新"];
        return;
    }
    
    NSString *stamp  = data[@"timestamp"];
    PayReq *req = [[PayReq alloc] init];
    req.partnerId = data[@"partnerid"];
    req.prepayId = data[@"prepayid"];
    req.timeStamp = stamp.intValue;
    req.nonceStr = data[@"noncestr"];
    req.package = @"Sign=WXPay";
    req.sign = data[@"sign"];
    
    [WXApi sendReq:req completion:^(BOOL success) {
        if (success) {
            [SVProgressHUD dismiss];
            
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            [dict setObject:data[@"out_trade_no"] forKey:@"outTradeNo"];
            [dict setObject:self.detailModel.noteId forKey:@"noteId"];
            [dict setObject:[FMUserDefault getUserId] forKey:@"userId"];
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            [defaults setObject:dict forKey:kUserDefault_IsPayNote];
            [defaults synchronize];
        } else {
            [SVProgressHUD showErrorWithStatus:@"打开微信异常，请重试"];
        }
    }];
}

#pragma mark - HTTP
/// 笔记详情
- (void)requestNoteDetail {
    [HttpRequestTool getNoteDetailWithNoteId:self.noteId start:^{
        [SVProgressHUD show];
    } failure:^{
        self.refreshStatistilNum += 1;
        [self.view showReloadNetworkViewWithBlock:^{
            self.tableView.hidden = YES;
            self.bottomView.hidden = YES;
            self.mentionStockLabel.hidden = YES;
            [self requestNoteDetail];
            [self requestNoteComment];
        }];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.totalComments = [dic[@"data"][@"commentNum"] integerValue];
            self.bottomView.commentNum = self.totalComments;

            FMNoteModel *model = [FMNoteModel modelWithDictionary:dic[@"data"]];
            if (model.deleteFlag.integerValue == 1) { // 笔记未被删除
                if (model.contentAudio.length) {
                    AVURLAsset *audioAsset = [AVURLAsset URLAssetWithURL:[NSURL URLWithString:model.contentAudio] options:nil];
                    CMTime audioDuration = audioAsset.duration;
                    model.audioLength = CMTimeGetSeconds(audioDuration);
                }
                self.detailModel = model;
                [self createHtmlModel];
            
                [self pageBrowseTimeCountDownWithStartTime:[dic[@"systemTime"] longLongValue]];
                
                self.bottomView.collected = [[FMUserDataSyncManager sharedManager] isNoteCollected:self.detailModel.noteId];
                self.bottomView.praised = [[FMUserDataSyncManager sharedManager] isNoteLiked:self.detailModel.noteId];
                self.bottomView.praiseNum = self.detailModel.satisfiedNums.integerValue;
                if (self.detailModel.bignameDto.attestationType == 2) {
                    // 投顾人员 显示打赏按钮
                    self.bottomView.showType = FMDetailBottomShowTypeReward | FMDetailBottomShowTypeCommentNum | FMDetailBottomShowTypePraise;
                } else {
                    self.bottomView.showType = FMDetailBottomShowTypeCommentNum | FMDetailBottomShowTypePraise;
                }
                [self.bottomView.commentNumBtn setImage:FMImgInBundle(@"笔记/底部评论") forState:UIControlStateNormal];
                self.bottomView.commentNumBtn.tagLabel.backgroundColor = FMNavColor;
                
                [self.bottomView setNeedsLayout];
                [self.bottomView layoutIfNeeded];
            } else {
                self.detailModel = model;
            }
            self.navView.model = self.detailModel;
            self.navView.htmlModel = self.htmlModel;
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            [self.view showReloadNetworkViewWithBlock:^{
                self.tableView.hidden = YES;
                self.bottomView.hidden = YES;
                self.mentionStockLabel.hidden = YES;
                self.refreshStatistilNum = 0;
                [self requestNoteDetail];
                [self requestRecommendNoteList];
                [self requestNoteComment];
            }];
        }
        self.refreshStatistilNum += 1;
    }];
}

/// 推荐笔记
- (void)requestRecommendNoteList {
    [HttpRequestTool requestNoteDetailRecommendNoteListWithNoteId:self.noteId start:^{
    } failure:^{
        self.refreshStatistilNum += 1;
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self.recommendNotes removeAllObjects];
            
            NSArray *dataArr = [NSArray modelArrayWithClass:[FMNoteModel class] json:dic[@"data"]];
            for (NSInteger i = 0; i < dataArr.count; i ++) {
                FMAllNoteListModel *model = [[FMAllNoteListModel alloc] init];
                model.noteApiDto = dataArr[i];
                model.type = model.noteApiDto.type;
                [self.recommendNotes addObject:model];
            }
        }
        self.refreshStatistilNum += 1;
    }];
}

/// 评论列表
- (void)requestNoteComment {
    [HttpRequestTool getNoteCommentListWithWithPage:self.page pageSize:self.pageSize noteId:self.noteId start:^{
    } failure:^{
        self.refreshStatistilNum += 1;
        [self endRefreshForFailure];

        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"] && [dic[@"data"] isKindOfClass:[NSArray class]]) {
            self.currentPage = self.page;
            if (self.page == 1) {
                [self.tableView.mj_header endRefreshing];
                [self.tableView.mj_footer resetNoMoreData];
                [self.commentFrames removeAllObjects];
            } else {
                [self.tableView.mj_footer endRefreshing];
            }
            NSArray *dataArr = [NSArray modelArrayWithClass:[FMCommentModel class] json:dic[@"data"]];
            if (dataArr.count < self.pageSize) {
                [self.tableView.mj_footer endRefreshingWithNoMoreData];
            }
            
            [self dealCommentFramesWithComments:dataArr];
            if (!self.commentFrames.count) {
                UIView *tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 200)];
                [tableFooterView showNoDataViewWithString:@"暂无评论, 快来抢沙发" attributes:nil position:ShowPositionTop offsetX:0 offsetY:30];
                self.tableView.tableFooterView = tableFooterView;
                self.tableView.mj_footer.hidden = YES;
            } else {
                self.tableView.tableFooterView = nil;
                self.tableView.mj_footer.hidden = NO;
            }
        } else {
            [self endRefreshForFailure];
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
        
        self.refreshStatistilNum += 1;
    }];
}

/// 打赏记录
- (void)requestRewardRecord {
    // 只有投顾才有打赏功能
    if (self.detailModel.bignameDto.attestationType != 2) return;
    [HttpRequestTool getNoteRewardRecodListDataWithNoteId:self.noteId Start:^{
    } failure:^{
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.detailModel.rewardPeopleArr = [NSMutableArray arrayWithArray:dic[@"data"][@"userIcos"]];
            self.detailModel.rewardNum = dic[@"data"][@"rewardCount"];
            // 所有数据获取完 再去走定位页面位置逻辑

            [self.tableView reloadData];

            if (self.reloadPageBlock) {
                self.reloadPageBlock();
            }
        }
    }];
}

/// 查询问股回答所需时长
- (void)checkWaitTime {
    [HttpRequestTool questionCheckWaitTimeWithAnswerId:self.detailModel.bignameDto.userId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:nil
                                                                message:dic[@"data"]
                                                               delegate:self
                                                      cancelButtonTitle:@"取消"
                                                      otherButtonTitles:@"确定", nil];
            [alertView show];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

/// 笔记统计
- (void)noteStatisticsWithType:(NSInteger)type {
    [HttpRequestTool requestNoteStatisticsWithNoteId:self.noteId behaviorType:type start:^{
    } failure:^{
        [FMNoteStatisticsCacheTool updateStatusWithType:type completed:NO noteId:self.noteId];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [FMNoteStatisticsCacheTool updateStatusWithType:type completed:YES noteId:self.noteId];
        } else {
            [FMNoteStatisticsCacheTool updateStatusWithType:type completed:NO noteId:self.noteId];
        }
    }];
}

#pragma mark - UIAlertView Delegate
- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex {
    if (buttonIndex == 1) {
        WEAKSELF;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [HttpRequestTool questionAskWithAnswerId:__weakSelf.detailModel.bignameDto.userId content:__weakSelf.questionContent  type:__weakSelf.askCommentView.type goodsId:__weakSelf.askCommentView.couponId  stockCode:__weakSelf.choosedStockModel.oldStockCode stockName:__weakSelf.choosedStockModel.name usePoints:__weakSelf.askCommentView.usePoints start:^{
                [SVProgressHUD show];
            } failure:^{
                [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [SVProgressHUD showSuccessWithStatus:@"您的提问已成功发送，请注意接收回复通知"];
                    __weakSelf.askCommentView.textView.text = @"";
                    __weakSelf.questionContent = nil;
                    __weakSelf.choosedStockModel = nil;
                    __weakSelf.askCommentView = nil;
                    [[NSNotificationCenter defaultCenter] postNotificationName:kAskCodeSuccess object:nil];
                    
                    [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }];
        });
    } else {
        self.questionContent = nil;
        self.choosedStockModel = nil;
    }
}

#pragma mark - playerControlViewDelegate
/// 开通顾问或者支付金币
- (void)detailPayViewPayBtnClick:(UIButton *)sender {
    if ([FMHelper checkLoginStatus]) {
        if (self.detailModel.ex_needVIP) {
            // 开通投顾服务
            [self openVip];
        } else {
            if (self.detailModel.authority.integerValue == 5) {
                if (self.detailModel.notePrice.integerValue > 0) {
                    // 去支付
                    [self gotoPay];
                } else {
                    // 开通投顾服务
                    [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
                        [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://vipproduct?id=%@",self.detailModel.bignameDto.userId]];
                    }];
                }
            } else {
                // 去支付
                [self gotoPay];
            }
        }
    }
}

// 开通投顾服务
- (void)openVip {
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    if (userModel.vip) {
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"weixin://"] options:@{} completionHandler:nil];
    } else {
        NSString *str = [NSString stringWithFormat:@"tel://%@", [FMUserDefault getSeting:AppInit_ContactUs_Phone]];
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str] options:@{} completionHandler:nil];
    }
}

//重新试看视频
- (void)videoReWatch:(UIButton *)sender {
    self.currentPalyStatus = 1;
    self.detailModel.playStatus = self.currentPalyStatus;
    self.controlView.model = self.detailModel;
    [self.player.currentPlayerManager replay];
}

- (void)showVideoPlayerInView:(UIView *)view {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(networkChanged) name:kNetworkStatusChangedNotification object:nil];    
    self.controlView.prepareShowControlView = YES;
    [self.player playTheIndexPath:nil assetURL:[NSURL URLWithString:self.detailModel.noteVideo]];
    // 配置画中画
    [[FMAppDelegate shareApp] configPiP:[FMPlayerManager shareManager].playerManager.avPlayerLayer];
    
    [self.controlView showTitle:@""
                 coverURLString:self.detailModel.noteImg
                 fullScreenMode:ZFFullScreenModeLandscape];
    NSMutableDictionary *playTimeDic = [FMUserDefault getUnArchiverDataForKey:KNotePlayerCachePlayTime];
    NSNumber *time = [playTimeDic objectForKey:[NSString stringWithFormat:@"note%@",self.detailModel.noteId]];
    if ([time doubleValue] > 0) {
        [self.player seekToTime:[time doubleValue] completionHandler:nil];
    }

    @zf_weakify(self)
    self.player.playerPlayTimeChanged = ^(id<ZFPlayerMediaPlayback>  _Nonnull asset, NSTimeInterval currentTime, NSTimeInterval duration) {
        @zf_strongify(self)
        if (self.detailModel.tryPlaySecond.integerValue > 0) { //有试看
            if (floorf(currentTime) >= self.detailModel.tryPlaySecond.integerValue || self.currentPalyStatus == 2) {
                [self.controlView resetControlView];
                [self.player.currentPlayerManager pause];
                if (self.player.isFullScreen) {
                    [self.player enterFullScreen:NO animated:YES];
                }
                self.currentPalyStatus = 2; //试看结束
                self.detailModel.playStatus = self.currentPalyStatus;
                self.controlView.model = self.detailModel;
            }
        }
    };
}


#pragma mark - ScrollViewDelegate 
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (scrollView == self.tableView) {
        if (_player != nil) {
            FMNoteDetailLiveHeaderTabCell *cell = [self.tableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:0]];
            if (scrollView.contentOffset.y > cell.liveBgImageV.origin.y) {
                [self.view addSubview:self.playerManager.view];
                self.playerManager.view.top = self.navView.bottom;
            } else {
                [cell.liveBgImageV addSubview:self.playerManager.view];
                self.playerManager.view.top = 0;
            }
        }
        
        // 导航栏颜色
        CGFloat offsetY = scrollView.contentOffset.y;
        if (offsetY < 65) {
            self.navView.redStyle = YES;
            [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
        } else {
            self.navView.redStyle = NO;
            [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
        }
        
        // 评论数按钮切换
        NSIndexPath *targetIndexPath = [NSIndexPath indexPathForRow:0 inSection:2];
        CGRect cellRect = [self.tableView rectForRowAtIndexPath:targetIndexPath];
        CGRect tableViewVisibleRect = CGRectMake(self.tableView.contentOffset.x, self.tableView.contentOffset.y, self.tableView.bounds.size.width, self.tableView.bounds.size.height);
        
        // 判断目标cell是否出现在可视区域内
        if (CGRectIntersectsRect(tableViewVisibleRect, cellRect)) {
            // cell是否出现在tableView底部
            if (CGRectGetMinY(cellRect) < CGRectGetMaxY(tableViewVisibleRect)) {
                [self.bottomView.commentNumBtn setImage:FMImgInBundle(@"笔记/底部回到正文") forState:UIControlStateNormal];
                self.bottomView.commentNumBtn.tagLabel.hidden = YES;
                self.bottomView.commentNumBtn.showCommentIcon = NO;
            }
        } else {
            // cell是否消失在tableView底部
            if (CGRectGetMinY(cellRect) > CGRectGetMaxY(tableViewVisibleRect)) {
                [self.bottomView.commentNumBtn setImage:FMImgInBundle(@"笔记/底部评论") forState:UIControlStateNormal];
                self.bottomView.commentNumBtn.tagLabel.hidden = (self.totalComments == 0);
                self.bottomView.commentNumBtn.showCommentIcon = YES;
            }
        }
    }
}

#pragma mark - TableViewDelegate DataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 3;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section == 0) {
        if (self.detailModel.noteType.integerValue == 6) {
            return 2;
        }
        return 3;
    } else if (section == 1) {
        return self.recommendNotes.count;
    }
    
    // 这里增加一个高度为CGFLOAT_MIN的cell，方便评论数切换逻辑处理
    return self.commentFrames.count + 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    WEAKSELF
    if (indexPath.section == 0) {
        if (self.detailModel == nil) {
            return [[UITableViewCell alloc] init];
        }
        
        if (self.detailModel.noteType.integerValue == 6) {
            if(indexPath.row == 0) {
                // 投顾信息 , 关注按钮, 笔记被删除 笔记内容 笔记图片组件 引用内容UI
                FMNoteRetweetsDetailTopCell *cell = [tableView reuseCellClass:[FMNoteRetweetsDetailTopCell class]];
                cell.model = self.detailModel;
                
                cell.refreshHeightBlock = ^{
                    [__weakSelf.tableView reloadData];
                };
                cell.askCodeClick = ^(UIButton * _Nonnull sender) {
                    [__weakSelf startAskCode:sender];
                };
                return cell;
            } else if (indexPath.row == 1) {
                // 笔记关联直播信息 PDF视图 免责声明 赞赏UI 相关股票 相关话题 底部view(浏览人数  价格或者vip权限语句)
                FMNoteDetailHeaderThirdCell *cell = [tableView reuseCellClass:[FMNoteDetailHeaderThirdCell class]];
                cell.model = self.detailModel;
                return cell;
            }
        } else {
            if (indexPath.row == 0) {
                if (self.detailModel.noteType.integerValue == 5) {
                    // 视频笔记, 投顾信息 播放器承载imageView
                    FMNoteDetailLiveHeaderTabCell *cell = [tableView reuseCellClass:[FMNoteDetailLiveHeaderTabCell class]];
                    self.detailModel.playStatus = self.currentPalyStatus;
                    cell.model = self.detailModel;
                    
                    cell.askCodeBlock = ^(UIButton * _Nonnull sender) {
                        [__weakSelf startAskCode:sender];
                    };
                    return cell;
                } else {
                    // 文字笔记 投顾信息 内容  引导支付UI  权限UI 音频播放UI 支付按钮
                    FMNoteDetailHeaderCell *cell = [tableView reuseCellClass:[FMNoteDetailHeaderCell class]];
                    cell.delegate = self;
                    cell.model = self.detailModel;
                    cell.askCodeClick = ^(UIButton *sender) {
                        [__weakSelf startAskCode:sender];
                    };
                    return cell;
                }
            } else if(indexPath.row == 1) {
                // html内容
                FMDetailHtmlCell *cell = [tableView reuseCellClass:[FMDetailHtmlCell class]];
                cell.model = self.htmlModel;
                return cell;
            } else if (indexPath.row == 2) {
                // 笔记关联的直播信息 PDF视图 免责声明 赞赏UI 相关股票 相关话题 底部view(浏览人数  价格或者vip权限语句)
                FMNoteDetailHeaderThirdCell *cell = [tableView reuseCellClass:[FMNoteDetailHeaderThirdCell class]];
                cell.model = self.detailModel;
                return cell;
            }
        }
    } else if (indexPath.section == 1) {
        FMAllNoteListModel *model = (FMAllNoteListModel *)self.recommendNotes[indexPath.row];
        model.noteApiDto.indexPath = indexPath;
        FMNoteDetailRecommendedNoteCell *cell = [tableView reuseCellClass:[FMNoteDetailRecommendedNoteCell class]];
        cell.model = model.noteApiDto;
        return cell;
    }
    
    // 评论cell
    if (indexPath.row == 0) {
        FMCommentCell *cell = [tableView reuseCellClass:[FMCommentCell class]];
        cell.hidden = YES;
        return cell;
    }
    FMCommentCell *cell = [tableView reuseCellClass:[FMCommentCell class]];
    if (self.commentFrames.count >= indexPath.row) {
        cell.frameModel = self.commentFrames[indexPath.row - 1];
    }
    cell.allowBigFont = YES;
    cell.delegate = self;
    cell.hidden = NO;
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        WEAKSELF;
        if (self.detailModel == nil) {
            return 0;
        }
        
        if (self.detailModel.noteType.integerValue == 6) {
            if (indexPath.row == 0) {
                return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMNoteRetweetsDetailTopCell class]) configuration:^(FMNoteRetweetsDetailTopCell *cell) {
                    cell.model = __weakSelf.detailModel;
                }];
            } else  if (indexPath.row == 1) {
                return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMNoteDetailHeaderThirdCell class]) configuration:^(FMNoteDetailHeaderThirdCell *cell) {
                    cell.model = __weakSelf.detailModel;
                }];
            }
        }
        if (indexPath.row == 0) {
            if (self.detailModel.noteType.integerValue == 5) { //带视频的笔记
                return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMNoteDetailLiveHeaderTabCell class]) configuration:^(FMNoteDetailLiveHeaderTabCell *cell) {
                    cell.model = __weakSelf.detailModel;
                }];
            } else { //文字笔记
                return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMNoteDetailHeaderCell class]) configuration:^(FMNoteDetailHeaderCell *cell) {
                    cell.model = __weakSelf.detailModel;
                }];
            }
         
        } else if (indexPath.row == 2) {
            return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMNoteDetailHeaderThirdCell class]) configuration:^(FMNoteDetailHeaderThirdCell *cell) {
                cell.model = __weakSelf.detailModel;
            }];
        } else if (indexPath.row == 1) {
            return ceil(self.htmlModel.textHeight) > 10 ? ceil(self.htmlModel.textHeight) + 20 : 10;
        }
    } else if (indexPath.section == 1) {
        return 125.0f;
    }
    
    if (indexPath.row == 0) {
        return CGFLOAT_MIN;
    }
    // 检查 indexPath.row - 1 是否在数组的合法范围内
    if (indexPath.row - 1 < self.commentFrames.count) {
        FMCommentFrameModel *frameModel = [self.commentFrames objectAtIndex:indexPath.row - 1];
        return frameModel.cellHeight;
    } else {
        // 如果超出范围，返回一个默认值或处理错误
        return CGFLOAT_MIN; // 或者根据你的需求返回其他值
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (section == 0) return nil;
    if (section == 1 && !self.recommendNotes.count) return nil;
    if (section == 2 && !self.commentFrames.count) return nil;
    
    UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 45)];
    view.backgroundColor = UIColor.up_contentBgColor;
    
    UIView *sepLine = [[UIView alloc] init];
    [view addSubview:sepLine];
    [sepLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(@(0));
        make.height.equalTo(@0.5);
    }];
    sepLine.backgroundColor = UIColor.up_dividerColor;
    
    UILabel *label = [[UILabel alloc] init];
    [view addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.centerY.equalTo(view);
    }];
    if (section == 1) {
        label.text = @"推荐笔记";
    } else if (section == 2) {
        if (self.totalComments > 0) {
            label.text = [NSString stringWithFormat:@"热门评论(%zd)", self.totalComments];
        } else {
            label.text = @"热门评论";
        }
    }
    label.textColor = UIColor.up_textPrimaryColor;
    label.font = [FMHelper scaleFont:16.0];
    
    UIView *bottomSepLine = [[UIView alloc] init];
    [view addSubview:bottomSepLine];
    [bottomSepLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.equalTo(view);
        make.height.equalTo(@0.5);
    }];
    bottomSepLine.backgroundColor = UIColor.up_dividerColor;
    
    return view;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (section == 0) return CGFLOAT_MIN;
    if (section == 1 && !self.recommendNotes.count) return CGFLOAT_MIN;
    if (section == 2 && !self.commentFrames.count) return CGFLOAT_MIN;

    return 45.0f;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    UIView *view = [UIView new];
    view.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    return view;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (section == 1 && !self.recommendNotes.count) return CGFLOAT_MIN;
    if (section == 2) return CGFLOAT_MIN;
    
    return 8.0f;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    if (indexPath.section == 1) {
        FMAllNoteListModel *model = self.recommendNotes[indexPath.row];
        if (model.type == 1) {
            //进直播详情
            [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
                [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://dakalive?roomid=%@&status=%@",model.liveroomApiDto.liveId,model.liveroomApiDto.status]];
            }];
        } else {
            FMNoteDetailViewController *vc = [[FMNoteDetailViewController alloc] init];
            vc.noteId = model.noteApiDto.noteId;
            vc.deleteBlock = ^{
                [self.recommendNotes removeObjectAtIndex:indexPath.row];
                [self.tableView reloadData];
            };
            [self.navigationController pushViewController:vc animated:YES];
        }
    }
}

#pragma mark - FMNoteDetailHeaderCell Delegate
- (void)noteDetailHeaderCellPayBtnDidClicked:(FMNoteDetailHeaderCell *)headerCell serviceTime:(long long)serviceTime {
    // 金币支付成功，直接刷新tableView
    self.controlView.model = self.detailModel;
    self.detailModel.authority = @"1";
    [self createHtmlModel];
    [self.tableView reloadData];
    
    [self pageBrowseTimeCountDownWithStartTime:serviceTime];
}

#pragma mark - FMNoteCommentCell Delegate
- (void)commentCellReplyBtnDidClicked:(FMCommentModel *)commentModel {
    [self commentReply:commentModel];
}

- (void)commentCellSecondLevelReplyDidClicked:(FMCommentModel *)commentModel {
    [self commentReply:commentModel];
}

- (void)commentReply:(FMCommentModel *)commentModel {
    if ([FMHelper checkLoginStatus]) {
        UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
        self.commentView.frame = keyWindow.bounds;
        [keyWindow addSubview:self.commentView];
        NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"回复:%@", commentModel.commenterName]];
        [attrStr addAttributeTextColor:ColorWithHex(0x0074fa) range:NSMakeRange(3, attrStr.length - 3)];
        self.commentView.publisLb.attributedText = attrStr;
        self.commentView.commentLev = 2;
        
        WEAKSELF;
        self.commentView.publishBlock = ^(NSString *content, void (^success)(void)) {
            [HttpRequestTool noteCommentPublishWithNoteId:__weakSelf.detailModel.noteId commentLev:@"2" parentCommentid:commentModel.commentId commentContent:content start:^{
                [SVProgressHUD show];
            } failure:^{
                [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [SVProgressHUD showSuccessWithStatus:@"发布成功！审核通过后展示"];
                    if (success) {
                        success();
                    }
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }];
        };
    }
}

- (void)commentCellPraiseBtnDidClicked:(FMCommentModel *)commentModel success:(void(^)())success {
    [HttpRequestTool noteCommentPraiseWithCommentId:commentModel.commentId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            success();
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

#pragma mark - FMDetailBottomView Delegate
/// 赞赏按钮点击回调
- (void)detailBottomViewRewardBtnDidClicked:(UIView *)bottomView btn:(UIButton *)shareBtn {
    // 先检验支付权限 在拉起金额选择页面
    [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
        [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:self.detailModel.bignameDto.userId certCode:self.detailModel.bignameDto.certCode clickView:nil confirmOperation:^{
            self.rewardView.noteModel = self.detailModel;
            if(!self.rewardView.superview) {
                [self.view addSubview:self.rewardView];
            }
            [self.rewardView show];
        }];
    }];
}

/// 评论点击回调
- (void)detailBottomViewCommentBtnDidClicked:(UIView *)bottomView btn:(UIButton *)commentBtn {
    bottomView.userInteractionEnabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        bottomView.userInteractionEnabled = YES;
    });

    WEAKSELF;
    if ([FMHelper checkLoginStatus]) {
        UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
        self.commentView.frame = keyWindow.bounds;
        [keyWindow addSubview:self.commentView];
        self.commentView.publisLb.attributedText = [[NSAttributedString alloc] initWithString:@"发表评论"];
        self.commentView.commentLev = 1;
        
        self.commentView.publishBlock = ^(NSString *content, void (^success)(void)) {
            [HttpRequestTool noteCommentPublishWithNoteId:__weakSelf.detailModel.noteId commentLev:@"1" parentCommentid:nil commentContent:content start:^{
                [SVProgressHUD show];
            } failure:^{
                [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [SVProgressHUD showSuccessWithStatus:@"发布成功！审核通过后展示"];
                    if (success) {
                        success();
                    }
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }];
        };
    };
}

/// 点赞点击回调
- (void)detailBottomViewPraiseBtnDidClicked:(UIView *)bottomView btn:(FMDetailBottomViewRightTopNumView *)praiseBtn {
    if ([FMHelper checkLoginStatus]) {
        [HttpRequestTool notePraiseWithNoteId:self.detailModel.noteId start:^{
            praiseBtn.userInteractionEnabled = NO;
        } failure:^{
            praiseBtn.userInteractionEnabled = YES;
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            praiseBtn.userInteractionEnabled = NO;
            if ([dic[@"status"] isEqualToString:@"1"]) {
                [SVProgressHUD dismiss];
                [[FMUserDataSyncManager sharedManager] likeNote:self.detailModel.noteId];
                self.detailModel.satisfiedNums = [NSString stringWithFormat:@"%ld",self.detailModel.satisfiedNums.integerValue + 1];
                
                self.bottomView.praised = YES;
                self.bottomView.praiseNum = self.detailModel.satisfiedNums.integerValue;
                
                [[NSNotificationCenter defaultCenter] postNotificationName:kNotePriasedNotification object:nil userInfo:@{@"noteId" : self.detailModel.noteId}];
            } else {
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    }
}

/// 评论数(正文）按钮的点击切换
- (void)detailBottomViewCommentNumBtnDidClicked:(UIView *)bottomView btn:(FMCommentNumBtn *)commentNumBtn {
    if (commentNumBtn.showCommentIcon) {
        self.scrollOffsetY = self.tableView.contentOffset.y;
        
        // 获取tableView的内容高度和可视区域高度
        CGFloat contentHeight = self.tableView.contentSize.height;
        CGFloat visibleHeight = self.tableView.bounds.size.height;
        CGRect rectForSection = [self.tableView rectForSection:2];
        if (rectForSection.origin.y <= contentHeight - visibleHeight) {
            // 内容足够长，滚动到section顶部
            [self.tableView setContentOffset:CGPointMake(0, rectForSection.origin.y) animated:YES];
        } else {
            // 内容不够长，滚动到最后
            [self.tableView setContentOffset:CGPointMake(0, contentHeight - visibleHeight) animated:YES];
        }
    } else {
        [self.tableView setContentOffset:CGPointMake(0, self.scrollOffsetY) animated:YES];
        
        self.scrollOffsetY = 0;
    }
}

/// 快捷回复
- (void)detailBottomViewQuickCommentBtnDidClicked:(UIView *)bottomView btn:(UIButton *)quickRelpyBtn {
    if ([FMHelper checkLoginStatus]) {
        NSString *str = self.bottomView.commentBtn.titleLabel.text;
        [HttpRequestTool noteCommentPublishWithNoteId:self.detailModel.noteId commentLev:@"1" parentCommentid:nil commentContent:str start:^{
            [SVProgressHUD show];
            quickRelpyBtn.enabled = NO;
        } failure:^{
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            quickRelpyBtn.enabled = YES;
        } success:^(NSDictionary *dic) {
            quickRelpyBtn.enabled = YES;
            if ([dic[@"status"] isEqualToString:@"1"]) {
                [SVProgressHUD showSuccessWithStatus:@"发布成功！审核通过后展示"];

                NSArray *quickComments = (NSArray *)[FMUserDefault getSeting:AppInit_Quick_Comment];
                if ([quickComments isKindOfClass:[NSArray class]] && quickComments.count) {
                    NSUInteger randomIndex = arc4random_uniform((uint32_t)quickComments.count);
                    NSString *randomComment = quickComments[randomIndex];
                    [self.bottomView.commentBtn setTitle:randomComment forState:UIControlStateNormal];
                } else {
                    [self.bottomView.commentBtn setTitle:@"" forState:UIControlStateNormal];
                }
            } else {
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    }
}

#pragma mark - NSNotification
- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

- (void)appWillResignActive {
    if (self.needAddTimeCountFlag == 1) {
        self.needAddTimeCountFlag = 2;
        self.focusReminderFlag = 2;
    }
}

- (void)appDidBecomeActive {
    if (self.needAddTimeCountFlag == 2) {
        self.needAddTimeCountFlag = 1;
        self.focusReminderFlag = 1;
    }
}

- (void)networkChanged {
    if ([FMNetworkStatusMonitor sharedMonitor].currentStatus == NetworkStatusCellular) {
        if ([[FMHelper getCurrentVC] isEqual:self]) {
            [FMProgressHUD showTextOnlyInView:self.controlView withText:@"当前处于非wifi环境,请注意流量消耗"];
        }
    }
}

- (void)wxPayNoteSuccess:(NSNotification *)noti {
    NSDictionary *dic = noti.userInfo;
    NSString *noteId = dic[@"noteId"];
    if ([noteId isEqualToString:self.detailModel.noteId]) {
        FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
        [popView show];
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            
            //更新权限 更新播放视频组件状态
            self.detailModel.authority = @"1";
            self.detailModel.tryPlaySecond = @"0";
            self.currentPalyStatus = 0;
            self.detailModel.playStatus = 0;
            self.controlView.model = self.detailModel;
            [self.tableView reloadData];
            
            [self pageBrowseTimeCountDownWithStartTime:[dic[@"systemTime"] longLongValue]];
        });
        
    }
}

#pragma mark - Getter/Setter
- (FMNoteDetailNavView *)navView {
    if (!_navView) {
        _navView = [[FMNoteDetailNavView alloc] init];
        _navView.titleLabel.text = @"笔记详情";
        WEAKSELF
        _navView.backBlock = ^{
            [__weakSelf.navigationController popViewControllerAnimated:YES];
        };
        _navView.deleteBlock = self.deleteBlock;
    }
    
    return _navView;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped delegate:self dataSource:self viewController:self headerTarget:self headerAction:@selector(headerAction) footerTarget:self footerAction:@selector(footerAction)];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_tableView registerCellClass:[FMCommentCell class]];
        [_tableView registerCellClass:[FMNoteDetailHeaderCell class]];
        [_tableView registerCellClass:[FMNoteDetailLiveHeaderTabCell class]];
        [_tableView registerCellClass:[FMDetailHtmlCell class]];
        [_tableView registerCellClass:[FMNoteDetailHeaderThirdCell class]];
        [_tableView registerCellClass:[FMNoteRetweetsDetailTopCell class]];
        [_tableView registerCellClass:[FMNoteDetailRecommendedNoteCell class]];
        _tableView.backgroundColor = UIColor.up_contentBgColor;
        _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, CGFLOAT_MIN)];
    }
    
    return _tableView;
}

- (ZLTagLabel *)mentionStockLabel {
    if (!_mentionStockLabel) {
        _mentionStockLabel = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:FMWhiteColor backgroundColor:FMRedColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _mentionStockLabel.widthPadding = 30.0f;
        _mentionStockLabel.text = @"提及股票";
        _mentionStockLabel.userInteractionEnabled = YES;
        WEAKSELF
        [_mentionStockLabel addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
            __weakSelf.mentionStockView.authority = __weakSelf.detailModel.authority.integerValue == 1;
            [__weakSelf.mentionStockView showWithAnimation:YES];
            __weakSelf.isShowingMentionView = YES;
        }]];
    }
    
    return _mentionStockLabel;
}

- (FMNoteMentionStockView *)mentionStockView {
    if (!_mentionStockView) {
        WEAKSELF
        _mentionStockView = [[FMNoteMentionStockView alloc] initWithDataArr:__weakSelf.detailModel.noteStocks closeBlock:^{
            __weakSelf.isShowingMentionView = NO;
        }];
        _mentionStockView.unlockBlock = ^{
            [__weakSelf.tableView setContentOffset:CGPointZero animated:YES];
        };
    }
    
    return _mentionStockView;
}

- (FMDetailBottomView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[FMDetailBottomView alloc] init];
        FMTaskConfigModel *taskConfig = [FMUserDefault getUnArchiverDataForKey:AppInit_Task];
        if (taskConfig.taskDic[@(FMTaskTypeComment)].isEnable) {
            [_bottomView.commentBtn setTitle:[NSString stringWithFormat:@"发表精彩评论免费获取%zd积分", (taskConfig.taskDic[@(FMTaskTypeComment)]).awardValue] forState:UIControlStateNormal];
        } else {
            [_bottomView.commentBtn setTitle:@"发表精彩评论" forState:UIControlStateNormal];
        }
    }
    return _bottomView;
}

- (FMNoteDetailCommentInputView *)commentView {
    if (!_commentView) {
        _commentView = [[FMNoteDetailCommentInputView alloc] init];
        _commentView.placeholderText = @"说点什么吧，和老师互动";
    }
    return _commentView;
}

- (NSMutableArray *)recommendNotes {
    if (!_recommendNotes) {
        _recommendNotes = [NSMutableArray array];
    }
    
    return _recommendNotes;
}

- (NSMutableArray *)commentFrames {
    if (!_commentFrames) {
        _commentFrames = [NSMutableArray array];
    }
    return _commentFrames;
}

- (void)setRefreshStatistilNum:(NSInteger)refreshStatistilNum {
    _refreshStatistilNum = refreshStatistilNum;
    if (refreshStatistilNum >= 3) {
        [SVProgressHUD dismiss];
        _refreshStatistilNum = 0;  
        if (self.detailModel.deleteFlag.integerValue == 1) { // 笔记未被删除
            self.tableView.hidden = NO;
            self.bottomView.hidden = NO;
              
            [self.tableView reloadData];

            if (self.detailModel.noteType.integerValue == 5) {
                //配置播放器
                [self configPlayer];
            }
            // 获取笔记打赏信息 (在笔记详情和评论接口完成后 在获取打赏信息)
            [self requestRewardRecord];
            
            if (self.detailModel.noteStocks.count) {
                self.mentionStockLabel.hidden = NO;
                self.mentionStockLabel.text = [NSString stringWithFormat:@"提及股票(%zd)", self.detailModel.noteStocks.count];
                CGSize textSize = [self.mentionStockLabel.text sizeWithFont:FontWithSize(14) andSize:CGSizeMake(CGFLOAT_MAX, 36)];
                [self.mentionStockLabel layerAndBezierPathWithRect:CGRectMake(0, 0, ceil(textSize.width) + 30, 36) cornerRadii:CGSizeMake(18, 18) byRoundingCorners:UIRectCornerTopLeft|UIRectCornerBottomLeft];
            } else {
                self.mentionStockLabel.hidden = YES;
            }
        } else {
            // 显示被删除的占位页
            self.tableView.tableFooterView = nil;
            if(self.detailModel.noteId.length > 0) {
                NSString *str;
                if (self.detailModel.deleteFlag.integerValue == -1) {
                    str = [NSString stringWithFormat:@"因违背社区公约,该内容已被管理员删除\n\n删除原因：%@", self.detailModel.deleteReason];
                } else if (self.detailModel.deleteFlag.integerValue == -2) {
                    str = @"该内容已被作者删除";
                } else {
                    str = @"内容不存在";
                }
                dispatch_async(dispatch_get_main_queue(), ^{
                    NSMutableAttributedString *attr = [[NSMutableAttributedString alloc] initWithString:str attributes:@{NSFontAttributeName : FontWithSize(15), NSForegroundColorAttributeName : ColorWithHex(0x666666)}];
                    [self.view showNoDataViewWithImage:ImageWithName(@"note_delete_placeholder") attributedString:attr offsetY:UI_SAFEAREA_TOP_HEIGHT + 80];
                    self.navView.shareBtn.hidden = YES;
                    self.navView.moreBtn.hidden = YES;
                    [self.view bringSubviewToFront:self.navView];
                });
            }
        }
    }
}

- (FMNoteDetailPlayerControlView *)controlView {
    if (!_controlView) {
        _controlView = [[FMNoteDetailPlayerControlView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_WIDTH*9/16)];
        _controlView.activity.center = _controlView.center;
        _controlView.prepareShowLoading = YES;
        _controlView.prepareShowControlView = YES;
        _controlView.delegate = self;
    }
    return _controlView;
}

- (FMNoteRewardAmountView *)rewardView {
    if (!_rewardView) {
        _rewardView = [[FMNoteRewardAmountView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT)];
    }
    return _rewardView;
}

- (FMAskCodePopView *)askCommentView {
    if (!_askCommentView) {
        _askCommentView = [[FMAskCodePopView alloc] initWithCouponFrame:[UIScreen mainScreen].bounds];
    }
    return _askCommentView;
}

- (FMBigCastFocusReminderView *)focusReminderView {
    if (!_focusReminderView) {
        _focusReminderView = [FMBigCastFocusReminderView new];
    }
    
    return _focusReminderView;
}


#pragma mark- 控制横竖屏幕
//  是否支持自动转屏
- (BOOL)shouldAutorotate
{
    return NO;
}

// 支持哪些转屏方向
- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    if (self.player.isFullScreen) {
        return UIInterfaceOrientationMaskLandscape;
    }
    return UIInterfaceOrientationMaskPortrait;
}

@end

