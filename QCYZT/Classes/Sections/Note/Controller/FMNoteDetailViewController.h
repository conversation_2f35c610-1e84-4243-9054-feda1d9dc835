//
//  FMNoteDetailViewController.h
//  QCYZT
//
//  Created by th on 16/12/21.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface FMNoteDetailViewController : UIViewController

@property (nonatomic,copy) NSString *noteId;
/// 滚动到评论区
@property (nonatomic, assign) BOOL scrollToComment;
/// 在详情编辑发布后 需要经过合规 原始数据在列表中需要移除  在详情删除数据后 列表数据也需要移除
@property (nonatomic,copy) void(^deleteBlock) ();

// 会员中心访问埋点功能ID（可选，用于会员中心笔记详情页）
@property (nonatomic, assign) NSInteger memberCenterFunctionId;


@end
