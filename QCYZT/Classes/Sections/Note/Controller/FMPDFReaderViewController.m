//
//  FMPDFReaderViewController.m
//  QCYZT
//
//  Created by <PERSON> on 2018/4/26.
//  Copyright © 2018年 LZKJ. All rights reserved.
//

#import "FMPDFReaderViewController.h"
#import "FileManagerTool.h"
#import "NetworkManager.h"
#import "MemberCenterVisitManager.h"

@interface FMPDFReaderViewController ()<UIDocumentInteractionControllerDelegate,UIScrollViewDelegate>

@property (nonatomic, strong) UILabel *progressLabel;
@property (nonatomic, strong) UIProgressView *progressView;
@property (nonatomic, strong) WKWebView *webView;

@property (nonatomic, strong) UIDocumentInteractionController *documentController;

@end

@implementation FMPDFReaderViewController

- (UILabel *)progressLabel {
    if (!_progressLabel) {
        _progressLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH - 100, 20)];
        _progressLabel.textAlignment = NSTextAlignmentCenter;
        _progressLabel.centerX = self.view.centerX;
        _progressLabel.centerY = self.view.centerY - 120;
        _progressLabel.font = FontWithSize(16);
        _progressLabel.textColor = FMNavColor;
        [self.view addSubview:_progressLabel];
    }
    
    return _progressLabel;
}

- (UIProgressView *)progressView{
    if (!_progressView) {
        _progressView = [[UIProgressView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH - 100, 2)];
        _progressView.centerX = self.view.centerX;
        _progressView.centerY = self.view.centerY - 100;
        _progressView.progressTintColor = FMNavColor;
        _progressView.trackTintColor = [UIColor clearColor];
        [self.view addSubview:_progressView];
    }
    return _progressView;
}

- (WKWebView *)webView {
    if (!_webView) {
        _webView = [[WKWebView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT - UI_SAFEAREA_TOP_HEIGHT - UI_SAFEAREA_BOTTOM_HEIGHT)];
        _webView.scrollView.backgroundColor = UIColor.up_contentBgColor;
        [self.view addSubview:_webView];
    }
    return _webView;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.up_contentBgColor;
    if (!self.title.length) {
        self.title = @"阅读附件";
    }

    // 会员中心访问埋点 - 进入页面
    if (self.memberCenterFunctionId > 0) {
        [[MemberCenterVisitManager sharedManager] trackPageEnter:self functionId:(MemberCenterFunctionType)self.memberCenterFunctionId];
    }
        
    // 生成唯一文件名
    NSString *uniqueFileName = [NetworkManager uniqueFileNameForURL:self.pdfUrl];
    
    // 检查.pdf扩展名的文件是否存在
    NSString *pdfFileName = [uniqueFileName stringByAppendingPathExtension:@"pdf"];
    NSString *pdfPath = [FileManagerTool getPathAtCachesWithDirectoryName:@"PDF" fileName:pdfFileName];
    
    if (pdfPath.length) { // 已经下载，直接加载
        [self addPDFViewForPath:pdfPath];
    } else {
        // 尝试检查其他可能的扩展名（处理历史数据）
        NSArray *arr = [self.pdfUrl componentsSeparatedByString:@"/"];
        NSString *lastComponent = [arr lastObject];
        NSString *urlExt = [lastComponent pathExtension];
        
        if (urlExt.length > 0) {
            NSString *altFileName = [uniqueFileName stringByAppendingPathExtension:urlExt];
            NSString *altPath = [FileManagerTool getPathAtCachesWithDirectoryName:@"PDF" fileName:altFileName];
            
            if (altPath.length) {
                [self addPDFViewForPath:altPath];
                return;
            }
        }
        
        // 未找到任何匹配文件，下载到cachePath路径
        [self downLoadPDFfile:[FileManagerTool createDirAtCachesWithDirectoryName:@"PDF"]];
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self configNavWhiteColorWithCloseSEL:@selector(backArrowClicked)];
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)setShowShare:(BOOL)showShare {
    _showShare = showShare;
    
    if (showShare) {
        UIBarButtonItem *item = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemAction target:self action:@selector(navShareBtnClick)];
        self.navigationItem.rightBarButtonItem = item;
    } else {
        self.navigationItem.rightBarButtonItem = nil;
    }
}


- (void)downLoadPDFfile:(NSString *)catchPath{
    WEAKSELF;
    [HttpRequestTool downLoadFileRequestWithOperations:nil SavePath:catchPath UrlString:self.pdfUrl success:^(NSDictionary *dic) {
        [__weakSelf addPDFViewForPath:dic[__weakSelf.pdfUrl]];
    } failure:^{
        if (__weakSelf.progressView.progress < 1.0) {
            [SVProgressHUD showErrorWithStatus:@"载入出错"];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [__weakSelf.navigationController popViewControllerAnimated:YES];
            });
        }
    } progress:^(float progress) {
        dispatch_sync(dispatch_get_main_queue(), ^{
            [__weakSelf.progressView setProgress:progress animated:YES];
            __weakSelf.progressLabel.text = [NSString stringWithFormat:@"加载进度:%.1f%%", progress * 100];
            if (progress >= 1.0) {
                [__weakSelf.progressView removeFromSuperview];
                [__weakSelf.progressLabel removeFromSuperview];
            }
        });
    }];
}

//加载PDF视图
- (void)addPDFViewForPath:(NSString *)path{
    [self.webView loadFileURL:[NSURL fileURLWithPath:path] allowingReadAccessToURL:[NSURL fileURLWithPath:path]];
}

#pragma mark - 分享
- (void)navShareBtnClick{
    NSArray *urlArr = [self.pdfUrl componentsSeparatedByString:@"/"];
    NSString *path = [[FileManagerTool createDirAtCachesWithDirectoryName:@"PDF"] stringByAppendingString:[NSString stringWithFormat:@"/%@",[urlArr lastObject]]];
    if (![[NSFileManager defaultManager] fileExistsAtPath:path]) {
        NSLog(@"文件路径无效：%@", path);
        return;
    }
    self.documentController = [UIDocumentInteractionController interactionControllerWithURL:[NSURL fileURLWithPath:path]];
    self.documentController.delegate = self;
    [self.documentController presentOptionsMenuFromRect:self.view.bounds inView:self.view animated:YES];
}

- (void)dealloc {
    // 会员中心访问埋点 - 退出页面
    if (self.memberCenterFunctionId > 0) {
        [[MemberCenterVisitManager sharedManager] trackPageExit:self functionId:(MemberCenterFunctionType)self.memberCenterFunctionId];
    }
}

@end
