//
//  FMAskCodeModel.h
//  QCYZT
//  此模型为列表语音问股和视频问股模型 (接口只处理了此部分)
//  Created by shumi on 2022/12/6.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FMBigCastCommonModel.h"
#import "EnablePayModel.h"
NS_ASSUME_NONNULL_BEGIN

@interface QuestionPerm :NSObject
@property (nonatomic , copy) NSString              * audioTxt;
/// 权限（1:没有权限 2:免费看  3:已经看过 4：我问的 5：我回答的）
@property (nonatomic , copy) NSString              * type;
@end

@interface FMAskCodeModel : NSObject
/// 视频问股时长
@property (nonatomic , copy) NSString              * answerVideoTime;
/// 视频问股url
@property (nonatomic , copy) NSString              * answerVideoUrl;
/// 投顾数据模型
@property (nonatomic , strong) FMBigCastCommonModel              * bignameDto;
/// 在question_answer_flag为1时： 0/1/-1  待审核/通过/驳回
@property (nonatomic , copy) NSString              * checkStatus;
/// 跳转协议链接
@property (nonatomic , copy) NSString              * contentAction;
///内容类型  0语音问股  1 视频问股
@property (nonatomic , copy) NSString              * contentFileType;
/// 免责声明
@property (nonatomic , copy) NSString              * declareContent;
/// 过期状态（1过期，0未过期） 我的提问专用
@property (nonatomic , copy) NSString              * expireStatus;
/// 问股id
@property (nonatomic , copy) NSString              * askCodeId;
/// 问股收听人数
@property (nonatomic , copy) NSString              * listenerNums;
@property (nonatomic, copy) NSString *virtualListenerNums; // 虚拟收听人数

/// 语音长度
@property (nonatomic , copy) NSString              * questionAnswerLength;
/// 提问内容
@property (nonatomic , copy) NSString              * questionContent;
@property (nonatomic, strong) NSMutableAttributedString   * questionContentAttrStr;

/// 视频问股封面
@property (nonatomic , copy) NSString              * questionImg;
/// 权限数据模型
@property (nonatomic , strong) QuestionPerm              * questionPerm;
/// 问股价格
@property (nonatomic , copy) NSString              * questionPrice;
/// 偷听价格
@property (nonatomic , copy) NSString              * listenPriceStr;
/// 问股提问时间
@property (nonatomic , assign) long                  questionTime;
/// 问股回答时间
@property (nonatomic , assign) long                  answerTime;
/// 点赞人数
@property (nonatomic , copy) NSString              * satisfiedNums;
@property (nonatomic, copy) NSString *virtualSatisfiedNums; // 虚拟点赞人数

/// 问股状态 1已回答 0未回答 -1已过期
@property (nonatomic , copy) NSString              * questionAnswerFlag;
@property (nonatomic,assign) long createTime;
/// 评分等级
@property (nonatomic,assign) NSInteger starLevel;
@property (nonatomic,copy) NSString *feedback;

@property (nonatomic, copy) NSString *vipMessage;
@property (nonatomic, copy) NSString *stockPosition;


#pragma mark --------- 模型注释的字段 留待以后问股详情使用 ---------
/// 驳回理由字段
@property (nonatomic,copy) NSString *rejectReason;

@property (nonatomic , copy) NSString              * tryPlaySecond;
@property (nonatomic , assign) NSInteger              updateTime;
@property (nonatomic , copy) NSString              * userTitle;
@property (nonatomic , copy) NSString              * userType;
@property (nonatomic , copy) NSString              * questionUserName;
@property (nonatomic , copy) NSString              * questionUserid;
@property (nonatomic , copy) NSString              * questionIco;
@property (nonatomic , copy) NSString              * noticerNums;
@property (nonatomic , copy) NSString              * orderType;
@property (nonatomic , copy) NSString              * queryKeys;
@property (nonatomic , copy) NSString              * incomeTotleCoin;
@property (nonatomic , copy) NSString              * isEnable;
@property (nonatomic , copy) NSString              * isInSelect;
@property (nonatomic , copy) NSString              * isPublic;
/// 过期时间
@property (nonatomic , assign) NSInteger              expireTime;
@property (nonatomic , copy) NSString              * answerWxId;
@property (nonatomic , copy) NSString              * answerWxServer;
@property (nonatomic , copy) NSString              * answerAudioType;
@property (nonatomic , copy) NSString              * answerGood;
@property (nonatomic , copy) NSString              * answerIco;
@property (nonatomic , copy) NSString              * answerOssUrl;
@property (nonatomic , copy) NSString              * answerProfiles;
@property (nonatomic , copy) NSString              * answerUserid;
@property (nonatomic , copy) NSString              * answerUsername;
/// 支付模型
@property (nonatomic, strong) NSArray <EnablePayModel *> *enablePayModel;
/// 没有可用支付模式的提示字符
@property (nonatomic,copy) NSString *noPayModelText;
/// 没有可用支付模式状态码
@property (nonatomic,copy) NSString *noPayModelCode;
/// 支付按钮字符
@property (nonatomic,copy) NSString *payText;


#pragma mark - 辅助字段
@property (nonatomic,assign) NSInteger playStatus;


@end

NS_ASSUME_NONNULL_END
