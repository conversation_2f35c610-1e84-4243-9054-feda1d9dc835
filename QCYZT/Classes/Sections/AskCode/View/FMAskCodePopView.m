//
//  FMCommentView.m
//  QCYZT
//
//  Created by th on 17/2/23.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "FMAskCodePopView.h"
#import "NSString+emoji.h"
#import "IQKeyboardManager.h"
#import "FMPayMentCouponSelectVC.h"
#import "HttpRequestTool+UserCenter.h"
#import "FMAskCodeStockResultTableView.h"
#import "CacheRequestManager.h"
#import "HttpRequestTool+Search.h"
#import "FMNewSearchStockModel.h"
#import "NSString+characterJudge.h"
#import "FMProgressHUD.h"

@interface FMAskCodePopView() <UITextViewDelegate>

@property (nonatomic,weak) UIView *contentView;
@property (nonatomic,weak) UIButton *cancelBtn;
//@property (nonatomic,weak) UILabel *placeholderLb;

@property (nonatomic, strong) UIView *couponBgView;
@property (nonatomic, strong) UILabel *couponNameLB;
@property (nonatomic, strong) UILabel *couponValueLB;
@property (nonatomic, strong) UIImageView *couponArrowImgV;
@property (nonatomic, strong) ZLTagLabel *firstRechargeLabel;              // 首充优惠
@property (nonatomic, strong) NSArray<FMCouponTableModel *> *availableCoupons; // 可用卡券列表
@property (nonatomic, strong) UIStackView *stackView;

// 积分相关属性
@property (nonatomic, strong) UIView *pointsBgView;                        // 积分选择背景视图
@property (nonatomic, strong) UILabel *pointsTitleLabel;                   // 积分标题
@property (nonatomic, strong) UIButton *pointsHelpButton;                  // 积分帮助按钮
@property (nonatomic, strong) UILabel *pointsNumLabel;                     // 积分数量标签
@property (nonatomic, strong) YYLabel *pointsDescLabel;                    // 积分描述标签
@property (nonatomic, strong) UIButton *pointsCheckboxButton;              // 积分勾选框
@property (nonatomic, assign) NSInteger userPoints;                        // 用户积分数
@property (nonatomic, assign) NSInteger pointsRatio;                       // 积分与金币兑换比例
@property (nonatomic, assign) BOOL isPointsSelected;                       // 积分是否选中
@property (nonatomic, assign) NSInteger usePoints;                         // 使用的积分数

/// 股票结果View
@property (nonatomic, strong) FMAskCodeStockResultTableView *stockResultView;
@property (nonatomic, strong) UIScrollView *inputViewContentView;
@property (nonatomic, strong) UIView *resultBackView;


@end


@implementation FMAskCodePopView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setUp];
    }
    return self;
}

- (instancetype)initWithCouponFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = false;
        [self setUp];
    }
    return self;
}

- (void)willMoveToSuperview:(UIView *)newSuperview {
    [super willMoveToSuperview:newSuperview];
    
    [IQKeyboardManager sharedManager].enableAutoToolbar = false;
}

- (void)removeFromSuperview {
    [super removeFromSuperview];
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = true;
    [IQKeyboardManager sharedManager].enableAutoToolbar = true;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setUp {
    UIView *bgView = [[UIView alloc] init];
    [self addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    bgView.backgroundColor = ColorWithHexAlpha(0x000000, 0.5);
    [bgView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(removeFromSuperview)]];
    
    UIView *contentView = [[UIView alloc] init];
    [self addSubview:contentView];
    [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(@(0));
        make.height.equalTo(384); // 为非优惠券模式也增加积分UI的高度
    }];
    contentView.backgroundColor = UIColor.up_contentBgColor;
    [contentView layerAndBezierPathWithRect:CGRectMake(0, 0, UI_SCREEN_WIDTH,  414) cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight];
    self.contentView = contentView;
    
    UILabel *publishLb = [[UILabel alloc] init];
    [contentView addSubview:publishLb];
    [publishLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.top.equalTo(10);
        make.right.equalTo(@(-60));
        make.height.equalTo(24);
    }];
    publishLb.textColor = UIColor.up_textSecondary2Color;
    self.publisLb = publishLb;
    
    UIButton *cancelBtn = [[UIButton alloc] init];
    [contentView addSubview:cancelBtn];
    [cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(publishLb);
        make.right.equalTo(@-10);
        make.size.equalTo(CGSizeMake(24, 24));
    }];
    [cancelBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentRight];
    cancelBtn.backgroundColor = [UIColor clearColor];
    [cancelBtn setImage:ImageWithName(@"payview_close") forState:UIControlStateNormal];
    
    [cancelBtn addTarget:self action:@selector(cancel) forControlEvents:UIControlEventTouchUpInside];
    self.cancelBtn = cancelBtn;
    
    UIScrollView *inputView = [[UIScrollView alloc] init];
    inputView.showsHorizontalScrollIndicator = NO;
    [contentView addSubview:inputView];
    [inputView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.equalTo(@(-15));
        make.top.equalTo(publishLb.mas_bottom).offset(13);
    }];
    self.inputViewContentView = inputView;
    
    UIStackView *stackView = [[UIStackView alloc] init];
    stackView.axis = UILayoutConstraintAxisVertical;
    [inputView addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
        make.width.equalTo(UI_SCREEN_WIDTH - 30);
    }];
    self.stackView = stackView;
    
    FMTextView *textView = [[FMTextView alloc] init];
    textView.font = FontWithSize(16);
    textView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    textView.placehoderColor = UIColor.fm_BFBFBF_888888;
    textView.textColor = UIColor.up_textPrimaryColor;
    UI_View_BorderRadius(textView, 5, 1, ColorWithHex(0xe5e5e5));
    textView.delegate = self;
    textView.textContainerInset = UIEdgeInsetsMake(8, 10, 8, 0);
    [stackView addArrangedSubview:textView];
    [textView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(150);
    }];
    self.textView = textView;
    
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:UIColor.up_textSecondaryColor backgroundColor:UIColor.up_contentBgColor numberOfLines:1];
    label.text = @"优惠券:";
    [contentView addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(inputView.mas_bottom).offset((15));
        make.left.equalTo(inputView);
        make.height.equalTo(23);
    }];
    
    [contentView addSubview:self.couponBgView];
    [self.couponBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(-15);
        make.centerY.equalTo(label);
        make.height.equalTo(33);
        make.left.greaterThanOrEqualTo(label.mas_right).offset(15);
    }];
    
    [contentView addSubview:self.pointsBgView];
    [self.pointsBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(-15);
        make.height.equalTo(35);
        make.top.equalTo(self.couponBgView.mas_bottom).offset(10);
        make.left.equalTo(15);
    }];
    
    UIButton *publishBtn = [[UIButton alloc] init];
    [contentView addSubview:publishBtn];
    [publishBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(inputView.mas_bottom).offset(111);
        make.right.equalTo(@(-10));
        make.width.equalTo(UI_SCREEN_WIDTH - 30);
        make.height.equalTo(45);
        make.bottom.mas_equalTo(-10);
    }];
    publishBtn.layer.cornerRadius = 45 / 2.0;
    publishBtn.backgroundColor = ColorWithHex(0xababab);
    publishBtn.titleLabel.font = [UIFont systemFontOfSize:17];
    [publishBtn setTitleColor:FMWhiteColor forState:UIControlStateNormal];
    [publishBtn setTitle:@"发送" forState:UIControlStateNormal];
    [publishBtn addTarget:self action:@selector(publish) forControlEvents:UIControlEventTouchUpInside];
    publishBtn.userInteractionEnabled = NO;
    self.publishBtn = publishBtn;
    
    [contentView addSubview:self.firstRechargeLabel];
    [self.firstRechargeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(publishBtn);
        make.top.equalTo(publishBtn.mas_top).offset(-12.5);
        make.height.equalTo(25);
    }];
    self.firstRechargeLabel.hidden = YES;
    
    publishLb.font = [UIFont boldSystemFontOfSize:17];
    cancelBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillChangeFrame:) name:UIKeyboardWillChangeFrameNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(removeFromSuperview) name:kALiMessageAffirm object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(uptheme_themeDidUpdate) name:kUPThemeDidChangeNotification object:nil];
}

// 股票输入区域UI
- (void)configStockInputView {
    if(self.stockTF.superview) return;
    
    UILabel *title = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:UIColor.up_textSecondaryColor backgroundColor:UIColor.up_contentBgColor numberOfLines:1];
    title.text = @"咨询的股票：";
    [self.stackView insertArrangedSubview:title atIndex:0];
    [title mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(23);
    }];
    [self.stackView setCustomSpacing:8 afterView:title];
    
    UITextField *stockTF = [[UITextField alloc] init];
    stockTF.font = FontWithSize(16);
    stockTF.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    stockTF.clearButtonMode = UITextFieldViewModeWhileEditing;
    stockTF.attributedPlaceholder = [[NSMutableAttributedString alloc] initWithString:@"请输入股票名称或代码" attributes:@{NSFontAttributeName : FontWithSize(16), NSForegroundColorAttributeName : UIColor.fm_BFBFBF_888888}];
    UI_View_Radius(stockTF, 5);
    stockTF.leftViewMode = UITextFieldViewModeAlways;
    stockTF.leftView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 15, 45)];
    [self.stackView insertArrangedSubview:stockTF atIndex:1];
    [stockTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(45);
    }];
    [stockTF addTarget:self action:@selector(textFieldDidChange:) forControlEvents:UIControlEventEditingChanged];
    [stockTF becomeFirstResponder];
    stockTF.textColor = UIColor.up_textPrimaryColor;
    self.stockTF = stockTF;
    
    [self.stackView setCustomSpacing:15 afterView:stockTF];
    
    
    
UILabel *title1 = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:UIColor.up_textSecondaryColor backgroundColor:UIColor.up_contentBgColor numberOfLines:1];
    title1.text = @"提问的内容：";
    [self.stackView insertArrangedSubview:title1 atIndex:2];
    [title1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(23);
    }];
    [self.stackView setCustomSpacing:8 afterView:title1];
    
    
    [self addSubview:self.resultBackView];
    [self.resultBackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.stockTF.mas_bottom).offset(5);
        make.left.equalTo(15);
        make.right.equalTo(-15);
    }];
    
    [self.resultBackView addSubview:self.stockResultView];
    [self.stockResultView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    if (!self.firstRechargeLabel.hidden) {
        self.firstRechargeLabel.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xF5CE62), ColorWithHex(0xFFE59B)] withFrame:self.firstRechargeLabel.bounds direction:GradientDirectionLeftToRight];
        [self.firstRechargeLabel layerAndBezierPathWithRect:self.firstRechargeLabel.bounds cornerRadii:CGSizeMake(12.5, 12.5) byRoundingCorners:UIRectCornerTopLeft|UIRectCornerTopRight|UIRectCornerBottomRight];
    }
}

- (void)didMoveToSuperview {
    if (self.askPrice.length == 0) {
        [self.textView becomeFirstResponder];
    } else {
        [self.stockTF becomeFirstResponder];
    }
    
    // 确保积分数据已初始化
    if (self.askPrice.length > 0 && self.userPoints == 0) {
        [self initializePointsData];
    }
    
    // 强制显示积分UI用于测试（如果有价格）
    if (self.askPrice.floatValue > 0 && self.pointsTitleLabel) {
        self.pointsTitleLabel.hidden = NO;
        self.pointsHelpButton.hidden = NO;
        self.pointsBgView.hidden = NO;
    }
}

- (void)cancel {
    [self removeFromSuperview];
}

- (void)publish {
    if ([FMHelper checkLoginStatus]) {
        if ([self.publishBtn.titleLabel.text containsString:@"余额不足"]) {
            [ProtocolJump jumpWithUrl:@"qcyzt://recharge"];
            [self cancel];
        } else {
            if ([self characterJudge]) {
                [self removeFromSuperview];
                WEAKSELF;
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    if (__weakSelf.publishAskCodeBlock) {
                        __weakSelf.publishAskCodeBlock([NSString stringWithFormat:@"咨询%@,%@",__weakSelf.stockTF.text,__weakSelf.textView.text], __weakSelf.choosedStockModel);
                    } else if (__weakSelf.publishBlock) {
                        __weakSelf.publishBlock(__weakSelf.textView.text);
                    }
                });
            }
        }
    }
}

#pragma mark - UITextViewDelegate
-(void)textViewDidChange:(UITextView *)textView{
    if (textView.text.length<=0) {
        if ([self.publishBtn.titleLabel.text containsString:@"余额不足"]) {
            [self updatePublishButtonState];
        } else {
            self.publishBtn.backgroundColor = ColorWithHex(0xababab);
            self.publishBtn.userInteractionEnabled = NO;
        }
    }else{
        if ([textView.text isContainEmoji] && !self.supportEmoji) {
            [SVProgressHUD showErrorWithStatus:@"暂不支持输入表情"];
            return;
        }
        
        NSInteger limitNum = self.limitWordNum;
        if (limitNum == 0) {
            limitNum = 100;
        }
        if (textView.text.length > limitNum)
        {
            textView.text = [textView.text substringToIndex:limitNum];
            [SVProgressHUD showInfoWithStatus:[NSString stringWithFormat:@"最多输入%zd个字", limitNum]];
        }
        
        if (self.askPrice.length > 0 && self.stockTF.text.length != 0) {
            [self updatePublishButtonState];
        } else {
            [self updatePublishButtonState];
        }
    }
}

- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    if ([text isContainEmoji] && !self.supportEmoji) {
        [textView resignFirstResponder];
        [SVProgressHUD showErrorWithStatus:@"暂不支持输入表情"];
        return NO;
    }
    return YES;;
}

- (void)textFieldDidChange:(UITextField *)textField {
    if (textField.text.length == 0) {
        self.choosedStockModel = nil;
    }
    
    if (textField.text.length > 8) {
        textField.text = [textField.text substringToIndex:8];
    }
    [self delaySearch];
}

// 对textField文字更新做0.5秒延迟才去搜索，减轻服务器压力
- (void)delaySearch {
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(newSearchKey) object:nil];
    [self performSelector:@selector(newSearchKey) withObject:nil afterDelay:0.5];
}

- (void)newSearchKey {
    if (self.stockTF.text.length) {
        NSString *whereCondition = nil;
        if ([self.stockTF.text isPureNumber]) {
            whereCondition = [NSString stringWithFormat:@"stockName LIKE '%%%@%%' OR stockCode LIKE '%%%@%%'", self.stockTF.text, self.stockTF.text];
        } else if ([self.stockTF.text isPureAlphabet]) {
            whereCondition = [NSString stringWithFormat:@"stockName LIKE '%%%@%%' OR chiSpelling LIKE '%%%@%%'", self.stockTF.text, self.stockTF.text.uppercaseString];
        } else {
            whereCondition = [NSString stringWithFormat:@"stockName LIKE '%%%@%%' OR stockCode LIKE '%%%@%%' OR chiSpelling LIKE '%%%@%%'", self.stockTF.text, self.stockTF.text, self.stockTF.text.uppercaseString];
        }
        
        NSArray *allStocks = [DBManager selectDatasWithClass:[FMNewSearchStockModel class] where:whereCondition order:nil limit:@"200"];
        
        NSMutableArray <FMSearchStockModel *> *arr = [NSMutableArray array];
        for (FMNewSearchStockModel *stockModel in allStocks) {
            FMSearchStockModel *model = [FMSearchStockModel new];
            model.name = stockModel.stockName;
            model.oldStockCode = stockModel.stockCode;
            UPMarketCodeMatchInfo *info = [FMUPDataTool matchInfoWithSetCodeAndCode:stockModel.stockCode];
            model.code = info.code;
            model.setCode = info.setCode;
            model.category = info.category;
            model.origCategory = info.origCategory;
            
            [arr addObject:model];
        }
        self.resultBackView.hidden = arr.count == 0;
        self.inputViewContentView.scrollEnabled = arr.count == 0;
        self.stockResultView.dataArr = arr;
        if (!self.stockResultView.hidden) {
            [self.stockResultView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.equalTo(arr.count > 4 ? 120 : 35 * arr.count);
            }];
            self.resultBackView.size = CGSizeMake(UI_SCREEN_WIDTH - 30, arr.count > 4 ? 120 : 35 * arr.count);
            self.resultBackView.layer.masksToBounds = NO;
            self.resultBackView.layer.shadowColor = ColorWithHexAlpha(0x000000, 0.16).CGColor;
            self.resultBackView.layer.shadowOffset = CGSizeMake(0,0);
            self.resultBackView.layer.shadowRadius = 5;
            self.resultBackView.layer.shadowOpacity = 1;
        }
    }
}

#pragma mark - Notification
- (void)keyboardWillChangeFrame:(NSNotification *)notification
{
    NSDictionary *userInfo = notification.userInfo;
    double duration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    CGRect keyboardF = [userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    
    [UIView animateWithDuration:duration animations:^{
        if (keyboardF.origin.y < UI_SCREEN_HEIGHT) {
            self.transform = CGAffineTransformMakeTranslation(0, - keyboardF.size.height);
        } else {
            self.transform = CGAffineTransformMakeTranslation(0,  0);
        }
    }];
}

#pragma mark - HTTP
- (void)requestCouponList {
    [HttpRequestTool requestPaymentCouponListWithBignameId:self.bignameId consumeType:self.consumeType contentId:self.contentId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD dismiss];
    } success:^(NSDictionary *dic) {
        [SVProgressHUD dismiss];
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.availableCoupons = [NSArray modelArrayWithClass:[FMCouponTableModel class] json:dic[@"data"]];
            [self configCouponNameAndValueShow];
            [self initializePointsData]; // 初始化积分数据
        }
    }];
}

#pragma mark - Private
- (BOOL)characterJudge {
    NSString *inputStr = [self.textView.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if (!inputStr.length) {
        [SVProgressHUD showErrorWithStatus:@"输入内容不能为空~"];
        return NO;
    }
    if (self.stockTF && (!self.stockTF.text.length || !self.choosedStockModel)) {
        [SVProgressHUD showErrorWithStatus:@"请输入并选择您想咨询的股票"];
        return NO;
    }
    
    if ([self.textView.text isContainEmoji] && !self.supportEmoji) {
        [SVProgressHUD showErrorWithStatus:@"暂不支持输入表情"];
        return NO;
    }
    
    return YES;
}

- (void)configCouponNameAndValueShow {
    if (self.selectedCoupon) {
        self.couponValueLB.text = [NSString stringWithFormat:@"-%.0f金币", self.selectedCoupon.value];
        if (self.selectedCoupon.name.length > 8) {
            self.couponNameLB.text = [NSString stringWithFormat:@"%@...",[self.selectedCoupon.name substringToIndex:8]];
        } else {
            self.couponNameLB.text = self.selectedCoupon.name;
        }
        self.couponNameLB.textColor = UIColor.up_textPrimaryColor;
        self.couponArrowImgV.image = [UIImage imageWithTintColor:FMNavColor blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"userCenter_arrow")];
        self.couponBgView.backgroundColor = FMClearColor;
        [self.couponBgView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(0);
        }];
        self.couponId = self.selectedCoupon.couponId;
        self.type = self.selectedCoupon.goodsType;
    } else {
        CGFloat maxAvailableCouponValue = -1;
        for (FMCouponTableModel *model in self.availableCoupons) {
            if (model.isEnable.boolValue) {
                if (model.value > maxAvailableCouponValue) {
                    maxAvailableCouponValue = model.value;
                }
            }
        }
        
        self.couponValueLB.text = @"";
        self.couponId= @"";
        self.type = @"";
        if (maxAvailableCouponValue == -1) { // 没有可用的卡券
            self.couponNameLB.text = @"请选择";
            self.couponNameLB.textColor = UIColor.fm_BFBFBF_888888;
            self.couponArrowImgV.image = ImageWithName(@"userCenter_arrow");
            self.couponBgView.backgroundColor = FMClearColor;
            [self.couponBgView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(0);
            }];
        } else {
            self.couponNameLB.text = [NSString stringWithFormat:@"未选卡券，最高抵扣%.0f金币", maxAvailableCouponValue];
            self.couponNameLB.textColor = FMNavColor;
            self.couponArrowImgV.image = [UIImage imageWithTintColor:FMNavColor blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"userCenter_arrow")];
            self.couponBgView.backgroundColor = ColorWithHex(0xffebeb);
            [self.couponBgView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(-15);
            }];
        }
    }
    
    // 优惠券变化时，如果当前选中积分且积分不够用了，自动取消选中
    if (self.isPointsSelected && [self getPointsShowType] != PointsShowTypeEnable) {
        self.isPointsSelected = NO;
        self.usePoints = 0;
    }
    
    // 更新积分显示和按钮状态
    [self updatePointsDisplay];
    [self updatePublishButtonState];
}

#pragma mark - Setter/Getter
- (void)setPlaceholderText:(NSString *)placeholderText {
    _placeholderText = placeholderText;
    self.textView.placehoder = placeholderText;
}

- (void)setAskPrice:(NSString *)askPrice {
    _askPrice = askPrice;
    
    // 如果有价格，显示积分UI
    if (askPrice.floatValue > 0 && self.pointsTitleLabel && self.pointsTitleLabel.hidden) {
        self.pointsTitleLabel.hidden = NO;
        self.pointsHelpButton.hidden = NO;
        self.pointsBgView.hidden = NO;
    }
    
    [self updatePublishButtonState];
    [self updatePointsDisplay];
    [self configStockInputView];
}


- (UIView *)couponBgView {
    if (!_couponBgView) {
        _couponBgView = [UIView new];
        _couponBgView.backgroundColor = ColorWithHex(0xffebeb);
        UI_View_Radius(_couponBgView, 16.5);
        
        [_couponBgView addSubview:self.couponArrowImgV];
        [self.couponArrowImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(0);
            make.right.equalTo(-15);
        }];
        
        [_couponBgView addSubview:self.couponValueLB];
        [self.couponValueLB mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(0);
            make.right.equalTo(self.couponArrowImgV.mas_left).offset(-5);
        }];
        
        [_couponBgView addSubview:self.couponNameLB];
        [self.couponNameLB mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.couponValueLB.mas_left).offset(0);
            make.centerY.equalTo(0);
            make.left.equalTo(15);
        }];
        
        _couponBgView.userInteractionEnabled = YES;
        WEAKSELF;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
            [__weakSelf endEditing:YES];
            __weakSelf.hidden = YES;
            FMPayMentCouponSelectVC *vc = [[FMPayMentCouponSelectVC alloc] init];
            vc.selectedModel = __weakSelf.selectedCoupon;
            vc.dataArray = __weakSelf.availableCoupons;
            vc.closePage = ^{
                __weakSelf.hidden = NO;
            };
            vc.couponSelectBlock = ^(FMCouponTableModel *selectedCoupon) {
                __weakSelf.hidden = NO;
                __weakSelf.selectedCoupon = selectedCoupon;
                [__weakSelf configCouponNameAndValueShow];
            };
            [__weakSelf.textView resignFirstResponder];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
        [_couponBgView addGestureRecognizer:tap];

    }
    return _couponBgView;
}


- (UILabel *)couponNameLB {
    if (!_couponNameLB) {
        _couponNameLB = [[UILabel alloc]init];
        _couponNameLB.text = @"请选择";
        _couponNameLB.textAlignment = NSTextAlignmentRight;
        _couponNameLB.font = [UIFont systemFontOfSize:16];
        _couponNameLB.textColor = UIColor.fm_BFBFBF_888888;
    }
    return _couponNameLB;
}


- (UILabel *)couponValueLB {
    if (!_couponValueLB) {
        _couponValueLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:FMNavColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    }
    
    return _couponValueLB;
}

- (UIImageView *)couponArrowImgV {
    if (!_couponArrowImgV) {
        _couponArrowImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"userCenter_arrow")];
    }
    
    return _couponArrowImgV;
}

- (ZLTagLabel *)firstRechargeLabel {
    if (!_firstRechargeLabel) {
        _firstRechargeLabel = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:ColorWithHex(0x6a0000) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _firstRechargeLabel.widthPadding = 20;
    }
    
    return _firstRechargeLabel;
}

- (FMAskCodeStockResultTableView *)stockResultView {
    if (!_stockResultView) {
        _stockResultView = [[FMAskCodeStockResultTableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _stockResultView.backgroundColor = UIColor.up_contentBgColor;
        WEAKSELF
        _stockResultView.stockSelectBlock = ^(FMSearchStockModel * _Nonnull model) {
            __weakSelf.stockTF.text = [NSString stringWithFormat:@"%@",model.name];
            __weakSelf.choosedStockModel = model;
            __weakSelf.resultBackView.hidden = YES;
            __weakSelf.inputViewContentView.scrollEnabled = YES;
        };
    }
    return _stockResultView;
}

- (UIView *)resultBackView {
    if (!_resultBackView) {
        _resultBackView = [[UIView alloc] init];
        _resultBackView.hidden = YES;
    }
    return _resultBackView;
}

- (UIView *)pointsBgView {
    if (!_pointsBgView) {
        _pointsBgView = [UIView new];
        _pointsBgView.backgroundColor = FMClearColor;
        
        // 积分标题
        self.pointsTitleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:UIColor.up_textSecondaryColor backgroundColor:UIColor.up_contentBgColor numberOfLines:1];
        self.pointsTitleLabel.text = @"积分";
        [_pointsBgView addSubview:self.pointsTitleLabel];
        [self.pointsTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(0);
            make.centerY.equalTo(0);
        }];
        
        // 问号按钮
        self.pointsHelpButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [self.pointsHelpButton setImage:[UIImage imageNamed:@"pay_wenhao"] forState:UIControlStateNormal];
        [self.pointsHelpButton addTarget:self action:@selector(pointsHelpButtonTapped) forControlEvents:UIControlEventTouchUpInside];
        self.pointsHelpButton.lz_touchAreaInsets = UIEdgeInsetsMake(-10, -10, -10, -10);
        [_pointsBgView addSubview:self.pointsHelpButton];
        [self.pointsHelpButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.pointsTitleLabel.mas_right).offset(3);
            make.centerY.equalTo(self.pointsTitleLabel);
            make.width.height.equalTo(@16);
        }];
        
        // 右侧StackView - 参考PayPointsCell的结构
        UIStackView *rightStackView = [[UIStackView alloc] init];
        rightStackView.axis = UILayoutConstraintAxisHorizontal;
        rightStackView.alignment = UIStackViewAlignmentCenter;
        rightStackView.distribution = UIStackViewDistributionEqualSpacing;
        rightStackView.spacing = 8;
        [_pointsBgView addSubview:rightStackView];
        [rightStackView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(0);
            make.centerY.equalTo(0);
        }];
        
        // 用户积分数量
        self.pointsNumLabel = [[UILabel alloc] init];
        self.pointsNumLabel.font = FontWithSize(16);
        self.pointsNumLabel.textColor = UIColor.up_textPrimaryColor;
        [rightStackView addArrangedSubview:self.pointsNumLabel];
        
        // 描述标签
        self.pointsDescLabel = [[YYLabel alloc] init];
        self.pointsDescLabel.font = FontWithSize(14);
        self.pointsDescLabel.textAlignment = NSTextAlignmentLeft;
        [rightStackView addArrangedSubview:self.pointsDescLabel];
        
        // 勾选框按钮
        self.pointsCheckboxButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [self.pointsCheckboxButton addTarget:self action:@selector(pointsCheckboxButtonTapped) forControlEvents:UIControlEventTouchUpInside];
        self.pointsCheckboxButton.lz_touchAreaInsets = UIEdgeInsetsMake(-10, -10, -10, -10);
        [rightStackView addArrangedSubview:self.pointsCheckboxButton];
        [self.pointsCheckboxButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.equalTo(@20);
        }];
    }

    return _pointsBgView;
}

- (void)setConsumeType:(NSInteger)consumeType {
    _consumeType = consumeType;
    
    if (consumeType == 2) {
        [[CacheRequestManager shared] performRequest:@"allStocks"
                                      cacheInterval:6 * 3600
                                      requestBlock:^(void(^saveData)(id)) {
            [HttpRequestTool getAllStockWithStart:^{
            } failure:^{
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [DBManager clearTableWithClass:[FMNewSearchStockModel class]];
                    [[CacheRequestManager shared] clearCacheForKey:@"allStocks"];
                    
                    NSArray *array = [NSArray modelArrayWithClass:[FMNewSearchStockModel class] json:dic[@"data"]];
                    [DBManager insertBatchUseTransactionWithModels:array finishBlock:^(_Bool issuccess) {
                        if (issuccess) {
                            saveData(@{}); // 随便存一条数据作为有数据的标记
                        }
                    }];
                }
            }];
        } cacheDataBlock:^(id cachedData) {
        }];
    }
    
    // 初始化积分相关数据
    [self initializePointsData];
}

#pragma mark - Points Methods

/// 初始化积分数据
- (void)initializePointsData {
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    self.userPoints = userModel.points;
    self.pointsRatio = 1; // 默认1:1，后续可从配置获取
    self.isPointsSelected = NO;
    self.usePoints = 0;
    
    // 延迟更新显示，确保UI已经创建完成
    dispatch_async(dispatch_get_main_queue(), ^{
        [self updatePointsDisplay];
    });
}

/// 计算可抵扣的金币数量
- (NSInteger)calculateDeductibleCoins {
    NSInteger availablePoints = self.userPoints; // 可用积分
    NSInteger maxDeductibleCoins = availablePoints / self.pointsRatio; // 最大抵扣金币
    
    CGFloat price = self.askPrice.floatValue;
    if (self.selectedCoupon) {
        if (self.selectedCoupon.goodsType.integerValue == 4) { // 专用券
            price = 0;
        } else {
            price -= self.selectedCoupon.value; // 减去优惠券折扣
        }
    }
    price = MAX(price, 0);
    
    NSInteger actualOrderAmount = (NSInteger)price; // 实际订单金额
    return MIN(maxDeductibleCoins, actualOrderAmount);
}

/// 积分展示状态
typedef NS_ENUM(NSUInteger, PointsShowType) {
    PointsShowTypeDisable = -1,
    PointsShowTypeZero = 0,
    PointsShowTypeEnable
};

- (PointsShowType)getPointsShowType {
    CGFloat price = self.askPrice.floatValue;
    if (self.selectedCoupon) {
        if (self.selectedCoupon.goodsType.integerValue == 4) { // 专用券覆盖了订单
            return PointsShowTypeZero;
        } else {
            price -= self.selectedCoupon.value;
        }
    }
    price = MAX(price, 0);
    
    // 抵扣券覆盖了订单，不使用积分
    if (price <= 0) {
        return PointsShowTypeZero;
    }
    
    // 用户积分不够
    if (self.userPoints < self.pointsRatio) {
        return PointsShowTypeDisable;
    }

    return PointsShowTypeEnable;
}

/// 获取当前使用的积分数
- (NSInteger)getCurrentUsePoints {
    if (!self.isPointsSelected) {
        return 0;
    }
    return [self calculateDeductibleCoins] * self.pointsRatio;
}

/// 更新积分显示
- (void)updatePointsDisplay {
    // 确保UI组件已经创建
    if (!self.pointsNumLabel || !self.pointsDescLabel || !self.pointsCheckboxButton) {
        return;
    }
    
    // 设置积分数量显示
    self.pointsNumLabel.text = [NSString stringWithFormat:@"%ld", (long)self.userPoints];

    PointsShowType status = [self getPointsShowType];
    if (status == PointsShowTypeDisable) {
        // 状态1：积分不够 - "暂无可用"
        self.pointsDescLabel.text = @"暂无可用";
        self.pointsDescLabel.textColor = UIColor.up_textSecondary2Color;
        self.pointsCheckboxButton.hidden = YES;
    } else if (status == PointsShowTypeZero) {
        self.pointsCheckboxButton.hidden = YES;
        NSInteger deductibleCoins = [self calculateDeductibleCoins];
        [self setupUnselectedPointsDescLabel:deductibleCoins];
    } else {
        NSInteger deductibleCoins = [self calculateDeductibleCoins];
        self.pointsCheckboxButton.hidden = NO;

        if (self.isPointsSelected) {
            // 状态3：勾选时的样式 - "-**金币"，文本全部标红
            [self setupSelectedPointsDescLabel:deductibleCoins];
            [self.pointsCheckboxButton setImage:[UIImage imageNamed:@"paytype_select"] forState:UIControlStateNormal];
        } else {
            // 状态2：有可用积分但未勾选 - "此单可抵扣**金币，去使用>"
            [self setupUnselectedPointsDescLabel:deductibleCoins];
            [self.pointsCheckboxButton setImage:[UIImage imageNamed:@"unchecked"] forState:UIControlStateNormal];
        }
    }
}

- (void)setupSelectedPointsDescLabel:(NSInteger)deductibleCoins {
    NSString *text = [NSString stringWithFormat:@"-%ld金币", (long)deductibleCoins];
    NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:text];
    attributedText.yy_color = UIColor.up_riseColor;
    attributedText.yy_font = FontWithSize(16);
    self.pointsDescLabel.attributedText = attributedText;
}

- (void)setupUnselectedPointsDescLabel:(NSInteger)deductibleCoins {
    NSString *text;
    if (deductibleCoins == 0) {
        text = @"此单可抵扣0金币";
    } else {
        text = [NSString stringWithFormat:@"此单可抵扣%ld金币，去使用 ", (long)deductibleCoins];
    }
    
    NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:text];

    // 设置默认颜色
    attributedText.yy_color = UIColor.up_textSecondaryColor;
    attributedText.yy_font = FontWithSize(16);

    // 数字+金币标红
    NSString *numberText = [NSString stringWithFormat:@"%ld金币", (long)deductibleCoins];
    NSRange numberRange = [text rangeOfString:numberText];
    if (numberRange.location != NSNotFound) {
        [attributedText yy_setColor:UIColor.up_riseColor range:numberRange];
    }
    
    if (deductibleCoins == 0) {
        self.pointsDescLabel.attributedText = attributedText;
        return;
    }

    // 添加箭头图片
    UIImage *arrowImage = [UIImage imageNamed:@"pay_arrow"];
    if (arrowImage) {
        NSMutableAttributedString *arrowAttachment = [NSMutableAttributedString yy_attachmentStringWithContent:arrowImage contentMode:UIViewContentModeCenter attachmentSize:arrowImage.size alignToFont:FontWithSize(16) alignment:YYTextVerticalAlignmentCenter];
        [attributedText appendAttributedString:arrowAttachment];
    }

    self.pointsDescLabel.attributedText = attributedText;
}

/// 处理积分选择状态变化
- (void)handlePointsSelectionChanged {
    if ([self getPointsShowType] != PointsShowTypeEnable) {
        return;
    }

    // 切换选中状态
    self.isPointsSelected = !self.isPointsSelected;
    self.usePoints = [self getCurrentUsePoints];
    
    [self updatePointsDisplay];
    [self updatePublishButtonState];
}

/// 积分帮助按钮点击
- (void)pointsHelpButtonTapped {
    NSString *str = [NSString stringWithFormat:@"积分仅用于抵扣金币，%zd积分=1金币，可在“我的-福利中心”获得", self.pointsRatio];
    [FMProgressHUD showTextOnlyInView:nil withText:str];
}

/// 积分勾选框点击
- (void)pointsCheckboxButtonTapped {
    [self handlePointsSelectionChanged];
}

/// 更新发布按钮状态（考虑积分抵扣）
- (void)updatePublishButtonState {
    CGFloat price = self.askPrice.floatValue;
    if (self.selectedCoupon) {
        if (self.selectedCoupon.goodsType.integerValue == 4) { // 专用券
            price = 0;
        } else {
            price -= self.selectedCoupon.value;
        }
    }
    if (self.isPointsSelected) {
        price -= [self calculateDeductibleCoins]; // 减去积分抵扣
    }
    price = MAX(price, 0);
    
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    NSString *firstRechargeText = [FMUserDefault getSeting:AppInit_FirstRecharge_Text];
    
    if (userModel.coin >= price) {
        [self.publishBtn setTitle:[NSString stringWithFormat:@"发送(%.0f金币)", price] forState:UIControlStateNormal];
        self.firstRechargeLabel.hidden = YES;
        self.publishBtn.backgroundColor = FMNavColor;
        self.publishBtn.userInteractionEnabled = YES;
    } else {
        [self.publishBtn setTitle:@"余额不足，请先充值" forState:UIControlStateNormal];
        self.publishBtn.backgroundColor = FMNavColor;
        self.publishBtn.userInteractionEnabled = YES;
        if (userModel.isShowFirstDesc && firstRechargeText.length) {
            self.firstRechargeLabel.hidden = NO;
            self.firstRechargeLabel.text = firstRechargeText;
            // 此处不做延时的话layoutSubViews里无法获取firstRechargeLabel正确的frame
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self setNeedsLayout];
                [self layoutIfNeeded];
            });
        } else {
            self.firstRechargeLabel.hidden = YES;
        }
    }
}



@end
