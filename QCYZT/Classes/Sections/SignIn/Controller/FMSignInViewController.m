//
//  FMSignInViewController.m
//  QCYZT
//
//  Created by macPro on 2018/1/9.
//  Copyright © 2018年 sdcf. All rights reserved.
//

#import "FMSignInViewController.h"
#import "FMSignInView.h"
#import "FMSignInRewardView.h"
#import "FMSignInCalendarView.h"
#import "FMSignInDetailModel.h"
#import "FMSignInReward.h"
#import "FMSignInRewardPopView.h"
#import "HttpRequestTool+SignIn.h"
#import "FMTaskConfigModel.h"

@interface FMSignInViewController () <UIScrollViewDelegate>

@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *topView;
@property (nonatomic, strong) UIView *middleView;
@property (nonatomic, strong) NSMutableArray *rewardViews;
@property (nonatomic, strong) FMSignInCalendarView *calanderView;
@property (nonatomic, strong) FMSignInRewardPopView *popView;

@property (nonatomic, strong) UIButton *signBtn;
@property (nonatomic, strong) UILabel *daysLabel;
@property (nonatomic, strong) FMSignInDetailModel *detailModel;

@property (nonatomic, strong) NSDictionary *dict;

@property (nonatomic, assign) BOOL needsRefresh;

@end

@implementation FMSignInViewController{
    UIWindow *window;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.title = @"签到";
    self.view.backgroundColor = FMWhiteColor;
    
    [self.view addSubview:self.scrollView];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    UIView *containerView = [UIView new];
    [self.scrollView addSubview:containerView];
    [containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView);
        make.width.equalTo(self.scrollView);
    }];
    
    [containerView addSubview:self.topView];
    [self.topView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(@(0));
    }];
    
    [containerView addSubview:self.middleView];
    [self.middleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@(0));
        make.top.equalTo(self.topView.mas_bottom);
    }];
    
    [containerView addSubview:self.calanderView];
    [self.calanderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.middleView.mas_bottom).offset(10);
        make.left.right.equalTo(@(0));
        make.bottom.equalTo(containerView.mas_bottom);
    }];
    
    self.scrollView.hidden = YES;
    
    [self getDetail];
    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(handleLoginStatusNotification) monitorAuthLogin:NO];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if (self.needsRefresh) {
        self.needsRefresh = NO;
        [self getDetail];
    }
}

#pragma mark - NSNotification
- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (self.popView.superview) {
        [self.popView removeFromSuperview];
    }
}

#pragma mark - HTTP
- (void)getDetail {
    WEAKSELF;
    [HttpRequestTool signDetailWithStart:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            if (__weakSelf.scrollView.hidden == YES) {
                __weakSelf.scrollView.hidden = NO;
            }
            
            FMSignInDetailModel *detail = [FMSignInDetailModel modelWithDictionary:dic[@"data"]];
            if (detail.rule.length) {
                UIBarButtonItem *item = [[UIBarButtonItem alloc] initWithTitle:@"签到攻略" style:UIBarButtonItemStylePlain target:self action:@selector(enterToRule)];
                __weakSelf.navigationItem.rightBarButtonItem = item;
            } else {
                __weakSelf.navigationItem.rightBarButtonItem = nil;
            }
            
            __weakSelf.detailModel = detail;
        } else {
            [SVProgressHUD showInfoWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)signIn:(UIButton *)sender {
    if (![FMUserDefault getUserId].length) {
        [ProtocolJump jumpWithUrl:Login_URL];
        return;
    }
    
    WEAKSELF;
    [HttpRequestTool signInWithstart:^{
        sender.userInteractionEnabled = NO;
        [SVProgressHUD show];
    } failure:^{
        sender.userInteractionEnabled = YES;
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        __weakSelf.dict = dic;
        
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self showAImage];
            });
            [__weakSelf getDetail];
            
            FMTaskConfigModel *userTaskProgress = [FMUserDefault getUnArchiverDataForKey:UserTaskProgressCacheKey];
            FMSubTask *task = userTaskProgress.taskDic[@(FMTaskTypeFirstSign)];
            if (task.isEnable && task.completeNum < task.taskNum) { // 任务未完成
                task.completeNum = task.taskNum;
                [FMUserDefault setArchiverData:userTaskProgress forKey:UserTaskProgressCacheKey];
                [PushMessageView showWithTitle:@"任务完成" message:@"首次签到达成！新手任务奖励已发放" noticeImage:nil sureTitle:@"更多任务" cancelTitle:@"确定" clickSure:^{
                    [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                        [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://web?url=%@%@&title=%@", prefix.URLEncodedString, kAPI_UserCenter_FLZX.URLEncodedString, @"福利中心".URLEncodedString]];
                    }];
                } clickCancel:^{
                }];
            }
        } else {
            if ([dic[@"errcode"] isEqualToString:@"302"]) {
                [SVProgressHUD dismiss];
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self showAImage];
                });
                [__weakSelf getDetail];
            } else {
                [SVProgressHUD showInfoWithStatus:dic[@"errmessage"]];
            }
        }
        sender.userInteractionEnabled = YES;
    }];
}

#pragma mark - Private
- (void)enterToRule {
    [ProtocolJump jumpWithUrl:self.detailModel.rule];
}

- (void)showAImage {
    window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    window.windowLevel = UIWindowLevelStatusBar + 1;
    window.hidden = NO;
    window.rootViewController = [UIViewController new];
    UIView *bgView = [[UIView alloc] initWithFrame:window.bounds];
    bgView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.7];
    
    UIImageView *adImagV = [[UIImageView alloc] init];
    adImagV.center = window.center;
    adImagV.bounds = CGRectMake(0, 0, UI_SCREEN_WIDTH-30, UI_SCREEN_WIDTH-30);
    adImagV.userInteractionEnabled = YES;
    adImagV.contentMode = UIViewContentModeScaleAspectFit;
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(imageTap:)];
    [adImagV addGestureRecognizer:tap];
    __weak UIWindow *weakWindow = window;
    if ([_dict[@"data"] isKindOfClass:[NSDictionary class]] && [_dict[@"data"][@"alertImage"] isKindOfClass:[NSString class]]) {
        [adImagV sd_setImageWithURL:[NSURL URLWithString:_dict[@"data"][@"alertImage"]] completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
            if (image) {
                [weakWindow addSubview:bgView];
                [bgView addSubview:adImagV];
            }
        }];
    }
}

- (void)imageTap:(UITapGestureRecognizer *)gesture {
    if ([_dict[@"data"] isKindOfClass:[NSDictionary class]] && [_dict[@"data"][@"action"] length]) {
        [ProtocolJump jumpWithUrl:_dict[@"data"][@"action"]];
    }
//    UIView *view = gesture.view.superview;
//    [view removeFromSuperview];
    window.hidden = YES;
    window = nil;
}

- (void)scrollViewTap:(UITapGestureRecognizer *)gesture {
    if (self.popView.superview) {
        [self.popView removeFromSuperview];
    }
}

- (void)rewardViewClick:(UITapGestureRecognizer *)gesture {
    FMSignInRewardView *rewardView = (FMSignInRewardView *)gesture.view;
    if (self.popView.referView == rewardView) {
        self.popView.referView = nil;
        [self.popView removeFromSuperview];
        return;
    }
    
    self.popView.gifts = rewardView.reward.gifts;
    [self.scrollView addSubview:self.popView];
    self.popView.referRect = [rewardView convertRect:rewardView.bounds toView:self.scrollView];
    self.popView.referView = rewardView;
}

#pragma mark - Getter/Setter
- (void)setDetailModel:(FMSignInDetailModel *)detailModel {
    _detailModel = detailModel;
    
    // 上面按钮和文字
    self.signBtn.enabled = !detailModel.isSignIn;
    NSString *reminderStr = [detailModel.remindText length] ? detailModel.remindText : @"";
    self.daysLabel.attributedText = [FMHelper attrStrWithNormalStr:reminderStr pattern:@"[0-9]+" textColor:ColorWithHex(0xfef55a) textFont:FontWithSize(UI_Relative_WidthValue(16))];

    // 中间奖励提示
    for (FMSignInRewardView *giftView in self.rewardViews) {
        [giftView removeFromSuperview];
    }
    [self.rewardViews removeAllObjects];
    for (NSInteger i = 0; i < detailModel.rewards.count; i++) {
        FMSignInReward *rewardModel = [detailModel.rewards objectAtIndex:i];
        FMSignInRewardView *rewardView = [[FMSignInRewardView alloc] init];
        rewardView.reward = rewardModel;
        [_middleView addSubview:rewardView];
        [rewardView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.middleView).offset(22 * UI_RELATIVE_WIDTH);
            make.left.equalTo(@(i * UI_SCREEN_WIDTH / detailModel.rewards.count));
            make.width.equalTo(@(UI_SCREEN_WIDTH / detailModel.rewards.count));
            make.height.equalTo(@(130 * UI_RELATIVE_WIDTH));
            make.bottom.equalTo(self.middleView);
        }];
        [rewardView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(rewardViewClick:)]];
        if (i == 0) {
            rewardView.leftSepLine.hidden = YES;
        }
        if (i == detailModel.rewards.count - 1) {
            rewardView.rightSepLine.hidden = YES;
        }
        
        [self.rewardViews addObject:rewardView];
    }
    
    // 底部签到日历
    NSArray *days = [detailModel.signInDates componentsSeparatedByString:@","];
    NSMutableArray *arr = [NSMutableArray array];
    for (NSString *str in days) {
        if (str.length >= 2) {
            NSString *day = [str substringWithRange:NSMakeRange(str.length - 2, 2)];
            [arr addObject:day];
        }
    }
    self.calanderView.days = [arr copy];
}

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] init];
        _scrollView.backgroundColor = FMBgGreyColor;
        _scrollView.showsHorizontalScrollIndicator = NO;
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.bounces = NO;
        _scrollView.delegate = self;
        
        [_scrollView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(scrollViewTap:)]];
    }
    
    return _scrollView;
}

- (UIView *)topView {
    if (!_topView) {
        _topView = [UIView new];
        _topView.backgroundColor = FMWhiteColor;
        
        UIImageView *topImage = [UIImageView new];
        UIImage *image = [UIImage imageNamed:@"sign_qiandaobg_02"];
        topImage.image = image;
        
        [_topView addSubview:topImage];
        [topImage mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.equalTo(@(0));
            make.height.equalTo(@(210 * UI_RELATIVE_WIDTH));
            make.bottom.equalTo(topImage.superview);
        }];
        
        UIButton *signBtn = [[UIButton alloc] init];
        [signBtn setImage:[UIImage imageNamed:@"sign_dianjiqiandao"] forState:UIControlStateNormal];
        [signBtn setImage:[UIImage imageNamed:@"sign_yiqiandao"] forState:UIControlStateDisabled];
        
        [_topView addSubview:signBtn];
        [signBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(@(0));
            make.top.equalTo(@((70 - 49) * UI_RELATIVE_WIDTH));
            make.width.height.equalTo(@(98 * UI_RELATIVE_WIDTH));
        }];
        [signBtn addTarget:self action:@selector(signIn:) forControlEvents:UIControlEventTouchUpInside];
        self.signBtn = signBtn;
        
        UILabel *daysLabel = [[UILabel alloc] init];
        daysLabel.textColor = FMWhiteColor;
        daysLabel.font = [UIFont systemFontOfSize:16.0 * UI_RELATIVE_WIDTH];
        daysLabel.numberOfLines = 0;
        daysLabel.textAlignment = NSTextAlignmentCenter;
        [topImage addSubview:daysLabel];
        [daysLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(@(0));
            make.top.equalTo(@(145 * UI_RELATIVE_WIDTH));
        }];
        self.daysLabel = daysLabel;
    }
    
    return _topView;
}

- (UIView *)middleView {
    if (!_middleView) {
        _middleView = [UIView new];
        _middleView.backgroundColor = FMWhiteColor;
        
        UILabel *middleLable = [[UILabel alloc] init];
        middleLable.textColor = FMZeroColor;
        middleLable.font = [UIFont systemFontOfSize:15.0 * UI_RELATIVE_WIDTH];
        middleLable.textAlignment = NSTextAlignmentCenter;
        [_middleView addSubview:middleLable];
        [middleLable mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(@(0));
            make.top.equalTo(@(0));
        }];
        NSMutableAttributedString *attrString = [[NSMutableAttributedString alloc] initWithString:@"每月累计签到送礼包"];
        [attrString addAttribute:NSForegroundColorAttributeName value:FMNavColor range:NSMakeRange(6, 3)];
        middleLable.attributedText = attrString;
    }
    
    return _middleView;
}

- (NSMutableArray *)rewardViews {
    if (!_rewardViews) {
        _rewardViews = [NSMutableArray array];
    }
    
    return _rewardViews;
}

- (FMSignInCalendarView *)calanderView {
    if (!_calanderView) {
        _calanderView = [FMSignInCalendarView new];
        _calanderView.backgroundColor = FMWhiteColor;
    }
    
    return _calanderView;
}

- (FMSignInRewardPopView *)popView {
    if (!_popView) {
        _popView = [FMSignInRewardPopView new];
        _popView.backgroundColor = FMWhiteColor;
    }
    
    return _popView;
}
     

@end
