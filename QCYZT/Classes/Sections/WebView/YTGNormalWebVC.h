//
//  YTGNormalWebVC.h
//  FQYFNative
//
//  Created by macPro on 2019/10/29.
//  Copyright © 2019 macPro. All rights reserved.
//  普通的webVC

#import "CDVViewController.h"

@interface YTGNormalWebVC : CDVViewController <UIScrollViewDelegate>

@property (nonatomic, copy) NSString *titleStr;

@property (nonatomic, assign) BOOL banScreenshot;

// 会员中心访问埋点功能ID（可选，用于会员中心报告详情页）
@property (nonatomic, assign) NSInteger memberCenterFunctionId;

/**
 设置右上角按钮

 @param routerBlock 点击的跳转事件
 @param type 按钮类型 0.分享 1.文字 2.下载图片
 @param content type=1|2时传入 type=1传入文字内容，type=2传入图片链接
 */
- (void)configRightButtonWithRouter:(void(^)(void))routerBlock type:(NSInteger)type content:(NSString *)content;

/**
 隐藏右上角按钮
 */
- (void)hiddenRightButton;

/**
 显示左上角关闭按钮

 @param show 是否显示
 */
- (void)navCloseBtnShow:(BOOL)show;

// 拦截非通用协议
- (BOOL)dealPolicyForNavigationAction:(WKNavigationAction *)navigationAction  decisionHandler: (void (^)(WKNavigationActionPolicy))decisionHandler;

// 重新加载页面
- (void)reloadRequest;


@end

