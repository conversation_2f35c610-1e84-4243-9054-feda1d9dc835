//
//  YTGNormalWebVC.m
//  FQYFNative
//
//  Created by macPro on 2019/10/29.
//  Copyright © 2019 macPro. All rights reserved.
//

#import "YTGNormalWebVC.h"
#import "ZLWebViewProgress.h"
#import "NSURLProtocol+WebKitSupport.h"
#import "CDVURLProtocol.h"
#import "UIWindow+Category.h"
#import <AVKit/AVKit.h>
#import "FMPlayerManager.h"
#import "MAScreenShieldView.h"
#import "MemberCenterVisitManager.h"

@interface YTGNormalWebVC () <UIScrollViewDelegate>

@property (nonatomic, strong) UIButton *backBtn;
@property (nonatomic, strong) UIButton *closeBtn;

@property (nonatomic, copy) void(^rightBtnRouter)(void);

@property (nonatomic, strong) ZLWebViewProgress *progressView;

@property (nonatomic,strong) NSArray *protocolArr;
@property (nonatomic, strong) AVPlayerViewController *avPlayerController;

@property (nonatomic, assign) NSInteger currentButtonType;
@property (nonatomic, copy) NSString *currentButtonContent;

@end


@implementation YTGNormalWebVC

- (void)loadView {
    if (@available(iOS 13.2, *)) {
        MAScreenShieldView *view = [MAScreenShieldView creactWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIScreen mainScreen].bounds.size.height)];
        self.view = view;
    }else{
        [super loadView];
    }
    
    self.view.userInteractionEnabled = YES;
    self.view.backgroundColor = UIColor.whiteColor;
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    
    // 在布局完成后再次确保 webView 的 autoresizingMask 和 frame
    self.webView.autoresizingMask = UIViewAutoresizingNone;
    self.webView.frame = self.view.bounds;
}

- (void)dealloc {
    FMLog(@"++++++%@ dealloc+++++", NSStringFromClass([self class]));
    [[NSNotificationCenter defaultCenter] removeObserver:self];

    [[NSNotificationCenter defaultCenter] postNotificationName:kNormalWebVCDealloc object:nil];

    // 会员中心访问埋点 - 退出页面
    if (self.memberCenterFunctionId > 0) {
        [[MemberCenterVisitManager sharedManager] trackPageExit:self functionId:(MemberCenterFunctionType)self.memberCenterFunctionId];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    if ([self.startPage containsString:@"active/rwdb/rwdbActive"]) {
        [HttpRequestTool getPersonalInfoStart:^{
        } failure:^{
        } success:^(NSDictionary *dic) {
            if([dic[@"status"] isEqualToString:@"1"]) {
                NSDictionary *personalInfo = dic[@"data"];
                FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
                userModel.coin = [personalInfo[@"coin"] integerValue];
                [MyKeyChainManager save:kUserModel data:userModel];
                [[NSNotificationCenter defaultCenter] postNotificationName:kUserCoinUpdate object:nil];
            }
        }];
    }
    
    
}

- (void)viewDidLoad {
    [super viewDidLoad];

    self.view.backgroundColor = UIColor.whiteColor;
    self.navigationItem.title = self.titleStr.length ? self.titleStr : @"";

    [self.view addSubview:self.progressView];
    [self.progressView beginSimulateAnimation];

    [self configLeftBarButtonItems];

    // 会员中心访问埋点 - 进入页面
    if (self.memberCenterFunctionId > 0) {
        [[MemberCenterVisitManager sharedManager] trackPageEnter:self functionId:(MemberCenterFunctionType)self.memberCenterFunctionId];
    }
    
    // 设置代理，禁止WebView自动滚动
    WKWebView *webView = (WKWebView *)self.webView;
    webView.scrollView.delegate = self;
    webView.scrollView.bounces = NO;
    // 解决在x上底部黑边问题
    webView.backgroundColor = UIColor.whiteColor;
    webView.opaque = NO;
    if (@available(iOS 16.4, *)) {
        webView.inspectable = true;
    }
    // 设置ua
    [webView evaluateJavaScript:@"navigator.userAgent" completionHandler:^(id _Nullable oldAgent, NSError * _Nullable error) {
        if (![oldAgent isKindOfClass:[NSString class]]) {
            // 为了避免没有获取到oldAgent，所以设置一个默认的userAgent
            // Mozilla/5.0 (iPhone; CPU iPhone OS 12_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148
            oldAgent = [NSString stringWithFormat:@"Mozilla/5.0 (%@; CPU iPhone OS %@ like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148", [[UIDevice currentDevice] model], [[[UIDevice currentDevice] systemVersion] stringByReplacingOccurrencesOfString:@"." withString:@"_"]];
        }
        
        NSString *version = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"];
        NSString *newUserAgent = [NSString stringWithFormat:@"%@ com.djc.qcyzt/%@", oldAgent, version];
        webView.customUserAgent = newUserAgent;
    }];
    


    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pageDidLoad) name:CDVPageDidLoadNotification object:nil];
    
    // 视频隐藏的通知，处理iOS12上状态栏消失问题
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(windowDidBecomeHidden:) name:UIWindowDidBecomeHiddenNotification object:nil];
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(windowDidBecomeVisible:) name:UIWindowDidBecomeVisibleNotification object:nil];
    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(refreshPageData) monitorAuthLogin:NO];
    
    // 添加主题变化监听
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(themeDidChange) name:kUPThemeDidChangeNotification object:nil];

    WEAKSELF
    [FMPlayerManager shareManager].player.playerPlayStateChanged = ^(id<ZFPlayerMediaPlayback>  _Nonnull asset, ZFPlayerPlaybackState playState) {
        if (__weakSelf.avPlayerController) {
            if (playState == ZFPlayerPlayStatePlaying) {
                [__weakSelf.avPlayerController.player pause];
            } else {
                [__weakSelf.avPlayerController.player play];
            }
        }
    };

}

- (void)refreshPageData {
    WKWebView *webView = (WKWebView *)self.webView;
    [webView reload];
}

- (void)configLeftBarButtonItems {
    self.backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.backBtn setImage:FMImgInBundle(@"导航/亮黑暗灰返回") forState:UIControlStateNormal];
    [self.backBtn addTarget:self action:@selector(back) forControlEvents:UIControlEventTouchUpInside];
    self.backBtn.frame = CGRectMake(0, 0, 28, 28);
    UIBarButtonItem *backItem = [[UIBarButtonItem alloc] initWithCustomView:self.backBtn];
    
    self.closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.closeBtn setImage:FMImgInBundle(@"导航/亮黑暗灰关闭") forState:UIControlStateNormal];
    [self.closeBtn addTarget:self action:@selector(close) forControlEvents:UIControlEventTouchUpInside];
    self.closeBtn.frame = CGRectMake(0, 0, 28, 28);
    UIBarButtonItem *closeItem = [[UIBarButtonItem alloc] initWithCustomView:self.closeBtn];
    self.closeBtn.hidden = YES;
    
    self.navigationItem.leftBarButtonItems = @[backItem, closeItem];
}

- (NSURL *)errorURL {
    if ([self needInjectCordova]) {
        if ([self.webViewEngine.URL.absoluteString containsString:@"djc888"]) {
            NSString *path = [[NSBundle mainBundle] pathForResource:@"notfind" ofType:@"html"];
            return [NSURL fileURLWithPath:path];
        }
    }
    
    return nil;
}


// 重新加载页面
- (void)reloadRequest {
    [self.webViewEngine loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:self.startPage]]];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self configNavWhiteColorWithCloseSEL:nil];
}

#pragma mark - NSNotification
- (void)pageDidLoad {
    [self.progressView beginCompleteAnimation];
    
    if ([self needInjectCordova]) {
        NSString *cordovaConfigStr = @"function evaluateCordova() {\
                                           if(document.scripts.yztMyCordova == null) { \
                                               var script = document.createElement('script'); \
                                               script.src='https://file.djc8888.com/appFiles/cordova_ios/cordova.js'; \
                                               script.type='text/javascript'; \
                                               script.id='yztMyCordova'; \
                                               document.head.appendChild(script); \
                                               return '1';\
                                           } else {return '2';}\
                                        }\
                                        evaluateCordova()";
        [self.webViewEngine evaluateJavaScript:cordovaConfigStr completionHandler:^(NSString *result, NSError *error) {
        }];
        
        // 注入监听标题变化的js
        NSString *changeTitleStr = @"var titleEl = document.getElementsByTagName('title')[0]; \
        titleEl.addEventListener('DOMSubtreeModified', function(evt) { \
        navigator.general.changeTitle({title:titleEl.innerText}); \
        }, false);";
        [self.webViewEngine evaluateJavaScript:changeTitleStr completionHandler:^(NSString *result, NSError *error) {
        }];
        
    }
    
    // 获取title
    if (!self.titleStr.length) {
        WEAKSELF
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [__weakSelf.webViewEngine evaluateJavaScript:@"document.title" completionHandler:^(NSString *result, NSError *error) {
                NSString *title = [result stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]].length ? result : @"";
                if (title.length) {
                    __weakSelf.title = title;
                }
            }];
        });
    }
    
}

- (void)windowDidBecomeHidden:(NSNotification *)noti {
    UIWindow * win = (UIWindow *)noti.object;
    if(win){
        UIViewController *rootVC = win.rootViewController;
        NSArray<__kindof UIViewController *> *vcs = rootVC.childViewControllers;
        if([vcs.firstObject isKindOfClass:NSClassFromString(@"AVPlayerViewController")]){
            self.avPlayerController = nil;
            [FMPlayerManager play];
            [[UIApplication sharedApplication] setStatusBarHidden:NO];
        }
    }
    
    if (@available(iOS 16.0, *)) {
        UIWindow *window = [UIApplication sharedApplication].windows.firstObject;
        if ([window isKindOfClass:NSClassFromString(@"PGHostedWindow")]) {
            [window remove];
        }
    }
}

- (void)windowDidBecomeVisible:(NSNotification *)noti {
    for (NSInteger i = 0; i < [UIApplication sharedApplication].windows.count ; i ++) {
        UIWindow *window = [UIApplication sharedApplication].windows[i];
        UIViewController *rootVC = window.rootViewController;
        for (NSInteger j = 0; j < rootVC.childViewControllers.count; j++) {
            UIViewController *avVC = rootVC.childViewControllers[j];
            if ([avVC isKindOfClass:[AVPlayerViewController class]]) {
                self.avPlayerController = (AVPlayerViewController *)avVC;
                [FMPlayerManager pause];
                break;
            }
        }
    }
}

#pragma mark - Public
- (void)configRightButtonWithRouter:(void(^)(void))routerBlock type:(NSInteger)type content:(NSString *)content {
    self.rightBtnRouter = routerBlock;
    
    // 保存当前按钮类型和内容，用于主题切换时恢复
    self.currentButtonType = type;
    self.currentButtonContent = content;
    
    if (type == 0) {
        self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithImage:FMImgInBundle(@"导航/亮黑暗灰分享") style:UIBarButtonItemStylePlain target:self action:@selector(rightBtnClick)];
    } else if (type == 1) {
        self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithTitle:content style:UIBarButtonItemStylePlain target:self action:@selector(rightBtnClick)];
    } else if (type == 2) {
        [[SDWebImageDownloader sharedDownloader] downloadImageWithURL:[NSURL URLWithString:content] options:SDWebImageDownloaderLowPriority progress:nil completed:^(UIImage * _Nullable image, NSData * _Nullable data, NSError * _Nullable error, BOOL finished) {
            if (image) {
                UIImage *resizedImage = [image cropImageToSize:CGSizeMake(22.0 * image.size.width / image.size.height, 22.0)];
                
                // 在暗黑模式下，对图片进行反色处理
                if ([UPThemeManager isDarkTheme]) {
                    resizedImage = [UIImage imageWithTintColor:ColorWithHex(0xD8D8D8) blendMode:kCGBlendModeDestinationIn WithImageObject:resizedImage];
                } else {
                    resizedImage = [UIImage imageWithTintColor:FMZeroColor blendMode:kCGBlendModeDestinationIn WithImageObject:resizedImage];
                }
                
                // 设置渲染模式为原始图片，避免系统自动着色
                resizedImage = [resizedImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
                
                dispatch_async(dispatch_get_main_queue(), ^{
                    self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithImage:resizedImage style:UIBarButtonItemStylePlain target:self action:@selector(rightBtnClick)];
                });
            }
        }];
    }
}

- (void)hiddenRightButton {
    self.navigationItem.rightBarButtonItem = nil;
    self.rightBtnRouter = nil;
}

- (void)navCloseBtnShow:(BOOL)show {
    self.closeBtn.hidden = !show;
}

#pragma mark - Private
- (BOOL)needInjectCordova {
    BOOL needInjectCordova = NO;
    NSArray *filter = [JsonTool dicOrArrFromJsonString:[JsonTool jsonStrWithJsonName:@"injectCordovaWhiteList"]];
    for (NSString *url in filter) {
        if ([self.webViewEngine.URL.absoluteString containsString:url]) {
            needInjectCordova = YES;
            break;
        }
    }
    
    return needInjectCordova;
}

- (void)rightBtnClick {
    if (self.rightBtnRouter) {
        self.rightBtnRouter();
    }
}

- (void)back {
    WKWebView *webView = (WKWebView *)self.webView;
    if ([webView canGoBack]) {
        [webView goBack];
    } else {
        [self.navigationController popViewControllerAnimated:YES];
    }
}

- (void)close {
    [self.navigationController popViewControllerAnimated:YES];
}

- (BOOL)dealPolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler: (void (^)(WKNavigationActionPolicy))decisionHandler {
    NSURLRequest *request = navigationAction.request;
    NSString *urlString = request.URL.absoluteString;
    
    for (int i=0; i<self.protocolArr.count; i++) {
        if ([urlString containsString:self.protocolArr[i]]) {
            switch (i) {
                case 0:
                case 2:{
                    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:urlString] options:@{} completionHandler:nil];
                    decisionHandler(WKNavigationActionPolicyCancel);
                }
                    break;
                    
                case 1: {
                    if ([urlString containsString:@"//"]) {
                        NSURL *url = [NSURL URLWithString:urlString];
                        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
                    } else {
                        NSString *number = [urlString substringFromIndex:[urlString rangeOfString:@":"].location + 1];
                        NSURL *url = [NSURL URLWithString:[NSString stringWithFormat:@"tel://%@", number]];
                        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
                    }
                    decisionHandler(WKNavigationActionPolicyCancel);
                }
                    break;
                    
                default:
                    break;
            }
            
            return YES;
        }
    }
    
    return NO;
}

#pragma mark - Notification
// 主题变化监听处理
- (void)themeDidChange {
    // 如果有右边按钮并且是图片类型的，重新加载
    if (self.rightBtnRouter && self.navigationItem.rightBarButtonItem) {
        [self configRightButtonWithRouter:self.rightBtnRouter
                                     type:self.currentButtonType
                                  content:self.currentButtonContent];
    }
}

#pragma mark - 父类方法重写
#pragma mark -- 屏幕旋转相关
-(BOOL)shouldAutorotate {
   return YES;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
    return UIInterfaceOrientationPortrait;
}

#pragma mark - getter
- (ZLWebViewProgress *)progressView {
    if (!_progressView) {
        CGRect barFrame = CGRectMake(0, 0, UI_SCREEN_WIDTH, 3.0);
        _progressView = [[ZLWebViewProgress alloc] initWithFrame:barFrame];
        _progressView.useGradientColor = YES;
        _progressView.progressColor = ColorWithHex(0xFFB100);
        [_progressView beginSimulateAnimation];
    }
    return _progressView;
}

- (NSArray *)protocolArr {
    if (!_protocolArr) {
        _protocolArr = @[@"itms-appss://", @"tel:", @"weixin://"];
    }
    return _protocolArr;
}


- (void)setBanScreenshot:(BOOL)banScreenshot {
    _banScreenshot = banScreenshot;
    
    MAScreenShieldView *view = (MAScreenShieldView *)self.view;
    if ([view isKindOfClass:[MAScreenShieldView class]]) {
        view.banScreenshot = banScreenshot;
    }
}

@end



