//
//  FMDakaLiveDetailVC.m
//  QCYZT
//
//  Created by shumi on 2021/11/15.
//  Copyright © 2021 LZKJ. All rights reserved.
//


#import "FMDakaLiveDetailVC.h"
#import "FMDakaLiveDetailHeaderView.h"
#import "FMDakaLiveIntroduceTabCell.h"
#import "FMDakaLiveAlertVC.h"

#import "ZFAVPlayerManager.h"
#import "FMCustomDakaLivePlayerControlView.h"
#import "HttpRequestTool+Live.h"

#import "FMLiveProfileVC.h"
#import "EnablePayModel.h"

#import "PaymentView.h"
#import "FMDakaLiveDakaProfileVC.h"
#import "FMProgressHUD.h"
#import "IQKeyboardManager.h"
#import "FMLivePassWordAlertVC.h"
#import "LiveChatRoomTabView.h"
#import "LiveDetailBottomView.h"
#import "FMLiveErroInfoView.h"
#import "NSObject+FBKVOController.h"
#import "FMShareHelper.h"
#import "FMShareModel.h"
#import "FMTaskConfigModel.h"
#import "HttpRequestTool+DailyTask.h"
#import "FMTaskConfigModel.h"
#import "FMPayCoinOrBuyVipView.h"
#import "FMPayHandle.h"
#import "FMGlobalVideoPlayView.h"
#import "FMPlayerManager.h"
#import "FMLiveStockModel.h"
#import "FMLiveTopMsgDetailView.h"
#import "LiveMessageFrameModel.h"
#import "LiveGiftContainerView.h"
#import "LiveGiftMsgInfo.h"
#import "LiveGiftBoardView.h"
#import "LiveGiftModel.h"
#import "FMLiveQuickMsgView.h"
#import "CountDown.h"
#import "ZFLandscapeWindow.h"
#import "FMLiveCommentView.h"
#import "StatisticsManager.h"

@implementation FMDakaLiveDetailNeedPwdModel

-(FMDaKaLiveType)liveType{
    return self.type.integerValue;
}

-(FMLiveStatus)liveStatus{
    return self.status.integerValue;
}

@end

@interface FMDakaLiveDetailVC ()<DakaLivePlayerControlViewDelegate,LivePlayerStatusViewDelegate,LiveDetailHeaderViewDelegate,UITableViewDelegate, UITableViewDataSource>
@property (nonatomic ,assign, readwrite) FMLiveStatus liveStatus;
@property (nonatomic ,copy) NSString *roomId;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIButton *closeBtn;
@property (nonatomic, strong) FMDakaLiveDetailHeaderView *headerView;
/// 置顶消息详情视图
@property (nonatomic, strong) FMLiveTopMsgDetailView *topMsgDetailView;
/// 预约
@property (nonatomic, strong) UIButton *appointmentBtn;
/// 播放器控制层
@property (nonatomic, strong) FMCustomDakaLivePlayerControlView *controlView;
/// 直播简介
@property (nonatomic, strong) FMLiveProfileVC *profileVC;
/// 权限提示弹窗
@property (nonatomic, strong) FMDakaLiveAlertVC *alertVC;
/// 当前试看进度
@property (nonatomic, assign) NSInteger currentTryTime;
/// 记录收到停播通知的记录字段  回看状态也能播放视频,离开页面视频暂停和播放时 不能以回看直播状态作为的判断
@property (nonatomic, assign) BOOL isStopLive;
/// 是否需要密码
@property (nonatomic, assign) BOOL needPassWord;
/// 密码是否验证过
@property (nonatomic, assign) BOOL pwdChecked;
/// 密码弹窗
@property (nonatomic, strong) FMLivePassWordAlertVC *passwordAlertVC;
/// 记录文字直播观看时长计时器
@property (nonatomic, retain) dispatch_source_t timer;
/// 聊天室试图
@property (nonatomic, strong) LiveChatRoomTabView *chatRoomView;
/// 底部输入框视图
@property (nonatomic, strong) LiveDetailBottomView *bottomView;
/// 快捷消息
@property (nonatomic, strong) FMLiveQuickMsgView *quickMsg;
/// 占位页
@property (nonatomic, strong) FMLiveErroInfoView *errorInfo;
/// 分享按钮
@property (nonatomic, strong) UIButton *shareButton;
/// 每日任务页面浏览计时
@property (nonatomic, assign) NSInteger pageBrowseTime;
/// 是否需要增加计数标志，1表示需要，2表示当前不需要，0表示不再需要
@property (nonatomic, assign) FMNeedAddTimeType needAddTimeCountFlag;
@property(nonatomic,strong) FMPayCoinOrBuyVipView *coinOrVipView;
/// 获取权限方式 1 联系顾问  2 支付金币
@property (nonatomic,assign) FMPermissionsMethod permissionsWay;
/// 打赏动画View
@property (nonatomic, strong) LiveGiftContainerView *giftView;
/// 礼物选择页面
@property (nonatomic, strong) LiveGiftBoardView *giftChooseView;
/// 快捷消息倒计时
@property (nonatomic, strong) CountDown *quickCountDown;
/// 是否展示快捷消息
@property (nonatomic,assign) BOOL showQuickMsgView;
/// 评论输入框
@property (nonatomic, strong) FMLiveCommentView *commentView;
#pragma mark - 宣传视频播放相关属性
@property (nonatomic, strong) UIButton *playButton;
@end

@implementation FMDakaLiveDetailVC


- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.up_contentBgColor;
    
    /// 直播详情可以根据通知再次进入新的直播间, 此处让导航栈只有一个直播详情页面
    NSMutableArray *controllers = [NSMutableArray arrayWithArray:self.navigationController.viewControllers];
    for (UIViewController *vc in controllers) {
        if(![vc isEqual:self] && [NSStringFromClass([vc class]) isEqualToString:@"FMDakaLiveDetailVC"]) {
            [controllers removeObject:vc];
            break;
        }
    }
    self.navigationController.viewControllers = [NSArray arrayWithArray:controllers];
    [self configSubViews];
    [self configConstraints];
    [self addNotifications];
    if (self.passWord.length == 0) {
        // 查询是否为密码房
        [self reqIfSecretRoom];
    } else {
        // 调用密码校验接口
        [self passWordVerify];
    }
    
    // 添加功能使用统计 - 直播详情页统计
    [[StatisticsManager sharedManager] trackControllerCreated:self functionId:FunctionItemTypeLive];
}

- (void)viewWillAppear:(BOOL)animated {
    [self configNav];
    [super viewWillAppear:animated];
    
    if (!self.isStopLive) {
        [FMPlayerManager shareManager].player.viewControllerDisappear = NO;
    }
    /**
     进入直播间
     从小窗口播放恢复到详情播放时,如果是同一播放源,会提前关闭小窗口,不会释放播放资源
     其他情况则会关闭小窗口 并且释放播放源
     */
    if (![FMPlayerManager shareManager].liveModel) {
        [FMPlayerManager dismissGlobalVideoPlayViewWithDestroyPlayer];
    } else {
        if([FMPlayerManager shareManager].liveModel.liveId == [self.roomId integerValue]) {
            [FMPlayerManager dismissGlobalVideoPlayView];
        } else {
            [FMPlayerManager dismissGlobalVideoPlayViewWithDestroyPlayer];
        }
    }
 
    [IQKeyboardManager sharedManager].enableAutoToolbar = NO;
}


- (void)viewWillDisappear:(BOOL)animated {
    [self configNavOnDisappear:animated];
    [super viewWillDisappear:animated];
    if (![FMHelper getCurrentVC].presentingViewController) { //不是推出控制器
        /// 如果不是小窗口播放, 当前控制器即将消失时 暂停播放
        if (![FMPlayerManager shareManager].liveModel.openSmallViewPlay) {
            [FMPlayerManager shareManager].player.viewControllerDisappear = YES;
        }
    }
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
    [IQKeyboardManager sharedManager].enableAutoToolbar = YES;
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    if (self.liveStatus == FMLiveStatusNotStart) {
        self.tableView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT - (60 + UI_SAFEAREA_BOTTOM_HEIGHT + UI_SAFEAREA_TOP_HEIGHT));
    } else {
        self.tableView.frame = CGRectMake(0, UI_STATUS_HEIGHT, UI_SCREEN_WIDTH, self.headerView.height);
        self.chatRoomView.frame = CGRectMake(0, CGRectGetMaxY(self.tableView.frame), UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT - CGRectGetMaxY(self.tableView.frame) - UI_SAFEAREA_BOTTOM_HEIGHT - 65);
        self.giftView.frame = CGRectMake(self.giftView.x, self.chatRoomView.y + 30, self.giftView.width, self.giftView.height);
            self.view.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
    }
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
}

- (void)dealloc {
    FMLog(@"%s",__func__);
    [self.commentView removeFromSuperview];
    [self destoryTimer];
    // 打开了小窗口播放功能
    if ([FMPlayerManager shareManager].liveModel.openSmallViewPlay) {
        // 如果当前不是小窗口播放 则开启小窗口
        if(![FMPlayerManager shareManager].isSmallViewPlay) {
            if ([FMPlayerManager shareManager].player) {
                /// 当前直播可以小窗播放
                [FMPlayerManager showGlobalVideoPlayView];
            } else {
                [FMPlayerManager dismissGlobalVideoPlayViewWithDestroyPlayer];
            }
        }
    } else {
        // 未打开小窗口播放则 销毁播放源
        [FMPlayerManager dismissGlobalVideoPlayViewWithDestroyPlayer];
        [self taskTipMessage];
    }
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (BOOL)shouldAutorotate {
    return NO;
}

/// 支持哪些转屏方向
- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    if ([FMPlayerManager shareManager].player.isFullScreen) {
        return UIInterfaceOrientationMaskLandscape;
    }
    return UIInterfaceOrientationMaskPortrait;
}

/// 根据直播房间id初始化
- (id)initWithRoomId:(NSString *)roomId {
    self = [super init];
    if (self) {
        [self configLiveInitInfoWithRoomId:roomId passWord:nil];
    }
    return self;
}

/// 根据房间id和 密码初始化(针对需要密码的直播 小窗播放后回到直播详情的场景)
- (id)initWithRoomId:(NSString *)roomId passWord:(NSString *)password {
    self = [super init];
    if (self) {
        [self configLiveInitInfoWithRoomId:roomId passWord:password];
    }
    return self;
}

/// 配置直播室信息
- (void)configLiveInitInfoWithRoomId:(NSString *)roomId passWord:(NSString *)password {
    self.roomId = roomId;
    self.passWord = password;
    self.pwdChecked = false;
    self.needAddTimeCountFlag = FMNeedAddTimeTypeNeed;
    WEAKSELF
    [self.KVOController observe:[CountDownShareInstance shareInstance] keyPath:DifferentValue options:NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (!__weakSelf.detailModel) {
            return;
        }
        if (![change objectForKey:NSKeyValueChangeNewKey]) {
            return;
        }
        // 更新数据模型的时间
        __weakSelf.detailModel.currTime = [FMUserDefault getServerSystemTime];
        
        // 预约倒计时
        if(__weakSelf.detailModel.liveStatus == FMLiveStatusNotStart){
            [__weakSelf liveAppointmentCountDown];
        }
        
        // 文字试看
        if(__weakSelf.detailModel.liveType == FMDaKaLiveTypeWord){
            [__weakSelf tryPlayWordCountDown];
        }
        
        // 每日任务统计
        [__weakSelf dailyTaskStatistics];
    }];
}

/// 直播预约倒计时
-(void)liveAppointmentCountDown {
    // 预约
    if (self.detailModel.liveStatus != FMLiveStatusNotStart) {return;}
    if (self.detailModel.liveAppointmentCountDownEndFlag) {return;}
    // 倒计时结束
    if (self.detailModel.currTime >= (self.detailModel.startTime + 1000)) {
        self.detailModel.liveAppointmentCountDownEndFlag = true;
        
        // 需要密码的预约类型，到开播的时候需要验一次密码
        if (self.needPassWord && !self.pwdChecked) {
            // 需要密码 弹密码窗口
            [self showPassWordAlert:self.detailModel.liveType status:FMLiveStatusLiveing];
            return;
        }
        
        // 不需要密码请求直播间详情
        [self requestData];
    } else { // 更新cell倒计时
        if (!self.tableView.visibleCells) {return;}
        UITableViewCell *cell = [self.tableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:0]];
        if ([cell isKindOfClass:[FMDakaLiveIntroduceTabCell class]]) {
            FMDakaLiveIntroduceTabCell *introduceCell = (FMDakaLiveIntroduceTabCell *)cell;
            introduceCell.systemTime = [FMUserDefault getServerSystemTime];
        }
    }
}

// 每日任务统计
- (void)dailyTaskStatistics {
    // 直播没开始
    if (self.detailModel.liveStatus == FMLiveStatusNotStart) { return; }
    
    // 任务未开启 返回,  任务数达到 返回
    FMTaskConfigModel *taskConfig = [FMUserDefault getUnArchiverDataForKey:AppInit_Task];
    if (!taskConfig.taskDic[@(FMTaskTypeWatchLive)].isEnable) {return;}
    
    // 需要付费
    if (self.detailModel.ticketInfo.needPay != 0){return;}
    
    if ( self.needAddTimeCountFlag != FMNeedAddTimeTypeNeed ) { return; }
        
    self.pageBrowseTime ++;
    // 达到任务时长
    if (self.pageBrowseTime >= taskConfig.taskDic[@(FMTaskTypeWatchLive)].taskRequire) {
        self.needAddTimeCountFlag = FMNeedAddTimeTypeNoMore;
        [self postReqToReachTaskTimeRequire];
    }
}

/// 文字直播试看倒计时
-(void)tryPlayWordCountDown {
    BOOL hasPermission = self.detailModel.ticketInfo.needPay == 0 && self.detailModel.needVip == 0;
    if (hasPermission) {
        return;
    }
    if (self.detailModel.wordTryPlayAlertFlag) {
        return;
    }
    if (self.detailModel.tryEndTime < self.detailModel.currTime) {
        return;
    }
    //多加1000ms 给服务器一点时间去处理状态更新逻辑
    if (self.detailModel.currTime >= self.detailModel.tryEndTime - 1000) {
        // 试看结束 取消统计接口的定时统计
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t) (1 * NSEC_PER_SEC)), dispatch_get_main_queue(),^{
            [self destoryTimer];
            [self showPermissionsAlertVC];
            self.detailModel.wordTryPlayAlertFlag = true;
        });
    }
}


-(void)configSubViews{
    self.view.backgroundColor = UIColor.up_contentBgColor;
    
    [self.view addSubview:self.tableView];
    [self.view addSubview:self.chatRoomView];
    [self.chatRoomView superviewAddMessageRemindView];
    
    
    [self.view addSubview:self.giftView];
    
    [self.view addSubview:self.errorInfo];
    
    [self.view addSubview:self.bottomView];
    // 预约直播按钮
    [self.view addSubview:self.appointmentBtn];
    
    [self.view addSubview:self.closeBtn];
}

-(void)configConstraints{
    [self.errorInfo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(25 + UI_STATUS_HEIGHT));
        make.right.equalTo(@(-20));
        make.size.equalTo(@(CGSizeMake(22, 22)));
    }];
}

-(void)addNotifications{
    // 点击通知栏通知
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(notificationViewClick:) name:kNotificationViewClickNotification object:nil];
    // 投顾关注状态通知
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updateFocusStatus:) name:kLiveRoomfocusNumChanged object:nil];
    // 直播室 信息更新
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(liveInfoUpdate:) name:kNotificationLiveRoomInfoUpdate object:nil];
    // 活动信息更新
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updateActiveInfo:) name:kNotificationLiveRoomActiviteInfo object:nil];
    // 相关股票更新
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updateStockData:) name:kNotificationLiveRelatedStockUpdate object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appWillResignActive) name:UIApplicationWillResignActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appDidBecomeActive) name:UIApplicationDidBecomeActiveNotification object:nil];
    // 收到礼物通知
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(receiveGiftMsg:) name:kNotificationLiveSendGiftMessage object:nil];
}

-(void)configNav{
    [self configNavWhiteColorWithCloseSEL:@selector(closeBtnClick)];
    
    /// 默认 先隐藏 拿到直播间数据后在决定 显示还是隐藏
    if (self.detailModel) {
        if ([self.detailModel.status integerValue] ==     FMLiveStatusLiveing ||
            [self.detailModel.status integerValue] ==     FMLiveStatusEnd) {
            self.selfNavigationBarHidden = YES;
        } else {
            self.selfNavigationBarHidden = NO;
        }
    } else {
        self.selfNavigationBarHidden = YES;
    }
}

-(void)configNavOnDisappear:(BOOL)animated {
    [self configNavRedColor];

    self.selfNavigationBarHidden = NO;
}

//关闭直播间 每日任务 提示语
- (void)taskTipMessage {
    FMTaskConfigModel *taskConfig = [FMUserDefault getUnArchiverDataForKey:AppInit_Task];
    if (taskConfig.taskDic[@(FMTaskTypeWatchLive)].isEnable && [self.detailModel.status integerValue] != FMLiveStatusNotStart && self.detailModel.ticketInfo.needPay == 0) {
        FMTaskConfigModel *userTaskProgress = [FMUserDefault getUnArchiverDataForKey:UserTaskProgressCacheKey];
        FMSubTask *liveTaskModel = userTaskProgress.taskDic[@(FMTaskTypeWatchLive)];
        // 今日直播任务未完成 给出弱提示
        if (liveTaskModel.completeNum != (liveTaskModel.taskNum * liveTaskModel.taskNeedNum)) {
            if (taskConfig.taskDic[@(FMTaskTypeWatchLive)].isEnable) { //任务可用
                if (self.pageBrowseTime < taskConfig.taskDic[@(FMTaskTypeWatchLive)].taskRequire) { //观看时长未达到任务时长 给出弱提示
                    /**
                     获取直播间完成任务的缓存时间  有值则表示完成过任务
                     在完成任务当天内 则属于有效 不给提示
                     超过当天 则重置时间 给出提示
                     */
                    long long resetTime = [[FMUserDefault getUnArchiverDataForKey:[NSString stringWithFormat:@"%ld",self.detailModel.liveId]] longLongValue];
                    if (resetTime > 0) {
                        if ([FMUserDefault getServerSystemTime] > resetTime) {
                            //重置当前直播间任务完成状态
                            [FMUserDefault setArchiverData:[NSNumber numberWithLongLong:0] forKey:[NSString stringWithFormat:@"%ld",self.detailModel.liveId]];
                        }
                    } else {
                        if ([FMPlayerManager shareManager].player.isFullScreen) {
                            [SVProgressHUD setContainerView:self.controlView];
                        }
                        [SVProgressHUD showImage:ImageWithName(@"") status:[NSString stringWithFormat:@"再观看直播%ld分钟即可获得奖励！",(taskConfig.taskDic[@(FMTaskTypeWatchLive)].taskRequire / 60)]];
                    }
                }
            }
        }
    }
}

/*
 reqIfSecretRoom
 requestData
 setDetailModel
 setLiveStatus
 这个时候的detailModel是初始化好的
 */
//布局更新
- (void)setLiveStatus:(FMLiveStatus)liveStatus {
    _liveStatus = liveStatus;
    if (self.liveStatus == FMLiveStatusNotStart) {
        self.tableView.scrollEnabled = YES;
        self.navigationItem.title = @"直播预约";
        [self.navigationController setNavigationBarHidden:NO animated:NO];
        self.selfNavigationBarHidden = NO;
        self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:self.shareButton];
        [self.tableView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.edges.insets(UIEdgeInsetsMake(0, 0, 60 + UI_SAFEAREA_BOTTOM_HEIGHT, 0));
        }];
        [self.chatRoomView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.tableView.mas_bottom);
            make.left.right.equalTo(self.view);
            make.bottom.equalTo(@(-UI_SAFEAREA_BOTTOM_HEIGHT - 65));
        }];
        self.appointmentBtn.hidden = NO;
        [self.appointmentBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@(15));
            make.right.equalTo(@(-15));
            make.top.equalTo(self.tableView.mas_bottom).offset(7.5);
            make.height.equalTo(@(45));
        }];
        self.bottomView.hidden = YES;
        self.quickMsg.hidden = YES;
        self.tableView.contentInset = UIEdgeInsetsMake(self.tableView.contentInset.top, self.tableView.contentInset.left, 0, self.tableView.contentInset.right);;
    } else {
        [self.navigationController setNavigationBarHidden:YES animated:NO];
        self.tableView.scrollEnabled = NO;
        self.appointmentBtn.hidden = YES;
        [self.appointmentBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@(15));
            make.right.equalTo(@(-15));
            make.top.equalTo(self.tableView.mas_bottom).offset(7.5);
            make.height.equalTo(@(CGFLOAT_MIN));
        }];
        
        [self.tableView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(@(UI_STATUS_HEIGHT));
            make.left.right.equalTo(self.view);
            make.height.equalTo(@(self.headerView.frame.size.height));
            
        }];
        
        self.chatRoomView.detailModel = self.detailModel;
        [self.chatRoomView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.tableView.mas_bottom);
            make.left.right.equalTo(self.view);
            make.bottom.equalTo(@(-UI_SAFEAREA_BOTTOM_HEIGHT - 65));
        }];
        
        self.bottomView.hidden = NO;
        [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.left.right.equalTo(self.view);
            make.height.equalTo(@(65 + UI_SAFEAREA_BOTTOM_HEIGHT));
        }];
        
        [self.shareButton removeFromSuperview];
        [self.view addSubview:self.shareButton];
        
        [self.shareButton mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.closeBtn);
            make.right.equalTo(self.closeBtn.mas_left).offset(-10);
            make.size.equalTo(@(CGSizeMake(22, 22)));
        }];
        
        // 评论试图
        [self confgiCommonView];
        
        self.commentView.isShowQuickMsg = self.liveStatus == FMLiveStatusLiveing;
        if (self.liveStatus == FMLiveStatusLiveing) {
            /// 直播中才显示快捷话术
            self.commentView.isGreetingTime = YES;
            self.quickMsg.frame = CGRectMake(0, UI_SCREEN_HEIGHT - 50 - 65 - UI_SAFEAREA_BOTTOM_HEIGHT, UI_SCREEN_WIDTH, 50);
            self.quickMsg.hidden = NO;
            [self.view insertSubview:self.quickMsg belowSubview:self.bottomView];
        }
    }
    self.closeBtn.hidden = (self.liveStatus == FMLiveStatusNotStart) ? YES : NO;
}


//更新页面数据
- (void)setDetailModel:(FMLiveDetailModel *)detailModel {
    _detailModel = detailModel;
    if (_detailModel.liveId == [FMPlayerManager shareManager].liveModel.liveId) {
        _detailModel.openSmallViewPlay = [FMPlayerManager shareManager].liveModel.openSmallViewPlay;
    }
    self.roomId = [NSString stringWithFormat:@"%ld",detailModel.liveId];
    self.bottomView.model = detailModel;
    [self.tableView beginUpdates];
    self.headerView.model = detailModel;
    self.tableView.tableHeaderView = self.headerView;
    [self.tableView endUpdates];
    self.profileVC.model = detailModel;
    //更新布局
    self.liveStatus = [detailModel.status integerValue];
    
    
    
    if (self.liveStatusBlock) {
        self.liveStatusBlock(detailModel.status);
    }
    
    [self.appointmentBtn setTitle:detailModel.isSign.boolValue ? @"立即订阅" : @"立即预约" forState:UIControlStateNormal];
    [self.appointmentBtn setTitle:detailModel.isSign.boolValue ? @"取消订阅" : @"已预约" forState:UIControlStateSelected];
    self.appointmentBtn.selected = detailModel.isReservation == 1;
    if (detailModel.isReservation) {
        self.coinOrVipView.hidden = true;
        self.appointmentBtn.hidden = false;
        self.appointmentBtn.backgroundColor = ColorWithHex(0xdddddd);
    } else {
        self.appointmentBtn.backgroundColor = FMNavColor;
    }
    
    
    // 检查是否从预约状态切换到直播中状态
    static FMLiveStatus previousLiveStatus = -1;
    BOOL isStatusChangedFromNotStartToLiving = (previousLiveStatus == FMLiveStatusNotStart && self.liveStatus == FMLiveStatusLiveing);
    previousLiveStatus = self.liveStatus;

    if (self.liveStatus == FMLiveStatusLiveing || self.liveStatus == FMLiveStatusEnd) {  //直播中UI展示逻辑
        // 如果是从预约状态切换到直播中，需要先停止宣传视频播放
        if (isStatusChangedFromNotStartToLiving && detailModel.videoUrl.length > 0) {
            // 如果播放器处于全屏状态，先退出全屏
            if ([FMPlayerManager shareManager].player.isFullScreen) {
                [[FMPlayerManager shareManager].player enterFullScreen:NO animated:NO];
            }

            // 停止宣传视频播放
            [[FMPlayerManager shareManager].player.currentPlayerManager pause];
            [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = YES;

            // 清理播放器状态，准备切换到直播流
            [FMPlayerManager shareManager].player.assetURL = nil;
        }

        [self setUpLivingUI];
    } else if (self.liveStatus == FMLiveStatusNotStart) {  //预约状态
        // 检查是否有宣传视频
        if (detailModel.videoUrl.length > 0) {
            // 有宣传视频，设置播放器并自动播放
            [self setupPromotionVideoPlayer];
        }
    }

    if ([self.detailModel.status integerValue] ==     FMLiveStatusLiveing ||
        [self.detailModel.status integerValue] ==     FMLiveStatusEnd) {
        [self.navigationController setNavigationBarHidden:YES animated:NO];
        self.selfNavigationBarHidden = YES;
    } else {
        [self.navigationController setNavigationBarHidden:NO animated:NO];
        self.selfNavigationBarHidden = NO;
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.tableView reloadData];
    });
}

/// 直播中的UI展示
-(void)setUpLivingUI{
    _coinOrVipView.hidden = true;
    // 聊天室连接websocket 载入数据
    [self.chatRoomView prepareLinkWebsocket];
    if (self.detailModel.liveType == FMDaKaLiveTypeVideo) {
        // 视频直播UI
        [self setUpVideoLivingUI];
    } else if (self.detailModel.liveType == FMDaKaLiveTypeWord) {
        // 文字直播UI
        [self setUpWordLivingUI];
    }
}

// 视频直播UI
-(void)setUpVideoLivingUI {
    // 播放结束 && 回看地址为空
    if (self.liveStatus == FMLiveStatusEnd && self.detailModel.replayUrl.length == 0) {
        //展示引导图
        [self showGuideView];
    } else {
        //视频直播
        BOOL hasPermission = self.detailModel.ticketInfo.needPay == 0 && self.detailModel.needVip == 0;
        if  (hasPermission) {// 无需权限
            //免费直播
            [self configPlayer];
            //展示引导图
            [self showGuideView];
        } else { // 需要权限
            //  hasTryPlayTime > 0当前用户可试看  tryPlayMinute当前直播可以试看，当前用户试看结束
            if (self.detailModel.ticketInfo.hasTryPlayTime.integerValue > 0 || (self.detailModel.tryPlayMinute > 0 && self.detailModel.ticketInfo.hasTryPlayTime.integerValue == 0)) {
                if (self.detailModel.isSaleEnd) {
                    // 卖完了 展示权限弹窗
                    [self showPermissionsAlertVC];
                    return;
                }
                [self resetAlertVC];
                //有试看, 或者试看结束 权限展示逻辑在播放窗口
                [self configPlayer];
                //展示引导图
                [self showGuideView];
            } else {
                //展示权限弹窗
                [self showPermissionsAlertVC];
            }
        }
    }
}

// 文字直播UI
-(void)setUpWordLivingUI {
    // 确保播放器不在全屏状态
    if ([FMPlayerManager shareManager].player && [FMPlayerManager shareManager].player.isFullScreen) {
        [[FMPlayerManager shareManager].player enterFullScreen:NO animated:NO];
    }

    // 是否需要权限
    BOOL hasPermission = self.detailModel.ticketInfo.needPay == 0 && self.detailModel.needVip == 0;
    if (hasPermission) { // 无需权限
        [self wordLiveStartStatistical];
        [self showGuideView];
    } else {
        /**
         需要权限
         1.先看是否能试看   试看结束时间大于 系统当前时间  处于试看期间
         2.不能试看
        */
        if (self.detailModel.tryEndTime > self.detailModel.currTime) {
//            // 售卖完
//            if (self.detailModel.isSaleEnd) {
//                // 展示权限提示弹窗
//                [self showPermissionsAlertVC];
//                return;
//            }
            // 启动定时统计
            [self wordLiveStartStatistical];
            //文字直播试看期间
            self.detailModel.wordTryPlayAlertFlag = false;
            //展示引导图
            [self showGuideView];
        } else {
            //展示权限弹窗
            [self showPermissionsAlertVC];
        }
    }
}

/// 文字直播开始统计
- (void)wordLiveStartStatistical {
    if (!_timer) {
        dispatch_queue_t queue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);
        _timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0,queue);
        dispatch_source_set_timer(_timer,dispatch_walltime(NULL, 0),10.0*NSEC_PER_SEC, 0); //每10秒执行
        WEAKSELF
        dispatch_source_set_event_handler(_timer, ^{
            dispatch_async(dispatch_get_main_queue(), ^{
                [__weakSelf recordViewTime];
            });
        });
        dispatch_resume(_timer);
    }
}

/// 销毁定时器
-(void)destoryTimer {
    if (_timer) {
        dispatch_source_cancel(_timer);
        _timer = nil;
    }
}

/// 展示引导图
- (void)showGuideView {
    NSArray *array = [FMUserDefault getUnArchiverDataForKey:@"guide"];
    if (![array containsObject:[NSNumber numberWithInteger:GuideTypeLiveDetailView]] && [[FMHelper getCurrentVC] isEqual:self]) {
        CGRect titleRect = [self.view convertRect:self.headerView.liveTitleBtn.frame toView: [FMAppDelegate shareApp].main.view];
        CGRect rect = CGRectMake(titleRect.origin.x - 10, titleRect.origin.y - 10 + UI_STATUS_HEIGHT, UI_SCREEN_WIDTH - 20, titleRect.size.height + 20);
        [self.tableView setContentOffset:CGPointMake(0, 0) animated:NO];
        [FMHelper pageGuideWithGuideType:GuideTypeLiveDetailView rect:rect rectArr:nil];
    }
}

#pragma mark - noti
//收到通知 点击通知栏协议跳转  如果是横屏 需要回到竖屏 并做相应跳转
- (void)notificationViewClick:(NSNotification *)noti {
    [FMPlayerManager shareManager].player.viewControllerDisappear = YES;
    /// 点击通知退出全屏
    if ([FMPlayerManager shareManager].player.isFullScreen) {
        [self.controlView.landScapeControlView backBtnClickAction:self.controlView.landScapeControlView.backBtn];
    }
}

// 投顾关注状态通知监听
- (void)updateFocusStatus:(NSNotification *)noti {
    FMLiveDetailModel *model = (FMLiveDetailModel *)noti.object;
    if (model) {
        self.headerView.model = model;
    }
}

// 直播间信息更新
- (void)liveInfoUpdate:(NSNotification *)noti {
    NSDictionary *dic = (NSDictionary *)noti.object;
    if ([dic[@"roomid"] integerValue] != self.detailModel.liveId) {
        return;
    }
    if ([dic[@"websocketMsgType"] isEqualToString:@"LIVESTOP"]) {
        if ( self.detailModel.liveType == FMDaKaLiveTypeVideo && ![self.detailModel.status isEqualToString:@"2"]) { //直播中停播
            self.isStopLive = YES;
            if ([FMPlayerManager shareManager].player.isFullScreen) {
                [[FMPlayerManager shareManager].player enterFullScreen:NO animated:YES];
            }
            [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = YES;
            [[FMPlayerManager shareManager].player.currentPlayerManager pause];
            self.detailModel.status = @"2";
            self.detailModel = self.detailModel;
            [FMPlayerManager dismissGlobalVideoPlayViewWithDestroyPlayer];
        } else {
            self.detailModel.status = @"2";
            self.bottomView.model = self.detailModel;
        }
    } else if ([dic[@"websocketMsgType"] isEqualToString:@"ONLINE"]) {
        self.detailModel.onlineNum = [dic[@"onlineNum"] integerValue];
    } else if ([dic[@"websocketMsgType"] isEqualToString:@"STARTTITLE"]) {
        self.detailModel.name = dic[@"name"];
    } else if ([dic[@"websocketMsgType"] isEqualToString:@"USERZAN"]) {
        self.detailModel.likeNum = [dic[@"likeNum"] integerValue];;
        self.bottomView.model = self.detailModel;
    } else if ([dic[@"websocketMsgType"] isEqualToString:@"NOTICEMSG"]) {
        self.detailModel.bignameDto.userNoticerNums += 1;
    }
    if ([dic[@"websocketMsgType"] isEqualToString:@"permissionsUpdate"]) {
       // 文字直播室权限开通后 直播室通过请求刷新
       [self requestData];
    } else {
        // 直接修改本地模型刷新
        self.headerView.model = self.detailModel;
        self.profileVC.model = self.detailModel;
        self.controlView.model = self.detailModel;
    }
}

- (void)updateActiveInfo:(NSNotification *)noti {
    self.bottomView.model = self.detailModel;
}

- (void)updateStockData:(NSNotification *)noti {
    NSDictionary *data = (NSDictionary *)noti.object;
    if ([data[@"stockDatas"] isKindOfClass:[NSArray class]]) {
        NSArray *array = [NSArray modelArrayWithClass:[FMLiveStockModel class] json:data[@"stockDatas"]];
        NSMutableArray *stockStrArray = [NSMutableArray array];
        for (NSInteger i = 0; i < array.count; i ++) {
            FMLiveStockModel *model = array[i];
            [stockStrArray addObject:model.stockCode];
        }
        self.detailModel.stock = [stockStrArray componentsJoinedByString:@","];
        self.bottomView.model = self.detailModel;
    }
}

- (void)appWillResignActive {
    if (self.needAddTimeCountFlag == FMNeedAddTimeTypeNeed) {
        self.needAddTimeCountFlag = FMNeedAddTimeTypeNotNow;
    }
}

- (void)appDidBecomeActive {
    if (self.needAddTimeCountFlag == FMNeedAddTimeTypeNotNow) {
        self.needAddTimeCountFlag = FMNeedAddTimeTypeNeed;
    }
}

- (void)receiveGiftMsg:(NSNotification *)noti {
    NSDictionary *dic = (NSDictionary *)noti.object;
    if ([dic[@"roomid"] integerValue] != self.detailModel.liveId) {
        return;
    }
    NSString *contentStr = dic[@"content"];
    NSDictionary *contentDic = [JsonTool dicOrArrFromJsonString:contentStr];
    if ([[FMUserDefault getUserId] isEqualToString:contentDic[@"msgFrom"]]) {
        // 自己发送的礼物消息 直接通过接口成功的回调发送 不走ws消息接受的逻辑
        return;
    }
    if (self.controlView.landScapeControlView.hidden) {
        // 竖屏模式,礼物展示在聊天框部分
        LiveGiftMsgInfo *messageinfo = [[LiveGiftMsgInfo alloc] init];
        messageinfo.userId = dic[@"msgFrom"];
        messageinfo.userName = dic[@"userName"];
        messageinfo.userIcon = dic[@"userIco"];
        messageinfo.giftId = [NSString stringWithFormat:@"%@",contentDic[@"id"]];
        messageinfo.dakaId = self.detailModel.bignameDto.userId;
        messageinfo.giftName = contentDic[@"name"];
        messageinfo.giftImg = contentDic[@"image"];
        messageinfo.count = 1;
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.giftView message:messageinfo];
        });
    }
}

- (void)networkChanged {
    if ([FMNetworkStatusMonitor sharedMonitor].currentStatus == NetworkStatusCellular) {
        if ([[FMHelper getCurrentVC] isEqual:self]) {
            [FMProgressHUD showTextOnlyInView:self.controlView withText:@"当前处于非wifi环境,请注意流量消耗"];
        }
    }
}

#pragma mark - private
- (void)closeBtnClick {
    WEAKSELF
    dispatch_async(dispatch_get_main_queue(), ^{
        [__weakSelf.navigationController popViewControllerAnimated:YES];
    });
}

- (void)shareButtonClick:(UIButton *)sender {
    WEAKSELF
    // 调用分享方法
    [FMShareHelper shareLiveDetailWithModel:self.detailModel clickIndexBlock:^(NSInteger index) {
        switch (index) {
            case 0:
                // 微信好友分享逻辑
                break;
            case 1:
                // 朋友圈分享逻辑
                break;
            case 2:
                // 订阅/取消订阅逻辑
                if (__weakSelf.detailModel.isSign.boolValue) {
                    if (__weakSelf.detailModel.isReservation == 1) {
                        ShowConfirm(__weakSelf, @"温馨提示", @"关闭订阅后将收不到开播提醒，请问是否取消", @"取消", @"确定", nil, ^{
                            [__weakSelf seriesLiveSubscribeRequest];
                        });
                    } else {
                        [__weakSelf seriesLiveSubscribeRequest];
                    }
                }
                break;
            case 3:
                // 小窗播放逻辑
                [__weakSelf videoSmallViewPlay];
                break;
            case 4:
                // 反馈逻辑
                [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://complaint?id=%ld&type=1", __weakSelf.detailModel.liveId]];
                break;
            default:
                break;
        }
    }];
}

/// 小窗播放
- (void)videoSmallViewPlay {
    /// 小窗口播放
    [FMPlayerManager showGlobalVideoPlayView];
    [FMPlayerManager shareManager].liveModel.passWord = self.passWord;
    /// 点击小窗口播放后 定位到首页
    [ProtocolJump jumpWithUrl:@"qcyzt://home"];
    [self.navigationController popViewControllerAnimated:YES];
}

///配置播放器
- (void)configPlayer {
    [FMPlayerManager shareManager].liveModel = self.detailModel;
    [FMPlayerManager configZFPlayerContainerView:self.headerView.liveBgView controlView:self.controlView videoUrl:self.detailModel.liveUrl];

    // 检查当前播放的URL是否是宣传视频，如果是则强制切换到直播流
    NSURL *currentURL = [FMPlayerManager shareManager].player.assetURL;
    NSURL *promotionURL = self.detailModel.videoUrl.length > 0 ? [NSURL URLWithString:self.detailModel.videoUrl] : nil;
    NSURL *liveURL = [NSURL URLWithString:self.detailModel.liveUrl];

    BOOL isPlayingPromotionVideo = (currentURL && promotionURL && [currentURL.absoluteString isEqualToString:promotionURL.absoluteString]);

    if (!currentURL || isPlayingPromotionVideo) {
        // 如果没有播放源或正在播放宣传视频，切换到直播流
        [self playerPlayBtnClick];
    } else if ([currentURL.absoluteString isEqualToString:liveURL.absoluteString]) {
        // 如果已经在播放直播流，保持当前状态
        // 不是同一个直播间 重置播放速率
        if ([FMPlayerManager shareManager].liveModel.liveId != self.detailModel.liveId) {
            [FMPlayerManager shareManager].player.currentPlayerManager.rate = 1.0;
        } else {
           self.controlView.player.currentPlayerManager.rate = [FMPlayerManager shareManager].player.currentPlayerManager.rate;
        }
        /// 回到直播间如果是暂停状态回来的 则继续播放
        if ([FMPlayerManager shareManager].player.currentPlayerManager.playState == ZFPlayerPlayStatePaused) {
            [[FMPlayerManager shareManager].player.currentPlayerManager play];
        }
        /// 如果是播放状态 修改控制层按钮状态
        if ([FMPlayerManager shareManager].player.currentPlayerManager.playState == ZFPlayerPlayStatePlaying) {
            [self.controlView.portraitControlView playBtnSelectedState:YES];
        }
    } else {
        // 如果播放的是其他URL，强制切换到直播流
        [self playerPlayBtnClick];
    }
    self.controlView.model = self.detailModel;
    
    WEAKSELF
    [FMPlayerManager shareManager].player.orientationDidChanged = ^(ZFPlayerController * _Nonnull player, BOOL isFullScreen) {
        if (__weakSelf.liveStatus == FMLiveStatusNotStart) {
            __weakSelf.tableView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT - (60 + UI_SAFEAREA_BOTTOM_HEIGHT + UI_SAFEAREA_TOP_HEIGHT));
        } else {
            __weakSelf.tableView.frame = CGRectMake(0, UI_STATUS_HEIGHT, UI_SCREEN_WIDTH, __weakSelf.headerView.height);
            __weakSelf.chatRoomView.frame = CGRectMake(0, CGRectGetMaxY(__weakSelf.tableView.frame), UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT - CGRectGetMaxY(__weakSelf.tableView.frame) - UI_SAFEAREA_BOTTOM_HEIGHT - 65);
            __weakSelf.giftView.frame = CGRectMake(__weakSelf.giftView.x, __weakSelf.chatRoomView.y + 30, __weakSelf.giftView.width, __weakSelf.giftView.height);
            
            if (__weakSelf.showQuickMsgView) {
                __weakSelf.quickMsg.frame = CGRectMake(0, UI_SCREEN_HEIGHT - 50 - 65 - UI_SAFEAREA_BOTTOM_HEIGHT, UI_SCREEN_WIDTH, 50);
                __weakSelf.quickMsg.hidden = NO;
            } else {
                __weakSelf.quickMsg.frame = CGRectMake(0, UI_SCREEN_HEIGHT, UI_SCREEN_WIDTH, 50);
                __weakSelf.quickMsg.hidden = YES;
            }
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                if (isFullScreen) {
                    for (id window in [UIApplication sharedApplication].windows) {
                        if ([window isKindOfClass:[ZFLandscapeWindow class]]) {
                            [__weakSelf.commentView removeFromSuperview];
                            __weakSelf.controlView.commentView = __weakSelf.commentView;
                            ZFLandscapeWindow *zfWindow = (ZFLandscapeWindow *)window;
                            [zfWindow addSubview:__weakSelf.commentView];
                            [__weakSelf.commentView layoutIfNeeded];
                            break;
                        }
                    }
                } else {
                    [__weakSelf.commentView removeFromSuperview];
                    __weakSelf.commentView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
                    [__weakSelf.view addSubview:__weakSelf.commentView];
                    [__weakSelf.view bringSubviewToFront:__weakSelf.commentView];
                    [__weakSelf.commentView layoutIfNeeded];
                }
            });
        }
    };
}

//点击播放
- (void)playerPlayBtnClick {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(networkChanged) name:kNetworkStatusChangedNotification object:nil];

    [self showVideoPlayer];
  
}

//展示播放器
- (void)showVideoPlayer {
    if (self.liveStatus == FMLiveStatusLiveing) {
        [[FMPlayerManager shareManager].player playTheIndexPath:nil assetURL:[NSURL URLWithString:self.detailModel.liveUrl]];
    } else if (self.liveStatus ==  FMLiveStatusEnd) {
        [[FMPlayerManager shareManager].player playTheIndexPath:nil assetURL:[NSURL URLWithString:self.detailModel.replayUrl]];
    }
    
    // 配置画中画
    [[FMAppDelegate shareApp] configPiP:[FMPlayerManager shareManager].playerManager.avPlayerLayer];
    
    
    self.controlView.prepareShowControlView = YES;
    
    [self.controlView showTitle:@""
                 coverURLString:self.detailModel.cover
                 fullScreenMode:ZFFullScreenModeLandscape];
    self.controlView.model = self.detailModel;
    
    [FMPlayerManager shareManager].player.playerDidToEnd = ^(id  _Nonnull asset) {
        [[FMPlayerManager shareManager].player seekToTime:0 completionHandler:nil];
        [[FMPlayerManager shareManager].player.currentPlayerManager pause];
    };
    /**
     1. 该直播是否有试看时长 tryPlayMinute
     2. 用户能不能试看 hasTryPlayTime
     */
    @zf_weakify(self)
    [FMPlayerManager shareManager].player.playerPlayTimeChanged = ^(id<ZFPlayerMediaPlayback>  _Nonnull asset, NSTimeInterval currentTime, NSTimeInterval duration) {
        @zf_strongify(self)
        // 记录观看时长 每10s调用一次
        NSUInteger acTimeSeconds = (NSUInteger)currentTime;
        if (acTimeSeconds % 10 == 0 && [[NSString stringWithFormat:@"%.2f",fmod(currentTime, 1)] isEqualToString:@"0.00"]) {
            [FMPlayerManager  recordViewTime];
        }
        if (self.detailModel.ticketInfo.needPay == 1) {
            if (self.detailModel.status.integerValue == FMLiveStatusEnd) { // 直播结束
                if (self.detailModel.ticketInfo.hasTryPlayTime.integerValue > 0) { // 进来时有试看时长
                    if (floorf(currentTime) >= self.detailModel.tryPlayMinute * 60)  {  // 播放时长大于试看时长
                        self.detailModel.playStatus = 2; //试看结束 可以重新试看
                        if ([FMPlayerManager shareManager].player.isFullScreen) {
                            [[FMPlayerManager shareManager].player enterFullScreen:NO animated:YES];
                        }
                        
                        [[FMPlayerManager shareManager].player.currentPlayerManager pause];
                        [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = YES;
                        
                        self.headerView.model = self.detailModel;
                        self.controlView.model = self.detailModel;
                    }
                } else { // 没有试看时长了
                    self.detailModel.ticketInfo.hasTryPlayTime = @"1";
                    self.detailModel.playStatus = 2; //试看结束 可以重新试看
                    [[FMPlayerManager shareManager].player.currentPlayerManager pause];
                    [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = YES;
                    self.headerView.model = self.detailModel;
                    self.controlView.model = self.detailModel;
                }
            } else { // 直播中
                if (self.detailModel.ticketInfo.hasTryPlayTime.integerValue > 0) { // 进来时有试看时长
                    NSUInteger acTimeSeconds = (NSUInteger)currentTime;
                    if (acTimeSeconds % 60 == 0) {
                        if (acTimeSeconds - self.currentTryTime  > 1.0000) {
                            self.currentTryTime = acTimeSeconds;
                            [HttpRequestTool updateHasTryPlayTimeWithRoomId:[NSString stringWithFormat:@"%ld",self.detailModel.liveId] min:1 type:self.liveStatus start:^{
                            } failure:^{
                            } success:^(NSDictionary *dic) {
                                self.detailModel.ticketInfo.hasTryPlayTime = [NSString stringWithFormat:@"%@",dic[@"data"]];
                                //更新hasTryPlayTime字段  为0 表示试看结束
                                if (self.detailModel.ticketInfo.hasTryPlayTime.integerValue == 0) {
                                    [[FMPlayerManager shareManager].player.currentPlayerManager pause];
                                    [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = YES;
                                    if ([FMPlayerManager shareManager].player.isFullScreen) {
                                        [[FMPlayerManager shareManager].player enterFullScreen:NO animated:YES];
                                    }
                                    self.headerView.model = self.detailModel;
                                    self.controlView.model = self.detailModel;
                                }
                            }];
                        }
                    }
                } else { // 没有试看时长了
                    [[FMPlayerManager shareManager].player.currentPlayerManager pause];
                    [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = YES;
                }
            }
        } else {
            if (!(self.liveStatus == FMLiveStatusEnd && self.detailModel.replayUrl.length == 0)) { //直播结束且没有回看地址不做隐藏校验
                if ([FMPlayerManager shareManager].player.currentPlayerManager.view.hidden) {
                    [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = NO;
                    [[FMPlayerManager shareManager].player.currentPlayerManager.view.superview bringSubviewToFront:[FMPlayerManager shareManager].player.currentPlayerManager.view];
                }
            }
        }
    };
}

//重新试看视频
- (void)videoReWatch:(UIButton *)sender {
    self.detailModel.playStatus = 1;
    self.controlView.portraitControlView.slider.isdragging = NO;
    self.controlView.portraitControlView.slider.isdragging = NO;
    [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = NO;
    [[FMPlayerManager shareManager].player.currentPlayerManager.view.superview bringSubviewToFront:[FMPlayerManager shareManager].player.currentPlayerManager.view];
    self.headerView.model = self.detailModel;
    [[FMPlayerManager shareManager].player.currentPlayerManager replay];
}

///预约直播/
- (void)appointmentBtnClick {
    // 需要密码
    if (self.needPassWord) {
        [self showPassWordAlert:self.detailModel.liveType status:FMLiveStatusNotStart];
        return;
    }
    
    if ([FMHelper checkLoginStatus])  {
        if (self.detailModel.isSign.boolValue) { // 系列直播，不校验权限
            if (self.detailModel.isReservation == 1) {
                ShowConfirm(self, @"温馨提示", @"关闭订阅后将收不到开播提醒，请问是否取消", @"取消", @"确定", nil, ^{
                    [self seriesLiveSubscribeRequest];
                });
            } else {
                [self seriesLiveSubscribeRequest];
            }
        } else {
            [self appiontmentRequest];
        }
    }
}

-(void)resetAlertVC{
    if (self.alertVC) {
        [self.alertVC dismissViewControllerAnimated:false completion:nil];
        [self.alertVC.view removeFromSuperview];
        self.alertVC = nil;
    }
}


/// 展示密码弹窗
/// @param type 文字直播间类型
/// @param status 直播状态：-1-待发布；0-待开播；1-直播中；2-直播结束；3-回放待上传；4-回放
- (void)showPassWordAlert:(FMDaKaLiveType) type status:(FMLiveStatus) status {
    if ([[FMHelper getCurrentVC] isEqual:self]) {
        FMLivePassWordAlertVC *alertVC = [[FMLivePassWordAlertVC alloc] initWithType:type roomId:self.roomId.integerValue];
        alertVC.modalPresentationStyle = UIModalPresentationFullScreen | UIModalPresentationOverCurrentContext;
        WEAKSELF;
        alertVC.checkPassBlock = ^(NSString * password) {
            __weakSelf.passWord = password;
            if (status == FMLiveStatusNotStart) {
                [__weakSelf appiontmentRequest];
            } else {
                [__weakSelf requestData];
            }
        };
        self.passwordAlertVC = alertVC;
        [[FMAppDelegate shareApp].main presentViewController:alertVC animated:NO completion:nil];
    }
}

/// 快捷消息弹出动画
- (void)qucikMsgViewAnimationStart {
    if (!self.showQuickMsgView && self.liveStatus == FMLiveStatusLiveing) {
        self.showQuickMsgView = YES;
        self.commentView.isGreetingTime = YES;
        self.quickMsg.hidden = NO;
        self.chatRoomView.bottomSpace = 50;
        [UIView animateWithDuration:0.8 animations:^{
            self.quickMsg.frame = CGRectMake(0, UI_SCREEN_HEIGHT - 50 - 65 - UI_SAFEAREA_BOTTOM_HEIGHT, UI_SCREEN_WIDTH, 50);
        } completion:^(BOOL finished) {
             self.quickCountDown = [[CountDown alloc] init];
            __block  NSInteger time = 60;
            WEAKSELF
            [self.quickCountDown countDownWithPER_SECBlock:^{
                time--;
                if (time == 0) {
                    [__weakSelf qucikMsgViewAnimationEnd];
                }
            }];
        }];
    }
}

/// 快捷消息消失动画
- (void)qucikMsgViewAnimationEnd {
    self.commentView.isGreetingTime = NO;
    if (self.quickCountDown) {
        [self.quickCountDown destoryTimer];
        self.quickCountDown = nil;
        [UIView animateWithDuration:0.8 animations:^{
            self.quickMsg.frame = CGRectMake(0, UI_SCREEN_HEIGHT, UI_SCREEN_WIDTH, 50);
            self.quickMsg.hidden = YES;
            self.chatRoomView.bottomSpace = 10;
            [self.chatRoomView scrollToBottom];
        }];
    }
}

// 配置评论试图
- (void)confgiCommonView {
    if (!self.commentView) {
        self.commentView = [[FMLiveCommentView alloc] init];
        self.commentView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
        [self.view addSubview:self.commentView];
        [self.view bringSubviewToFront:self.commentView];
        self.commentView.hidden = YES;
        WEAKSELF
        self.commentView.sendBlock = ^(NSString * _Nonnull content) {
            [__weakSelf qucikMsgViewAnimationEnd];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.8 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [__weakSelf.chatRoomView insertNewMessage:content];
            });
        };
    }
}

#pragma mark - showpremissAlert
- (void)showPermissionsAlertVC {
    WEAKSELF;
    FMDakaLiveAlertVC *vc = [[FMDakaLiveAlertVC alloc] initWithSceneType:LiveAlertAcessPermissionsSceneType detailModel:self.detailModel actionBlock:^(FMPermissionsMethod permissionsWay) {
        if ([__weakSelf.detailModel.status integerValue] ==  FMLiveStatusLiveing || [__weakSelf.detailModel.status integerValue] == FMLiveStatusEnd) {
            if (__weakSelf.detailModel.isSaleEnd) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [__weakSelf.navigationController popViewControllerAnimated:YES];
                });
            }
        }
        __weakSelf.permissionsWay = permissionsWay;
        [__weakSelf acessPermissions];
    } closeBlock:^{
        if (__weakSelf.liveStatus == FMLiveStatusNotStart) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [__weakSelf.navigationController popViewControllerAnimated:YES];
            });
        } else {
            if ([__weakSelf.detailModel.status integerValue] == FMLiveStatusLiveing) {
                if (__weakSelf.detailModel.liveType == FMDaKaLiveTypeWord &&
                    __weakSelf.detailModel.tryEndTime > __weakSelf.detailModel.currTime) {
                    //文字直播试看中 关闭弹窗 停在当前页面
                } else {
                    [__weakSelf closeBtnClick];
                }
            } else {
                [__weakSelf closeBtnClick];
            }
        }
        __weakSelf.alertVC = nil;
    }];
    self.alertVC = vc;
    [self.view addSubview:vc.view];
    [self.view bringSubviewToFront:vc.view];
}

-(void)payCoin{
    [FMPayHandle payCoinWithDetailModel:self.detailModel success:^(EnablePayModel * _Nonnull selectedModel) {
        // 重置弹窗
        [self resetAlertVC];
        // 更新本地金币
        [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];
        
        if (self.liveStatus == FMLiveStatusNotStart) {
            //预约直播
            [self appiontmentRequest];
            [self requestData];
        } else {
            //更新直播间
            [self requestData];
            if (self.detailModel.liveType == FMDaKaLiveTypeVideo) {
                if ([FMPlayerManager shareManager].player) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        self.detailModel.ticketInfo.needPay = 0;
                        [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = NO;
                        [[FMPlayerManager shareManager].player.currentPlayerManager.view.superview bringSubviewToFront:[FMPlayerManager shareManager].player.currentPlayerManager.view];
                        [[FMPlayerManager shareManager].player.currentPlayerManager play];
                    });
                }
            }
            if (self.detailModel.liveType == FMDaKaLiveTypeWord) {
                // 启动定时统计
                [self wordLiveStartStatistical];
            }
        }
    }];
}



///获取观看直播权限
- (void)acessPermissions {
    if ([FMHelper checkLoginStatus]) {
        if (self.permissionsWay == FMPermissionsMethodContactCounselor) {
            //联系顾问
            [FMPayHandle contactAdviser];
        } else if (self.permissionsWay == FMPermissionsMethodPayCoin) {
            [self payCoin];
        }
    }
}

#pragma mark - request
/// 是否为密码房
- (void)reqIfSecretRoom {
    [HttpRequestTool liveInfoIsPasswordWith:[self.roomId integerValue] start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        self.errorInfo.hidden = NO;
    } success:^(NSDictionary *dic) {
        self.errorInfo.hidden = YES;
        if ( [dic[@"status"] isEqualToString:@"1"] && [dic[@"data"] isKindOfClass:[NSDictionary class]] ) {
            FMDakaLiveDetailNeedPwdModel *nModel = [FMDakaLiveDetailNeedPwdModel modelWithJSON:dic[@"data"]];
            // 保存需要密码的状态
            self.needPassWord = nModel.needPassWord;
            // 预约 || 不需要密码
            if (nModel.liveStatus == FMLiveStatusNotStart || !nModel.needPassWord) {
                //接口请求(请求刷线UI逻辑叫多 页面有闪动的效果 加上延时先布局默认状态在更新)
                LZDispatchAfter(0.5, ^{[self requestData];})
            } else {
                // 需要密码
                [SVProgressHUD dismiss];
                [self showPassWordAlert:nModel.liveType status:nModel.liveStatus];
            }
        } else {
            [self reqErrHandle:dic];
        }
    }];
}

/// 校验直播间密码 (针对小窗播放回到直播间的情况 因此不用考虑 直播状态和 needPassWord 的判断逻辑)
- (void)passWordVerify {
    [HttpRequestTool liveInfojudgePasswordWithRoomId:self.roomId.integerValue passWord:self.passWord start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self requestData];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}


/// 获取直播详情
- (void)requestData {
    [HttpRequestTool dakaLiveDetailWithRoomId:self.roomId start:^{
    } failure:^{
        [SVProgressHUD dismiss];
        self.errorInfo.hidden = NO;
    } success:^(NSDictionary *dic) {
        [SVProgressHUD dismiss];
        [self.view dismissNoNetWorkView];
        self.errorInfo.hidden = YES;
        if ([dic[@"status"] isEqualToString:@"1"]) {
            if ([dic[@"data"] isKindOfClass:[NSDictionary class]]) {
                // 密码弹窗消失
                if (self.passWord.length > 0) {
                    self.pwdChecked = true;
                    [self.passwordAlertVC dismissViewControllerAnimated:NO completion:nil];
                }
                // 刷新页面
                self.detailModel = [FMLiveDetailModel modelWithDictionary:dic[@"data"]];
                
                // 获取礼物配置信息
                if (self.detailModel.liveStatus != FMLiveStatusEnd) {
                    [self getGiftConfigInfo];
                }
                LZDispatchAfter(0.5, ^{ [self.tableView reloadData];} );
            }
        } else {
            if (self.passWord.length > 0) {
                self.passwordAlertVC.tipLB.textColor = FMNavColor;
                self.passwordAlertVC.tipLB.text = @"密码错误,请重新输入";
                return;
            }
            [self reqErrHandle:dic];
        }
    }];
}

/// 请求失败的处理
/// @param dic 请求失败的响应
-(void)reqErrHandle:(NSDictionary *)dic{
    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
    if ([dic[@"errcode"] isEqualToString:@"101"]) {
        // 101 直播间不存在
        LZDispatchAfter(1, ^{ [self.navigationController popViewControllerAnimated:YES]; });
    } else if ([dic[@"errcode"] isEqualToString:@"100"]) {
        LZDispatchAfter(1, ^{ [self.navigationController popViewControllerAnimated:YES]; });
    } else {
        self.errorInfo.hidden = NO;
    }
}

/// 订阅/取消订阅系列直播
- (void)seriesLiveSubscribeRequest {
    [HttpRequestTool seriesLiveSubscribeByisSubscribe:(self.detailModel.isReservation == 1) signId:self.detailModel.signId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            if (self.detailModel.isReservation == 1) {
                [SVProgressHUD showSuccessWithStatus:@"订阅已取消"];
                if (self.detailModel.onlineNum > 0) {
                    self.detailModel.onlineNum --;
                }
            } else {
                [SVProgressHUD showSuccessWithStatus:@"订阅成功，将在开播时提醒您"];
                self.detailModel.onlineNum ++;
            }
            self.detailModel.isReservation = self.detailModel.isReservation == 0 ? 1 : 0;
           
            [self.appointmentBtn setTitle:self.detailModel.isSign.boolValue ? @"立即订阅" : @"立即预约" forState:UIControlStateNormal];
            [self.appointmentBtn setTitle:self.detailModel.isSign.boolValue ? @"取消订阅" : @"已预约" forState:UIControlStateSelected];
            self.appointmentBtn.selected = self.detailModel.isReservation == 1;
            if (self.detailModel.isReservation) {
                self.coinOrVipView.hidden = true;
                self.appointmentBtn.hidden = false;
                self.appointmentBtn.backgroundColor = ColorWithHex(0xdddddd);
            } else {
                self.appointmentBtn.backgroundColor = FMNavColor;
            }
            if (self.reservationBlock) {
                self.reservationBlock([NSNumber numberWithInteger:self.detailModel.isReservation].boolValue);
            }
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.tableView reloadData];
            });
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

/// 预约直播
- (void)appiontmentRequest {
    [HttpRequestTool cancelOrSubscribeLiveWithIsReservation:self.detailModel.isReservation roomId:self.roomId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            if (self.passWord.length > 0) {
                self.pwdChecked = true;
                [self.passwordAlertVC dismissViewControllerAnimated:NO completion:nil];
            }
            
            if (self.detailModel.isReservation == 1) {
                [SVProgressHUD showSuccessWithStatus:@"预约已取消"];
                if (self.detailModel.onlineNum > 0) {
                    self.detailModel.onlineNum --;
                }
            } else {
                [SVProgressHUD showSuccessWithStatus:@"预约成功，开播会提醒您"];
                self.detailModel.onlineNum ++;
            }
            self.detailModel.isReservation = !self.detailModel.isReservation;

            self.appointmentBtn.hidden = false;
            self.coinOrVipView.hidden = true;
            self.appointmentBtn.selected = self.detailModel.isReservation == 1;
            if (self.detailModel.isReservation == 1) {
                self.appointmentBtn.backgroundColor = ColorWithHex(0xdddddd);
            } else {
                self.appointmentBtn.backgroundColor = FMNavColor;
            }
            if (self.reservationBlock) {
                self.reservationBlock([NSNumber numberWithInteger:self.detailModel.isReservation].boolValue);
            }
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.tableView reloadData];
            });
            
            [[NSNotificationCenter defaultCenter] postNotificationName:kLiveAppointmentStatusChangeNotification object:nil userInfo:@{@"roomId" : [NSString stringWithFormat:@"%ld",self.detailModel.liveId], @"status" : @(self.detailModel.isReservation)}];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }] ;
}

/// 记录观看时长
- (void)recordViewTime {
    [HttpRequestTool recordViewingTimeWithRoomId:[NSString stringWithFormat:@"%ld",self.detailModel.liveId] start:^{
    } failure:^{
    } success:^(NSDictionary *dic) {
    }];
}


/// 发送达到任务请求
/// @param taskModel FMTaskConfigModel
-(void)postReqToReachTaskTimeRequire {
    [HttpRequestTool requestCompleteWatchLiveTaskWithRoomId:[NSString stringWithFormat:@"%zd", self.detailModel.liveId] start:^{
    } failure:^{
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            FMSubTask *subTask = [FMSubTask modelWithDictionary:dic[@"data"]];
            //直播间任务完成更新 任务进度
            FMTaskConfigModel *userTaskProgress = [FMUserDefault getUnArchiverDataForKey:UserTaskProgressCacheKey];
            userTaskProgress.taskDic[@(FMTaskTypeWatchLive)] = subTask;
            [FMUserDefault setArchiverData:userTaskProgress forKey:UserTaskProgressCacheKey];
            
            if (subTask.completeNum >= subTask.taskNeedNum) {
                [SVProgressHUD showImage:nil status:@"任务完成！奖励已放入您的账户"];
            }
            // 记录完成时间
            [self recordCompeleteTime:dic];
        } else {
            // 4102 4101代表什么？
            if ([dic[@"errcode"] isEqualToString:@"4102"] ||
                [dic[@"errcode"] isEqualToString:@"4101"]) {
                [self recordCompeleteTime:dic];
            }
        }
    }];
}

// 获取直播间礼物配置信息
- (void)getGiftConfigInfo {
    [HttpRequestTool getLiveGiftConfigInfoWithstart:^{
    } failure:^{
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            if ([dic[@"data"] isKindOfClass:[NSArray class]]) {
                NSArray *arr = [NSArray arrayWithArray:dic[@"data"]];
                self.bottomView.giftItemArr = [NSArray arrayWithArray:arr];
                self.controlView.giftItemArr = [NSArray arrayWithArray:arr];
            }
        }
    }];
}


// 记录当前直播间完成了今日直播任务
- (void)recordCompeleteTime:(NSDictionary *)dic {
    // 完成任务的时间
    NSDate *currentDate = [NSDate dateWithTimeIntervalSince1970:[dic[@"systemTime"] longValue] / 1000];
    //完成任务的日期字符串
    NSString *currentDateStr = [NSString stringFromDate:currentDate format:@"yyyy/MM/dd"];
    // 完成任务日期这天0点的时间
    NSDate *current0Date = [NSString dateFromFormatedString:currentDateStr format:@"yyyy/MM/dd"];
    // 完成任务日期当天24点的时间戳
    NSTimeInterval current24Time = [current0Date timeIntervalSince1970] + 60 * 60 * 24;
    [FMUserDefault setArchiverData:[NSNumber numberWithLongLong:current24Time * 1000] forKey:[NSString stringWithFormat:@"%ld",self.detailModel.liveId]];
}

#pragma mark - headerView playerStatusViewdelegate

/// 关注投顾
- (void)focusDaka:(UIButton *)sender containerVC:(FMDakaLiveDakaProfileVC *)vc {
    [FMCommonHttp focusBtnWithButton:sender bigCastId:self.detailModel.bignameDto.userId roomId:self.roomId focusSuccess:^{
        // 直播间关注投顾 会发送关注消息 根据此消息 关注加1  这里的关注成功只有数据刷新 关注数不自增 添加延时是保证在收到消息更新了关注数后在跟心数据源
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            self.headerView.model = self.detailModel;
            self.controlView.model = self.detailModel;
            if (vc) {
                vc.model = self.detailModel;
            }
        });
    }];
}

/// 取消关注投顾
- (void)unfollow:(UIButton *)sender containerVC:(FMDakaLiveDakaProfileVC *)vc {
    [FMCommonHttp UnfollowWithButton:sender bigCastId:self.detailModel.bignameDto.userId unfollowSuccess:^{
        self.detailModel.bignameDto.userNoticerNums -= 1;
        self.headerView.model = self.detailModel;
        self.controlView.model = self.detailModel;
        if (vc) {
            vc.model = self.detailModel;
        }
    }];
}

#pragma mark - customLivePlayerControlViewDelegate
 //播放器控制层 开通权限 事件
- (void)openPlayerPermissionsWith:(NSInteger)payWay {
    self.permissionsWay = payWay;
    [self acessPermissions];
}

#pragma mark - tableDelegate
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (self.liveStatus == FMLiveStatusNotStart) {
        return 1;
    }
    return 0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.liveStatus == FMLiveStatusNotStart && self.detailModel) {
        FMDakaLiveIntroduceTabCell *cell = [tableView reuseCellClass:[FMDakaLiveIntroduceTabCell class]];
        cell.model = self.detailModel;
        cell.tableView = tableView;
        return cell;
    }
    return [UITableViewCell new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.liveStatus == FMLiveStatusNotStart && self.detailModel) {
        WEAKSELF;
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMDakaLiveIntroduceTabCell class]) configuration:^(FMDakaLiveIntroduceTabCell *cell) {
            cell.model = __weakSelf.detailModel;
            cell.systemTime = [FMUserDefault getServerSystemTime];
        }];
    }
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (self.liveStatus == FMLiveStatusNotStart && self.detailModel) {
        return [self buildLiveNotStartHeader];
    }
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (self.liveStatus == FMLiveStatusNotStart && self.detailModel) {
        return 64;
    }
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

-(UIView *)buildLiveNotStartHeader{
    UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 64)];
    view.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    
    UIView *bottomView = [[UIView alloc] initWithFrame:CGRectMake(0, 10, UI_SCREEN_WIDTH, 54)];
    bottomView.backgroundColor = UIColor.up_contentBgColor;
    [view addSubview:bottomView];
    UILabel *titleLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(17) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1];
    titleLB.text = @"直播介绍";
    [bottomView addSubview:titleLB];
    [titleLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(bottomView.mas_top).offset(20);
        make.left.equalTo(bottomView.mas_left).offset(25);
    }];
    
    // 直播介绍左边的红线
    UIView *line = [[UIView alloc] init];
    line.backgroundColor = FMNavColor;
    [bottomView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(bottomView.mas_left).offset(15);
        make.centerY.equalTo(titleLB);
        make.size.equalTo(@(CGSizeMake(3, 12)));
    }];
    return view;
}

#pragma mark - HeaderViewDelegate
//投顾信息页
- (void)liveProfileViewShow:(UIButton *)sender {
    if (sender.selected) {
        self.profileVC.model = self.detailModel;
        WEAKSELF;
        self.profileVC.dismissBlock = ^{
            sender.selected = !sender.selected;
            [__weakSelf.profileVC dismissViewControllerAnimated:NO completion:nil];
        };
        [self presentViewController:self.profileVC animated:NO completion:nil];
    }
}

//关注投顾
- (void)dakaProfileViewShow:(UITapGestureRecognizer *)sender {
    WEAKSELF;
    FMDakaLiveDakaProfileVC *vc = [[FMDakaLiveDakaProfileVC alloc] init];
    vc.modalPresentationStyle = UIModalPresentationFullScreen | UIModalPresentationOverCurrentContext;
    __weak FMDakaLiveDakaProfileVC *weakProfileVC = vc;
    vc.block = ^(UIButton *sender) {
        if (!sender.selected) {
            [__weakSelf focusDaka:sender containerVC:weakProfileVC];
        } else  {
            [__weakSelf unfollow:sender containerVC:weakProfileVC];
        }
    };
    [self presentViewController:vc animated:NO completion:nil];
    vc.model = self.detailModel;
}

#pragma mark - PlayerStatusViewDelegate
//重新试看
- (void)playerStatusViewLiveRewatch:(UIButton *)button {
    [self videoReWatch:nil];
}

//头不试图 试看时 权限开通事件
- (void)playerStatusAccessPermissions:(NSInteger)payType {
    self.permissionsWay = payType;
    [self acessPermissions];
}


#pragma mark - setter/getter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped delegate:self dataSource:self viewController:self];
        _tableView.backgroundColor = UIColor.up_contentBgColor;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_tableView registerCellClass:[FMDakaLiveIntroduceTabCell class]];
    }
    return _tableView;
}

- (FMDakaLiveDetailHeaderView *)headerView {
    if (!_headerView) {
        _headerView = [[FMDakaLiveDetailHeaderView alloc] init];
        _headerView.delegate = self;
        _headerView.playStatusView.delegate = self;
        WEAKSELF;
        _headerView.focusActionBlock = ^(UIButton * _Nonnull sender) {
            if (!sender.selected) {
                [__weakSelf focusDaka:sender containerVC:nil];
            } else {
                [__weakSelf unfollow:sender containerVC:nil];
            }
        };
        _headerView.updateFrameBlock = ^{
            if (__weakSelf.liveStatus != FMLiveStatusNotStart) {
                __weakSelf.tableView.frame = CGRectMake(0, UI_STATUS_HEIGHT, UI_SCREEN_WIDTH, __weakSelf.headerView.height);
                __weakSelf.chatRoomView.frame = CGRectMake(0, CGRectGetMaxY(__weakSelf.tableView.frame), UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT - CGRectGetMaxY(__weakSelf.tableView.frame) - UI_SAFEAREA_BOTTOM_HEIGHT - 65);
            }
        };
    }
    return _headerView;
}

- (UIButton *)appointmentBtn {
    if (!_appointmentBtn) {
        _appointmentBtn = [[UIButton alloc] init];
        [_appointmentBtn setTitleColor:FMWhiteColor forState:UIControlStateNormal];
        [_appointmentBtn setTitle:@"已预约" forState:UIControlStateSelected];
        [_appointmentBtn setTitle:@"预约直播" forState:UIControlStateNormal];
        [_appointmentBtn setTitleColor:UIColor.up_textSecondary1Color forState:UIControlStateSelected];
        _appointmentBtn.titleLabel.font = FontWithSize(17.0);
        [_appointmentBtn addTarget:self action:@selector(appointmentBtnClick) forControlEvents:UIControlEventTouchUpInside];
        UI_View_Radius(_appointmentBtn, 45 / 2.0);
        _appointmentBtn.hidden = YES;
    }
    return _appointmentBtn;
}

- (FMCustomDakaLivePlayerControlView *)controlView {
    if (!_controlView) {
        _controlView = [[FMCustomDakaLivePlayerControlView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_WIDTH * (9 / 16.0) + 115)];
        _controlView.delegate = self;
        WEAKSELF
        // 发送消息
        _controlView.sendMessageBlock = ^(NSString * _Nonnull content) {
            [__weakSelf qucikMsgViewAnimationEnd];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.8 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [__weakSelf.chatRoomView insertNewMessage:content];
            });
        };
        // 小窗播放
        _controlView.smallViewPlay = ^{
            [__weakSelf videoSmallViewPlay];
        };
        _controlView.inputViewClickBlock = ^{
            [__weakSelf.commentView.textView becomeFirstResponder];
        };
    }
    return _controlView;
}

- (FMLiveProfileVC *)profileVC {
    if (!_profileVC) {
        _profileVC = [[FMLiveProfileVC alloc] init];
        _profileVC.modalPresentationStyle = UIModalPresentationFullScreen | UIModalPresentationOverCurrentContext;
    }
    return _profileVC;
}

- (UIButton *)closeBtn {
    if (!_closeBtn) {
        UIButton *closeBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:FMImgInBundle(@"导航/亮黑暗灰关闭") target:self action:@selector(closeBtnClick)];
        _closeBtn = closeBtn;
        _shareButton.exclusiveTouch = YES;
        _closeBtn.hidden = YES;
    }
    return _closeBtn;
}

- (UIButton *)shareButton {
    if (!_shareButton) {
        UIButton *shareBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:FMImgInBundle(@"导航/亮黑暗灰更多") target:self action:@selector(shareButtonClick:)];
        _shareButton = shareBtn;
    }
    return _shareButton;
}

- (LiveChatRoomTabView *)chatRoomView {
    if (!_chatRoomView) {
        _chatRoomView = [[LiveChatRoomTabView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        WEAKSELF
        _chatRoomView.showTopMsgDetailBlock = ^(LiveMessageFrameModel * _Nonnull frameModel) {
            __weakSelf.topMsgDetailView.hidden = NO;
            __weakSelf.topMsgDetailView.frameModel = frameModel;
            __weakSelf.topMsgDetailView.detailModel = __weakSelf.detailModel;
            [__weakSelf.view bringSubviewToFront:__weakSelf.topMsgDetailView];
        };
        _chatRoomView.msgloaderCompletedBlock = ^{
            __weakSelf.controlView.barrageMsgArr = __weakSelf.chatRoomView.viewModel.frameModelArray;
            
            // 1s动画载入快捷消息
            [__weakSelf qucikMsgViewAnimationStart];
        };
        _chatRoomView.viewModel.refreshLiveInfo = ^{
            [__weakSelf requestData];
        };
        // 拉起送礼物面板
        _chatRoomView.openGiftChooseVew = ^(NSString * _Nonnull content) {
            NSDictionary *contentDic = [JsonTool dicOrArrFromJsonString:content];
            __weakSelf.giftChooseView.giftId = [contentDic[@"id"] integerValue];
            __weakSelf.giftChooseView.dataArr = __weakSelf.bottomView.giftItemArr;
            [__weakSelf.giftChooseView show];
        };
        _chatRoomView.grettingMsgBlock = ^{
            [__weakSelf.commentView.textView becomeFirstResponder];
        };
    }
    return _chatRoomView;
}

- (LiveDetailBottomView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[LiveDetailBottomView alloc] init];
        // 送礼物
        WEAKSELF
        _bottomView.sendGiftBtnClickBlock = ^(NSArray * _Nonnull arr) {
            __weakSelf.giftChooseView.dataArr = arr;
            [__weakSelf.giftChooseView show];
        };
        _bottomView.inputViewClickBlock = ^{
            [__weakSelf.commentView.textView becomeFirstResponder];
        };
    }
    return _bottomView;
}

- (FMLiveQuickMsgView *)quickMsg {
    if (!_quickMsg) {
        _quickMsg = [[FMLiveQuickMsgView alloc] init];
        _quickMsg.dataArr = @[@"老师好！",@"大家好！",@"签到！"];
        WEAKSELF
        _quickMsg.sendQuickMsg = ^(NSString * _Nonnull content) {
            [__weakSelf qucikMsgViewAnimationEnd];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.8 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [__weakSelf.chatRoomView insertNewMessage:content];
            });
        };
    }
    return _quickMsg;
}

- (FMLiveErroInfoView *)errorInfo {
    if (!_errorInfo) {
        _errorInfo = [[FMLiveErroInfoView alloc] init];
        _errorInfo.hidden = YES;
        WEAKSELF
        _errorInfo.reloadBlock = ^{
            [__weakSelf reqIfSecretRoom];
        };
    }
    return _errorInfo;
}

- (FMLiveTopMsgDetailView *)topMsgDetailView {
    if (!_topMsgDetailView) {
        _topMsgDetailView = [[FMLiveTopMsgDetailView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT)];
        _topMsgDetailView.hidden = YES;
        WEAKSELF
        _topMsgDetailView.messagePaymentBlock = ^(LiveMessageFrameModel * _Nonnull frameModel) {
            __weakSelf.chatRoomView.topMsgView.frameModel = frameModel;
            for (NSInteger i = 0; i < __weakSelf.chatRoomView.viewModel.frameModelArray.count; i ++) {
                LiveMessageFrameModel *model = __weakSelf.chatRoomView.viewModel.frameModelArray[i];
                if (model.model.messageId == frameModel.model.messageId) {
                    [__weakSelf.chatRoomView.viewModel.frameModelArray replaceObjectAtIndex:i withObject:frameModel];
                    [__weakSelf.chatRoomView reloadData];
                    break;
                }
            }
        };
        [self.view addSubview:_topMsgDetailView];
    }
    return _topMsgDetailView;
}


-(FMPayCoinOrBuyVipView *)coinOrVipView{
    if(!_coinOrVipView){
        _coinOrVipView = [[NSBundle mainBundle] loadNibNamed:NSStringFromClass(FMPayCoinOrBuyVipView.class) owner:nil options:nil].firstObject;
        WEAKSELF;
        [_coinOrVipView.leftItem bk_whenTapped:^{
            if (![FMHelper checkLoginStatus]) { return;}
            [FMPayHandle pushToVipBuyPageWithDetailModel:__weakSelf.detailModel];
        }];
        [_coinOrVipView.rightItem bk_whenTapped:^{
            if (![FMHelper checkLoginStatus]) { return;}
            [__weakSelf payCoin];
        }];
    }
    return _coinOrVipView;
}


- (LiveGiftContainerView *)giftView {
    if (!_giftView) {
        _giftView = [[LiveGiftContainerView alloc] initWithFrame:CGRectMake(15, 30, 250, 0)];
    }
    return _giftView;
}

- (LiveGiftBoardView *)giftChooseView {
    if (!_giftChooseView) {
        LiveGiftBoardView *giftChooseView = [[LiveGiftBoardView alloc] initPageLandScape:NO];
        giftChooseView.detailModel = self.detailModel;
        WEAKSELF;
        giftChooseView.sendGiftBlock = ^(LiveGiftModel * _Nonnull model) {
            LiveGiftMsgInfo *messageinfo = [[LiveGiftMsgInfo alloc] init];
            messageinfo.userId = [FMUserDefault getUserId];
            messageinfo.userName = [FMUserDefault getNickName];
            messageinfo.userIcon = [FMUserDefault getUserFace];
            messageinfo.giftId = [NSString stringWithFormat:@"%ld",model.giftId];
            messageinfo.dakaId = __weakSelf.detailModel.bignameDto.userId;
            messageinfo.giftName = model.name;
            messageinfo.giftImg = model.image;
            messageinfo.count = 1;
            [__weakSelf.giftView message:messageinfo];
        };
        giftChooseView.rechargeBlock = ^{
            [[FMPayTool payTool] gotoRecharge];
        };
        _giftChooseView = giftChooseView;
    }
    return _giftChooseView;
}

#pragma mark - 宣传视频播放相关方法

/// 设置宣传视频播放器
- (void)setupPromotionVideoPlayer {
    // 配置播放器
    [FMPlayerManager shareManager].liveModel = self.detailModel;
    [FMPlayerManager configZFPlayerContainerView:self.headerView.liveBgView controlView:self.controlView videoUrl:self.detailModel.videoUrl];

    // 设置屏幕旋转回调（修复宣传视频播放时横竖屏切换空白区域问题）
    WEAKSELF
    [FMPlayerManager shareManager].player.orientationDidChanged = ^(ZFPlayerController * _Nonnull player, BOOL isFullScreen) {
        if (__weakSelf.liveStatus == FMLiveStatusNotStart) {
            __weakSelf.tableView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT - (60 + UI_SAFEAREA_BOTTOM_HEIGHT + UI_SAFEAREA_TOP_HEIGHT));

            [__weakSelf.tableView reloadData];
        }
    };

    // 自动播放宣传视频
    [self playPromotionVideo];

    // 添加播放完成回调
    @zf_weakify(self)
    [FMPlayerManager shareManager].player.playerDidToEnd = ^(id  _Nonnull asset) {
        @zf_strongify(self)

        [[FMPlayerManager shareManager].player seekToTime:0 completionHandler:nil];
        [[FMPlayerManager shareManager].player.currentPlayerManager pause];

        // 如果不在全屏状态，直接显示封面
        if (![FMPlayerManager shareManager].player.isFullScreen) {
            [self showCoverWithPlayButton];
        }
    };
}

/// 播放宣传视频
- (void)playPromotionVideo {
    if (self.detailModel.videoUrl.length > 0) {
        // 隐藏播放按钮
        self.playButton.hidden = YES;

        // 确保播放器视图可见（修复重复播放时画面不显示的问题）
        [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = NO;

        [[FMPlayerManager shareManager].player playTheIndexPath:nil assetURL:[NSURL URLWithString:self.detailModel.videoUrl]];

        self.controlView.prepareShowControlView = YES;
//        [self.controlView showTitle:@""
//                     coverURLString:self.detailModel.cover
//                     fullScreenMode:ZFFullScreenModeLandscape];
        self.controlView.model = self.detailModel;
    }
}

/// 显示封面并添加播放按钮
- (void)showCoverWithPlayButton {
    // 暂停播放器
    [[FMPlayerManager shareManager].player.currentPlayerManager pause];
    [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = YES;

    // 显示封面
    if (!self.playButton) {
        // 创建播放按钮
        self.playButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [self.playButton setImage:ImageWithName(@"live_list_play") forState:UIControlStateNormal];
        self.playButton.frame = CGRectMake(0, 0, 60, 60);
        [self.playButton addTarget:self action:@selector(playPromotionVideo) forControlEvents:UIControlEventTouchUpInside];
        [self.headerView.liveBgView addSubview:self.playButton];
    }

    // 设置播放按钮位置并显示
    self.playButton.center = CGPointMake(self.headerView.liveBgView.bounds.size.width / 2,
                                        self.headerView.liveBgView.bounds.size.height / 2);
    self.playButton.hidden = NO;
}

@end


