//
//  FMNewUserGiftBagPopView.m
//  QCYZT
//
//  Created by zeng on 2022/5/13.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMNewUserGiftBagPopView.h"
#import "FMTaskConfigModel.h"
#import "HttpRequestTool+DailyTask.h"
#import "YTGNormalWebVC.h"
#import "FMHomePageVC.h"
#import "FMPayTool.h"
#import "FMProgressHUD.h"

@interface NewUserGifBagCell : UITableViewCell

@property (nonatomic, strong) UIImageView *iconImgV;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UIImageView *goldImgV;
@property (nonatomic, strong) UILabel *goldLabel;
@property (nonatomic, strong) UIButton *wenhaoBtn;
@property (nonatomic, strong) UILabel *descLabel;
@property (nonatomic, strong) UIButton *btn;

@property (nonatomic, strong) NSDictionary *dic;

@end

@implementation NewUserGifBagCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = FMClearColor;
    self.contentView.backgroundColor = FMClearColor;
    
    UIImageView *iconImgV = [[UIImageView alloc] init];
    [self.contentView addSubview:iconImgV];
    [iconImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(0);
        make.left.equalTo(0);
        make.width.height.equalTo(60);
    }];
    self.iconImgV = iconImgV;
    
    UIStackView *sv = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentLeading distribution:UIStackViewDistributionFill spacing:3 arrangedSubviews:nil];
    [self.contentView addSubview:sv];
    [sv mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(iconImgV.mas_right).offset(7.5);
        make.centerY.equalTo(0);
    }];
    
    UILabel *nameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(15) textColor:ColorWithHex(0x333333) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [sv addArrangedSubview:nameLabel];
    self.nameLabel = nameLabel;
    
    UIImageView *goldImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"xyhlb_gold3")];
    [self.contentView addSubview:goldImgV];
    [goldImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(nameLabel.mas_right).offset(10);
        make.centerY.equalTo(nameLabel);
        make.width.height.equalTo(15);
    }];
    self.goldImgV = goldImgV;
    
    UILabel *goldLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:ColorWithHex(0xff7200) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:goldLabel];
    [goldLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(goldImgV.mas_right).offset(2);
        make.centerY.equalTo(goldImgV);
    }];
    self.goldLabel = goldLabel;
    
    UIButton *wenhaoBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:FMWhiteColor backgroundColor:FMClearColor title:nil image:ImageWithName(@"xyhlb_wenhao") target:self action:@selector(wenhaoBtnClicked)];
    [self.contentView addSubview:wenhaoBtn];
    [wenhaoBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(goldLabel.mas_right).offset(5);
        make.centerY.equalTo(goldLabel);
    }];
    self.wenhaoBtn = wenhaoBtn;
    wenhaoBtn.lz_touchAreaInsets = UIEdgeInsetsMake(0, -100, 0, 0);
    
    UILabel *descLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [sv addArrangedSubview:descLabel];
    self.descLabel = descLabel;
    
    UIButton *btn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(14) normalTextColor:FMWhiteColor backgroundColor:FMClearColor title:@"去完成" image:nil target:self action:@selector(btnClicked)];
    [btn setTitle:@"已完成" forState:UIControlStateDisabled];
    [btn setBackgroundImage:[UIImage imageWithColor:[UIColor lz_gradientColors:@[ColorWithHex(0xec0807), ColorWithHex(0xfc6700)] withFrame:CGRectMake(0, 0, 65, 30) direction:GradientDirectionLeftToRight] andSize:CGSizeMake(65, 30)] forState:UIControlStateNormal];
    [btn setBackgroundImage:[UIImage imageWithColor:[UIColor lz_gradientColors:@[ColorWithHex(0xfd9298), ColorWithHex(0xfebb90)] withFrame:CGRectMake(0, 0, 65, 30) direction:GradientDirectionLeftToRight] andSize:CGSizeMake(65, 30)] forState:UIControlStateDisabled];
    [self.contentView addSubview:btn];
    [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(0);
        make.right.equalTo(-8);
        make.width.equalTo(65);
        make.height.equalTo(30);
        make.left.equalTo(sv.mas_right).offset(10);
    }];
    UI_View_Radius(btn, 15);
    self.btn = btn;
}

- (void)setDic:(NSDictionary *)dic {
    _dic = dic;
    
    self.iconImgV.image = ImageWithName(dic[@"icon"]);
    self.nameLabel.text = dic[@"name"];
    if ([dic[@"awardValue"] integerValue] == 0) {
        self.goldImgV.hidden = self.goldLabel.hidden = YES;
        self.wenhaoBtn.hidden = YES;
    } else {
        self.goldImgV.hidden = self.goldLabel.hidden = NO;
        self.wenhaoBtn.hidden = YES;
        NSInteger type =[dic[@"awardType"] integerValue];
        if (type == 1) { // 积分
            self.goldImgV.image = ImageWithName(@"xyhlb_gold3");
            self.goldLabel.text = [NSString stringWithFormat:@"%@", dic[@"awardValue"]];
        } else if (type == 2) { // 抽奖机会
            self.goldImgV.image = ImageWithName(@"xyhlb_cj");
            self.goldLabel.text = [NSString stringWithFormat:@"抽奖机会+%@", dic[@"awardValue"]];
            self.wenhaoBtn.hidden = NO;
        } else if (type == 3) {
            self.goldImgV.image = ImageWithName(@"xyhlb_kq");
            self.goldLabel.text = @"笔记券";
        } else if (type == 4) {
            self.goldImgV.image = ImageWithName(@"xyhlb_kq");
            self.goldLabel.text = @"私信券";
        } else if (type == 5) {
            self.goldImgV.image = ImageWithName(@"xyhlb_kq");
            self.goldLabel.text = @"问股券";
        }
    }
    self.descLabel.text = dic[@"desc"];
    self.btn.enabled = [dic[@"btnStatus"] boolValue];
}

- (void)btnClicked {
    void (^block)(void) = self.dic[@"btnBlock"];
    if (block) {
        block();
    }
}

- (void)wenhaoBtnClicked {
    [FMProgressHUD showTextOnlyInView:nil withText:@"获得抽奖机会后，可在\"福利中心-幸运大转盘\"参与抽奖"];
}

@end

@interface FMNewUserGiftBagPopView()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UILabel *goldLabel;
@property (nonatomic, strong) UILabel *reminderLabel;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSArray *datas;
@property (nonatomic, assign) BOOL shouldReappearAfterWebView;

@end

@implementation FMNewUserGiftBagPopView

+ (instancetype)sharedInstance {
    static FMNewUserGiftBagPopView *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] initWithFrame:CGRectZero];
    });
    return sharedInstance;
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT)]) {
        [self setUp];
        [self registerNotifications];
    }
    
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)registerNotifications {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleWebViewDismissed:)
                                                 name:kNormalWebVCDealloc
                                               object:nil];
}

- (void)setUp {
    CGFloat bgImgVBottom = 0;
    if (UI_SAFEAREA_BOTTOM_HEIGHT == 0) {
        bgImgVBottom = 50;
    }
    
    UIImageView *bgImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"xyhlb_mutitask_Bg")];
    [self addSubview:bgImgV];
    [bgImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(0);
        make.bottom.equalTo(bgImgVBottom);
        make.height.equalTo(UI_Relative_WidthValue(650));
    }];
    bgImgV.userInteractionEnabled = YES;
    
    {
        UIView *leftView = [UIView new];
        leftView.backgroundColor = ColorWithHex(0xe2faff);
        UI_View_BorderRadius(leftView, UI_Relative_WidthValue(8), UI_Relative_WidthValue(3), ColorWithHex(0xbcf5ff));
        [bgImgV addSubview:leftView];
        [leftView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(15));
            make.top.equalTo(UI_Relative_WidthValue(178));
            make.width.equalTo(UI_Relative_WidthValue(173.5));
            make.height.equalTo(UI_Relative_WidthValue(135.5));
        }];
        
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(UI_Relative_WidthValue(15)) textColor:FMZeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        [leftView addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(11.5));
            make.top.equalTo(UI_Relative_WidthValue(26));
        }];
        label.text = @"免费赠送家财险";
        
        UIImageView *hotImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"xyhlb_hot")];
        [leftView addSubview:hotImgV];
        [hotImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(label.mas_right).offset(UI_Relative_WidthValue(5));
            make.centerY.equalTo(label);
        }];
        
        UILabel *desLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:FMZeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        [leftView addSubview:desLabel];
        [desLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(label);
            make.top.equalTo(label.mas_bottom).offset(UI_Relative_WidthValue(8));
        }];
        desLabel.text = @"房屋损失可赔付，至多20万！";
        
        UIButton *btn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(14) normalTextColor:FMWhiteColor backgroundColor:[UIColor lz_gradientColors:@[ColorWithHex(0x42b4fc), ColorWithHex(0x10abd0)] withFrame:CGRectMake(0, 0, UI_Relative_WidthValue(85), UI_Relative_WidthValue(30)) direction:GradientDirectionLeftToRight] title:@"立即领取" image:nil target:self action:@selector(navigateToWebView)];
        [leftView addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(desLabel);
            make.top.equalTo(UI_Relative_WidthValue(81.5));
            make.width.equalTo(UI_Relative_WidthValue(85));
            make.height.equalTo(UI_Relative_WidthValue(30));
        }];
        UI_View_Radius(btn, UI_Relative_WidthValue(15));
        
        UIImageView *addImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"xyhlb_add")];
        [bgImgV addSubview:addImgV];
        [addImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(leftView.mas_right).offset(UI_Relative_WidthValue(4.5));
            make.centerY.equalTo(leftView).offset(-UI_Relative_WidthValue(5));
        }];
        
        UIImageView *baoImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"xyhlb_bao")];
        [leftView addSubview:baoImgV];
        [baoImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(-UI_Relative_WidthValue(12.5));
            make.bottom.equalTo(-UI_Relative_WidthValue(10));
            make.width.equalTo(UI_Relative_WidthValue(34.5));
            make.height.equalTo(UI_Relative_WidthValue(44.2));
        }];
    }
    
    {
        UIImageView *giftImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"xyhlb_gift")];
        [bgImgV addSubview:giftImgV];
        [giftImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(UI_Relative_WidthValue(100.5));
            make.top.equalTo(UI_Relative_WidthValue(205));
        }];
    }
    
    {
        UIImageView *goldImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"xyhlb_gold2")];
        
        UILabel *goldLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(UI_Relative_WidthValue(18)) textColor:ColorWithHex(0xe83f00) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        self.goldLabel = goldLabel;
        
        UILabel *desLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(UI_Relative_WidthValue(12)) textColor:ColorWithHex(0x707070) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        desLabel.text = @"已自动发放进您的钱包";
        
        UIStackView *stackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentCenter distribution:UIStackViewDistributionFill spacing:UI_Relative_WidthValue(10) arrangedSubviews:@[goldImgV, goldLabel, desLabel]];
        [bgImgV addSubview:stackView];
        [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(UI_Relative_WidthValue(100.5));
            make.top.equalTo(UI_Relative_WidthValue(205));
        }];
        
        [goldImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.equalTo(UI_Relative_WidthValue(21));
        }];
        
        [goldLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(UI_Relative_WidthValue(25));
        }];
        
        [stackView setCustomSpacing:UI_Relative_WidthValue(14.5) afterView:goldLabel];
    }
    
    UIButton *closeBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:ImageWithName(@"xyhlb_close") target:self action:@selector(dismiss)];
    [self addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(bgImgV.mas_top).offset(-10);
        make.right.equalTo(-15);
        make.width.height.equalTo(@35);
    }];
    
    UILabel *reminderLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:2];
    [bgImgV addSubview:reminderLabel];
    [reminderLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(UI_Relative_WidthValue(15));
        make.right.equalTo(-UI_Relative_WidthValue(15));
        make.top.equalTo(UI_Relative_WidthValue(325.5));
    }];
    self.reminderLabel = reminderLabel;
    
    UIView *whiteView = [UIView new];
    whiteView.backgroundColor = FMWhiteColor;
    UI_View_Radius(whiteView, 10);
    [bgImgV addSubview:whiteView];
    [whiteView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(reminderLabel);
        make.top.equalTo(reminderLabel.mas_bottom).equalTo(10);
        make.bottom.equalTo(-UI_SAFEAREA_BOTTOM_HEIGHT - bgImgVBottom);
    }];
    
    UILabel *grayLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [whiteView addSubview:grayLabel];
    [grayLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(15);
        make.right.equalTo(-15);
    }];
    grayLabel.text = @"可在“我的-福利中心”查看任务完成情况";
    
    UITableView *tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:nil];
    tableView.backgroundColor = FMWhiteColor;
    [tableView registerCellClass:[NewUserGifBagCell class]];
    [whiteView addSubview:tableView];
    [tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(grayLabel);
        make.right.equalTo(-8);
        make.top.equalTo(grayLabel.mas_bottom).offset(15);
        make.bottom.equalTo(-15);
    }];
    tableView.bounces = NO;
    
    self.tableView = tableView;
}

- (void)receiveNoteCoupon{
    [[FMPayTool payTool] checkOutRegistrFlagAndAnswerFlagResult:^{
    }];
}

- (void)judgeTaskStatusWithSuccessBlock:(void(^)(void))successBlock {
    [HttpRequestTool requestDailyTaskProgressInfoStart:^{
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            FMTaskConfigModel *userTaskProgress = [[FMTaskConfigModel alloc] init];
            NSArray *tasks = [NSArray modelArrayWithClass:[FMSubTask class] json:dic[@"data"][@"dailyTaskDtoList"]];
            for (FMSubTask *task in tasks) {
                userTaskProgress.taskDic[@(task.taskId)] = task;
            }
            [FMUserDefault setArchiverData:userTaskProgress forKey:UserTaskProgressCacheKey];
            
            NSMutableArray *mArr = [NSMutableArray array];
            FMSubTask *task = userTaskProgress.taskDic[@(FMTaskTypeFirstSign)];
            if (task.isEnable) {
                NSDictionary *dic = @{@"icon" : @"xyhlb_task_firstSign",
                                      @"name" : @"第一次签到",
                                      @"awardType" : @(task.awardType),
                                      @"awardValue" : @(task.awardValue),
                                      @"desc" : task.taskDesc,
                                      @"btnStatus" : @(task.completeNum < task.taskNum),
                                      @"btnBlock" : ^{
                                          [self dismiss];
                                          dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                                              [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                                                  [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://web?url=%@%@&title=%@", prefix.URLEncodedString, kAPI_UserCenter_FLZX.URLEncodedString, @"福利中心".URLEncodedString]];
                                              }];
                                          });
                                      }
                };
                [mArr addObject:dic];
            }
            task = userTaskProgress.taskDic[@(FMTaskTypeFollowFirstTeacher)];
            if (task.isEnable) {
                NSDictionary *dic = @{@"icon" : @"xyhlb_task_firstFollow",
                                      @"name" : @"关注一个老师",
                                      @"awardType" : @(task.awardType),
                                       @"awardValue" : @(task.awardValue),
                                      @"desc" : task.taskDesc,
                                      @"btnStatus" : @(task.completeNum < task.taskNum),
                                      @"btnBlock" : ^{
                                          [self dismiss];
                                          dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                                              [ProtocolJump jumpWithUrl:@"qcyzt://allBigcast"];
                                          });
                                      }
                };
                [mArr addObject:dic];
            }
            task = userTaskProgress.taskDic[@(FMTaskTypeBrowseHomepage)];
            if (task.isEnable) {
                NSDictionary *dic = @{@"icon" : @"xyhlb_task_browseHome",
                                      @"name" : @"首页浏览30s",
                                      @"awardType" : @(task.awardType),
                                       @"awardValue" : @(task.awardValue),
                                      @"desc" : task.taskDesc,
                                      @"btnStatus" : @(task.completeNum < task.taskNum),
                                      @"btnBlock" : ^{
                                          [self dismiss];
                                          dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                                              [ProtocolJump jumpWithUrl:@"qcyzt://home?browseTask=1"];
                                          });
                                      }
                };
                [mArr addObject:dic];
            }
            task = userTaskProgress.taskDic[@(FMTaskTypeInfoRegistration)];
            if (task.isEnable) {
                NSDictionary *dic = @{@"icon" : @"xyhlb_task_infoRegistration",
                                      @"name" : @"信息登记",
                                      @"awardType" : @(task.awardType),
                                       @"awardValue" : @(task.awardValue),
                                      @"desc" : task.taskDesc,
                                      @"btnStatus" : @(task.completeNum < task.taskNum),
                                      @"btnBlock" : ^{
                                          [self dismiss];
                                          dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                                              [ProtocolJump jumpWithUrl:@"qcyzt://investorInfo"];
                                          });
                                      }
                };
                [mArr addObject:dic];
            }
            task = userTaskProgress.taskDic[@(FMTaskTypeRiskEvaluation)];
            if (task.isEnable) {
                NSDictionary *dic = @{@"icon" : @"xyhlb_task_riskEvaluation",
                                      @"name" : @"风险测评",
                                      @"awardType" : @(task.awardType),
                                       @"awardValue" : @(task.awardValue),
                                      @"desc" : task.taskDesc,
                                      @"btnStatus" : @(task.completeNum < task.taskNum),
                                      @"btnBlock" : ^{
                                          [self dismiss];
                                          dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                                              [ProtocolJump jumpWithUrl:@"qcyzt://riskeValuation"];
                                          });
                                      }
                };
                [mArr addObject:dic];
            }
            task = userTaskProgress.taskDic[@(FMTaskTypeNoteCoupon)];
            if (task.isEnable) {
                NSDictionary *dic = @{@"icon" : @"xyhlb_task_notecoupon",
                                      @"name" : @"解锁一篇笔记",
                                      @"awardType" : @(task.awardType),
                                       @"awardValue" : @(task.awardValue),
                                      @"desc" : task.taskDesc,
                                      @"btnStatus" : @(YES),
                                      @"btnBlock" : ^{
                                          [self dismiss];
                                          [self receiveNoteCoupon];
                                      }
                };
                [mArr addObject:dic];
            }
            
            self.datas = mArr.copy;
            [self.tableView reloadData];
            
            NSString *str = @"完成下列 新手任务 可额外获得更多 积分/卡券 奖励（可用于付费内容的消费）";
            NSString *pattern = @"新手任务|积分/卡券";
            NSAttributedString *attrStr = [str attrStrWithMatchColor:ColorWithHex(0xfff50b) pattern:pattern textFont:FontWithSize(14)];
            self.reminderLabel.attributedText = attrStr;
            
            self.goldLabel.text = [NSString stringWithFormat:@"%zd积分", userTaskProgress.taskDic[@(FMTaskTypeRegister)].awardValue];
            
            if (successBlock) {
                successBlock();
            }
        }
    }];
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.datas.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NewUserGifBagCell *cell = [tableView reuseCellClass:[NewUserGifBagCell class]];
    cell.dic = self.datas[indexPath.section];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 60;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return 20;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (void)dismiss{
    [[FMPopWindowManager shareManager] dismiss:self];
}

- (void)show {
    [self judgeTaskStatusWithSuccessBlock:^{
        [[FMPopWindowManager shareManager].mutableArr addObject:self];
        [[FMPopWindowManager shareManager] show];
    }];
}

- (void)navigateToWebView {
    FMNewUserGiftBagPopView *popView = [FMNewUserGiftBagPopView sharedInstance];
    popView.shouldReappearAfterWebView = YES;
    [popView dismiss];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        
        YTGNormalWebVC *webVC = [[YTGNormalWebVC alloc] init];
        webVC.startPage = @"https://bd.wts99.cn/m/rwdb-jcb.html?djc=1";
        [[FMHelper getCurrentVC].navigationController pushViewController:webVC animated:YES];
    });
}

- (void)handleWebViewDismissed:(NSNotification *)notification {
    FMNewUserGiftBagPopView *popView = [FMNewUserGiftBagPopView sharedInstance];
    if (popView.shouldReappearAfterWebView) {
        popView.shouldReappearAfterWebView = NO;
        
        if (![[FMPopWindowManager shareManager].mutableArr containsObject:popView]) {
            [[FMPopWindowManager shareManager].mutableArr addObject:popView];
        }
        popView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
        [popView show];
    }
}

@end
