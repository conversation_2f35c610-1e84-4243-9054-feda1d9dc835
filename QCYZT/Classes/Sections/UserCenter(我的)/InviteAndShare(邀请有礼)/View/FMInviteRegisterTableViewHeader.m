//
//  FMInviteRegisterTableViewHeader.m
//  QCYZT
//
//  Created by zeng on 2022/5/24.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMInviteRegisterTableViewHeader.h"
#import "NSString+characterJudge.h"
#import "FMTaskConfigModel.h"
#import "FMMakeInvitationViewController.h"

@interface FMInviteRegisterTableViewHeader()<UITextFieldDelegate>

@property (nonatomic, strong) UILabel *taskAwardLabel;
@property (nonatomic, strong) UIButton *jumpBtn;
@property (nonatomic, strong) UILabel *registerReminderLabel;
@property (nonatomic, strong) UILabel *writeReminderLabel;
@property (nonatomic, strong) UITextField *textField;
@property (nonatomic, strong) UIButton *commitBtn;

@end

@implementation FMInviteRegisterTableViewHeader

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setUp];
    }
    
    return self;
}

- (void)setUp {
    self.backgroundColor = ColorWithHex(0xffbc64);
    
    UIImage *img = ImageWithName(@"invite_bg");
    UIImageView *bgImgV = [[UIImageView alloc] initWithImage:img];
    [self addSubview:bgImgV];
    [bgImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.right.equalTo(@0);
        make.height.equalTo(@(UI_Relative_WidthValue(img.size.height)));
    }];
    bgImgV.userInteractionEnabled = YES;
    
    UILabel *taskAwardLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(UI_Relative_WidthValue(27)) textColor:ColorWithHex(0xe83f00) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [bgImgV addSubview:taskAwardLabel];
    [taskAwardLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(UI_Relative_WidthValue(144)));
        make.top.equalTo(@(UI_Relative_WidthValue(257.5)));
        make.height.equalTo(@(UI_Relative_WidthValue(37.5)));
    }];
    self.taskAwardLabel = taskAwardLabel;
    
    UIButton *btn = [[UIButton alloc] init];
    [btn setBackgroundImage:ImageWithName(@"invite_btn") forState:UIControlStateNormal];
    [bgImgV addSubview:btn];
    [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(@(UI_Relative_WidthValue(347)));
        make.width.equalTo(@(UI_Relative_WidthValue(322.5)));
        make.height.equalTo(@(UI_Relative_WidthValue(72.5)));
    }];
    [btn addTarget:self action:@selector(jumpInvite) forControlEvents:UIControlEventTouchUpInside];
    self.jumpBtn = btn;
    
    UILabel *registerReminderLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(UI_Relative_WidthValue(14)) textColor:ColorWithHex(0xffeeea) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [bgImgV addSubview:registerReminderLabel];
    [registerReminderLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(@(UI_Relative_WidthValue(417.5)));
    }];
    self.registerReminderLabel = registerReminderLabel;
    
    UIView *whiteView = [[UIView alloc] init];
    [self addSubview:whiteView];
    [whiteView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(bgImgV.mas_bottom).offset(UI_Relative_WidthValue(10));
        make.width.equalTo(@(UI_Relative_WidthValue(345)));
        make.height.equalTo(@(UI_Relative_WidthValue(105)));
    }];
    whiteView.backgroundColor = FMWhiteColor;
    UI_View_Radius(whiteView, UI_Relative_WidthValue(10));
    
    UILabel *writeReminderLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(UI_Relative_WidthValue(15)) textColor:ColorWithHex(0x772600) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [whiteView addSubview:writeReminderLabel];
    [writeReminderLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(@(UI_Relative_WidthValue(15)));
    }];
    self.writeReminderLabel = writeReminderLabel;
    
    [whiteView addSubview:self.textField];
    [self.textField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.top.equalTo(@(UI_Relative_WidthValue(47)));
        make.height.equalTo(@(UI_Relative_WidthValue(40)));
    }];
}

- (void)judgeUserStatus {
    FMTaskConfigModel *taskConfig = [FMUserDefault getUnArchiverDataForKey:AppInit_Task];
    self.taskAwardLabel.text = self.inviteInfo[@"reward"];

    self.registerReminderLabel.text = [NSString stringWithFormat:@"好友注册可得%zd积分", taskConfig.taskDic[@(FMTaskTypeRegister)].awardValue];
    
    NSString *str = [NSString stringWithFormat:@"填写好友的邀请码，TA立刻获得%@", self.inviteInfo[@"reward"]];
    self.writeReminderLabel.attributedText = [str attrStrWithMatchColor:FMNavColor pattern:@"邀请码" textFont:FontWithSize(UI_Relative_WidthValue(15))];
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    [self btnClicked];
    
    return YES;
}

- (void)jumpInvite {
    FMMakeInvitationViewController *vc = [[FMMakeInvitationViewController alloc] init];
    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
}

- (void)btnClicked {
    NSString *str = [self.textField.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    
    if (!str.length) {
        [SVProgressHUD showInfoWithStatus:@"请输入邀请码"];
        return;
    }
    
    if (![str isPureNumberAndAlphabet]) {
        [SVProgressHUD showInfoWithStatus:@"邀请码格式不正确"];
        return;
    }
    
    [self.textField resignFirstResponder];
    if (self.commitBlock) {
        self.commitBlock(self.textField.text);
    }
}

- (UITextField *)textField {
    if (!_textField) {
        _textField = [UITextField new];
        _textField.placeholder = @"请在此处填写邀请码";
        _textField.delegate = self;
        _textField.backgroundColor = ColorWithHex(0xf5f5f5);
        _textField.keyboardType = UIKeyboardTypeNamePhonePad;
        _textField.font = FontWithSize(UI_Relative_WidthValue(16));
        UIView *rightView = [[UIView alloc] initWithFrame:self.commitBtn.bounds];
        [rightView addSubview:self.commitBtn];
        _textField.rightView = rightView;;
        _textField.leftView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 10, UI_Relative_WidthValue(40))];
        _textField.leftViewMode = UITextFieldViewModeAlways;
        _textField.rightViewMode = UITextFieldViewModeAlways;
        _textField.layer.cornerRadius = UI_Relative_WidthValue(5.0);
        _textField.layer.masksToBounds = YES;
        _textField.returnKeyType = UIReturnKeyDone;
    }
    
    return _textField;
}

- (UIButton *)commitBtn {
    if (!_commitBtn) {
        _commitBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _commitBtn.frame = CGRectMake(0, 0, UI_Relative_WidthValue(70), UI_Relative_WidthValue(40));
        [_commitBtn setTitle:@"提交" forState:UIControlStateNormal];
        _commitBtn.titleLabel.font = FontWithSize(UI_Relative_WidthValue(15));
        [_commitBtn setTitleColor:FMWhiteColor forState:UIControlStateNormal];
        _commitBtn.backgroundColor = FMNavColor;
        [_commitBtn addTarget:self action:@selector(btnClicked) forControlEvents:UIControlEventTouchUpInside];
    }
    
    return _commitBtn;
}

- (void)setInviteInfo:(NSDictionary *)inviteInfo {
    _inviteInfo = inviteInfo;
    
    [self judgeUserStatus];
}

@end
