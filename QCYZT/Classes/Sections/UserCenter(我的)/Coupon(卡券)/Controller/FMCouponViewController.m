//
//  FMCouponViewController.m
//  QCYZT
//
//  Created by Mr.文 on 2017/9/13.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "FMCouponViewController.h"
#import "FMCouponListViewController.h"
#import "FMWKWebViewController.h"

@interface FMCouponViewController ()<SGPageTitleViewDelegate,SGPageContentCollectionViewDelegate>

@property (nonatomic, strong) NSArray *titleArray;
@property (nonatomic, strong) SGPageTitleView *pageTitleView;
@property (nonatomic, strong) SGPageContentCollectionView *pageContentCollectionView;

@end

@implementation FMCouponViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.fm_F7F7F7_2E2F33;;

    self.title = @"我的卡券";
    self.titleArray = @[@"推荐使用", @"已使用", @"已过期"];
    
    [self setupPageView];
    if (self.index) {
        self.pageTitleView.selectedIndex = self.index;
        [self.pageContentCollectionView setPageContentCollectionViewCurrentIndex:self.index];
    }
    
    UIButton *rightBarItem = [UIButton buttonWithType:UIButtonTypeCustom];
    [rightBarItem setTitle:@"使用须知" forState:UIControlStateNormal];
    [rightBarItem setTitleColor:UIColor.fm_market_nav_text_zeroColor forState:UIControlStateNormal];
    rightBarItem.titleLabel.font = FontWithSize(15);
    [rightBarItem addTarget:self action:@selector(rightBarItemClick) forControlEvents:UIControlEventTouchUpInside];
    self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:rightBarItem];;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self configNavWhiteColorWithCloseSEL:@selector(backArrowClicked)];
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)rightBarItemClick {
    FMWKWebViewController *webVC = [[FMWKWebViewController alloc] init];
    webVC.title = @"卡券使用须知";

    // 获取本地HTML文件路径
    NSString *htmlPath = [[NSBundle mainBundle] pathForResource:@"CouponExplain" ofType:@"html"];
    if (htmlPath) {
        // 创建file URL并设置给urlStr属性
        NSURL *fileURL = [NSURL fileURLWithPath:htmlPath];
        webVC.urlStr = fileURL.absoluteString;
    } else {
        NSLog(@"PointExplain.html文件未找到");
    }

    [self.navigationController pushViewController:webVC animated:YES];
}

#pragma mark - SGPageTitleViewDelegate
- (void)pageTitleView:(SGPageTitleView *)pageTitleView selectedIndex:(NSInteger)selectedIndex{
    [self.pageContentCollectionView setPageContentCollectionViewCurrentIndex:selectedIndex];
}

#pragma mark - SGPageContentCollectionViewDelegate
- (void)pageContentCollectionView:(SGPageContentCollectionView *)pageContentCollectionView progress:(CGFloat)progress originalIndex:(NSInteger)originalIndex targetIndex:(NSInteger)targetIndex {
    [self.pageTitleView setPageTitleViewWithProgress:progress originalIndex:originalIndex targetIndex:targetIndex];
}

#pragma mark - Private
-(void)setupPageView {
    [self.pageTitleView removeFromSuperview];
    [self.pageContentCollectionView removeFromSuperview];
    self.pageTitleView = nil;
    self.pageContentCollectionView = nil;

    SGPageTitleViewConfigure *configure = [SGPageTitleViewConfigure pageTitleViewConfigure];
    configure.titleColor = UIColor.up_textSecondaryColor;
    configure.titleFont = FontWithSize(17.0);
    configure.titleSelectedColor = UIColor.up_textPrimaryColor;
    configure.titleSelectedFont = BoldFontWithSize(17.0);
    configure.indicatorStyle = SGIndicatorStyleFixed;
    configure.indicatorColor = FMNavColor;
    configure.indicatorFixedWidth = 18;
    configure.indicatorHeight = 3;
    configure.indicatorCornerRadius = 1.5;
    configure.titleAdditionalWidth = 30;
    configure.equivalence = YES;
    configure.showBottomSeparator = YES;
    configure.bottomSeparatorColor = UIColor.fm_sepline_color;
    self.pageTitleView = [SGPageTitleView pageTitleViewWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 45) delegate:self titleNames:self.titleArray configure:configure];
    self.pageTitleView.backgroundColor = UIColor.up_contentBgColor;
    [self.view addSubview:self.pageTitleView];
    self.pageContentCollectionView = [[SGPageContentCollectionView alloc] initWithFrame:CGRectMake(0, 45, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT-(UI_SAFEAREA_TOP_HEIGHT + 45 + UI_SAFEAREA_BOTTOM_HEIGHT)) parentVC:self childVCs:[self addChildVC]];
    self.pageContentCollectionView.delegatePageContentCollectionView = self;
    [self.view addSubview:self.pageContentCollectionView];
    // 处理侧滑返回失效
    [self.pageContentCollectionView.collectionView.panGestureRecognizer requireGestureRecognizerToFail:self.navigationController.interactivePopGestureRecognizer];
}

- (NSArray *)addChildVC {
    for (NSInteger i = 0; i < self.titleArray.count; i++) {
        FMCouponListViewController *vc = [[FMCouponListViewController alloc] init];
        vc.status = i + 1;
        [self addChildViewController:vc];
    }
    
    return self.childViewControllers;
}



@end
