//
//  FMUserCenterTopCell.m
//  QCYZT
//
//  Created by zeng on 2021/10/12.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMUserCenterTopCell.h"
#import "FMMeInfoVC.h"
#import "FMMeMyTrackViewController.h"
#import "FMMyCollectionViewController.h"
#import "FMMeInfoVC.h"
#import "FMMyFocusViewController.h"
#import "FMBigcastPrivateLetterOrderListViewController.h"
#import "FMMarketImgTextTopRightBtn.h"
#import "FMProgressHUD.h"

@interface FMUserCenterTopCell()

/// 头像
@property (nonatomic, strong) UIImageView *iconImgV;
/// 认证图标
@property (nonatomic, strong) UIImageView *approveIcon;
/// 昵称
@property (nonatomic, strong) UILabel *nameLabel;
/// 认证描述
@property (nonatomic, strong) UIButton *approveBtn;
/// 编辑
@property (nonatomic, strong) UIButton *editBtn;
/// 提示
@property (nonatomic, strong) UILabel *reminderLabel;
/// 账号
@property (nonatomic, strong) YYLabel *accountNumLabel;
/// 底部4个按钮那行的stackView
@property (nonatomic, strong) UIStackView *bottomStackView;
/// 私信按钮
@property (nonatomic, strong) FMMarketImgTextTopRightBtn *privateLetterBtn;

@end

@implementation FMUserCenterTopCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    
    return self;
}

- (void)setUp {
    self.backgroundColor = FMClearColor;
    self.contentView.backgroundColor = FMClearColor;
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    UIImageView *iconImgV = [[UIImageView alloc] init];
    [self.contentView addSubview:iconImgV];
    [iconImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(@10);
        make.width.height.equalTo(@60);
    }];
    UI_View_Radius(iconImgV, 30);
    iconImgV.contentMode = UIViewContentModeScaleAspectFill;
    iconImgV.userInteractionEnabled = YES;
    iconImgV.backgroundColor = FMWhiteColor;
    [iconImgV addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(jumpToUserInfo)]];
    self.iconImgV = iconImgV;
    
    UIImageView *approveIcon = [[UIImageView alloc] init];
    approveIcon.hidden = YES;
    [self.contentView addSubview:approveIcon];
    [approveIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.bottom.equalTo(iconImgV).offset(2);
        make.size.equalTo(@(CGSizeMake(20, 20)));
    }];
    self.approveIcon = approveIcon;
    
    // 昵称、描述、账号
    UIStackView *stackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentLeading distribution:UIStackViewDistributionEqualSpacing spacing:3 arrangedSubviews:nil];
    [self.contentView addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(iconImgV.mas_right).offset(10);
        make.right.equalTo(@-15);
        make.centerY.equalTo(iconImgV);
    }];
    
    // 昵称那行的stackView
    UIStackView *topStackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisHorizontal alignment:UIStackViewAlignmentCenter distribution:UIStackViewDistributionEqualSpacing spacing:9 arrangedSubviews:nil];
    [stackView addArrangedSubview:topStackView];
    [topStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@28);
    }];
    topStackView.userInteractionEnabled = YES;
    [topStackView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(jumpToUserInfo)]];
    
    UILabel *nameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(20) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [topStackView addArrangedSubview:nameLabel];
    [nameLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    self.nameLabel = nameLabel;
    
    UIButton *approveBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [approveBtn setImage:ImageWithName(@"usercenter_approve_icon") forState:UIControlStateNormal];
    approveBtn.titleLabel.font = [FMHelper scaleFont:12.0];
    [approveBtn setTitleColor:ColorWithHex(0x9a5800) forState:UIControlStateNormal];
    [topStackView addArrangedSubview:approveBtn];
    [approveBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@87);
        make.height.equalTo(@24);
    }];
    UI_View_Radius(approveBtn, 12);
    approveBtn.userInteractionEnabled = NO;
    self.approveBtn = approveBtn;
    
    UIView *gradientView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 87, 24)];
    [approveBtn insertSubview:gradientView belowSubview:approveBtn.imageView];
    NSArray *colors =  @[(__bridge  id)ColorWithHex(0xFFF4E8).CGColor,(__bridge  id)ColorWithHex(0xFFDBAF).CGColor];
    [gradientView drawCAGradientWithcolors:colors startPoint:CGPointMake(0.5, 0) endPoint:CGPointMake(0.5, 1)];
    
    UIButton *editBtn = [[UIButton alloc] init];
    [editBtn setImage:FMImgInBundle(@"我的/信息编辑") forState:UIControlStateNormal];
    [topStackView addArrangedSubview:editBtn];
    editBtn.userInteractionEnabled = NO;
    self.editBtn = editBtn;
    
    UILabel *reminderLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:13.0] textColor:UIColor.fm_userCenter_welcomeTextColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [stackView addArrangedSubview:reminderLabel];
    self.reminderLabel = reminderLabel;
    
    YYLabel *accountNumLabel = [[YYLabel alloc] init];
    accountNumLabel.font = [FMHelper scaleFont:13.0];
    accountNumLabel.numberOfLines = 0;
    accountNumLabel.textAlignment = NSTextAlignmentLeft;
    [stackView addArrangedSubview:accountNumLabel];
    accountNumLabel.userInteractionEnabled = YES;
    [accountNumLabel addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithActionBlock:^(UITapGestureRecognizer *sender) {
        UIPasteboard.generalPasteboard.string = [FMUserDefault getUserId];
        [FMProgressHUD showTextOnlyInView:nil withText:@"账号已复制"];
    }]];
    self.accountNumLabel = accountNumLabel;
    
    UIStackView *bottomStackView = [[UIStackView alloc] init];
    bottomStackView.axis = UILayoutConstraintAxisHorizontal;
    bottomStackView.distribution = UIStackViewDistributionEqualSpacing;
    bottomStackView.spacing = 0;
    [self.contentView addSubview:bottomStackView];
    [bottomStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(0));
        if ([FMHelper isBigFont]) {
            make.top.equalTo(reminderLabel.mas_bottom).offset(10);
        } else {
            make.top.equalTo(iconImgV.mas_bottom).offset(15);
        }
        make.height.equalTo(@(45));
        make.bottom.equalTo(self.contentView);
    }];
    self.bottomStackView = bottomStackView;
    
    NSArray *arr = @[@"0\n创作",@"0\n关注",@"0\n粉丝",@""];
    CGFloat stackHeight = [FMHelper isBigFont] ? 50 : 45;
    for (NSInteger i = 0; i < arr.count; i ++) {
        if (i == arr.count - 1) {
            FMMarketImgTextTopRightBtn *btn = [[FMMarketImgTextTopRightBtn alloc] initWithImgWidth:18 imgHeight:15 imgToText:6 textFont:[FMHelper scaleFont:12.0] textColor:UIColor.up_textSecondary1Color];
            [btn addTarget:self action:@selector(privateLetterBtnClick) forControlEvents:UIControlEventTouchUpInside];
            [bottomStackView addArrangedSubview:btn];
            [btn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.size.equalTo(@(CGSizeMake(UI_SCREEN_WIDTH / 4, stackHeight)));
            }];
            self.privateLetterBtn = btn;
        } else {
            UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(20) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:2 textAlignment:NSTextAlignmentCenter];
            label.userInteractionEnabled = YES;
            label.tag = 1000 + i;
            NSMutableAttributedString *attr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@",arr[i]]];
            [attr addAttributes:@{NSFontAttributeName:[FMHelper scaleFont:12],NSForegroundColorAttributeName:UIColor.up_textSecondary1Color} range:NSMakeRange(attr.length - 2, 2)];
            label.attributedText = attr;
            [bottomStackView addArrangedSubview:label];
            [label mas_makeConstraints:^(MASConstraintMaker *make) {
                make.size.equalTo(@(CGSizeMake(UI_SCREEN_WIDTH / 4, stackHeight)));
            }];
            UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithActionBlock:^(UITapGestureRecognizer *sender) {
                [self itemOption:sender];
            }];
            [label addGestureRecognizer:tap];
            
            UIView *line = [[UIView alloc] init];
            line.backgroundColor = UIColor.up_dividerColor;
            [label addSubview:line];
            [line mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.centerY.equalTo(@(0));
                make.size.equalTo(@(CGSizeMake(0.5, 25)));
            }];
            
        }
    }
}

// 私信或者编辑
- (void)privateLetterBtnClick {
    if ([self.privateLetterBtn.textLabel.text isEqualToString:@"私信"]) {
        FMBigcastPrivateLetterOrderListViewController *vc = [[FMBigcastPrivateLetterOrderListViewController alloc] init];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    } else {
        FMMeInfoVC *vc = [[FMMeInfoVC alloc] init];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }
}

- (void)itemOption:(UITapGestureRecognizer *)sender {
    switch (sender.view.tag - 1000) {
        case 0:
        {
            // 创作
            [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://bigname?id=%@", [FMUserDefault getUserId]]];
        }
            break;
        case 1:
        {
            // 关注
            FMMyFocusViewController *vc = [[FMMyFocusViewController alloc] init];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }
            break;
        case 2:
        {
            // 粉丝
        }
            break;
        default:
            break;
    }
}

- (void)setData {
    if ([FMHelper isLogined]) {
        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
        self.bottomStackView.hidden =  userModel.userFlag == 1 ? NO : YES;
        CGFloat stackHeight = [FMHelper isBigFont] ? 50 : 45;
        [self.bottomStackView mas_updateConstraints:^(MASConstraintMaker *make) {
            if (userModel.userFlag == 1) {
                make.height.equalTo(@(stackHeight));
            } else {
                make.height.equalTo(@(CGFLOAT_MIN));
            }
        }];

        
        if (userModel.userName.length) {
            [self.iconImgV sd_setImageWithURL:[NSURL URLWithString:userModel.userIco] placeholderImage:[UIImage imageNamed:@"userCenter_dltx"]];

            self.nameLabel.text = userModel.userName;
            
            if (userModel.attestationType > 0) {
                self.approveBtn.hidden = NO;
                self.approveIcon.hidden = NO;

                [self.approveBtn setTitle:userModel.attestationTitle forState:UIControlStateNormal];
                [self.approveBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:2];
                if (userModel.attestationType == 1) {
                    self.approveIcon.image = ImageWithName(@"MemberCenter_AttestationTypeColumn");
                } else if (userModel.attestationType == 2) {
                    self.approveIcon.image = ImageWithName(@"MemberCenter_AttestationTypeConsultant");
                } else {
                    self.approveIcon.image = ImageWithName(@"MemberCenter_AttestationTypeOrganization");
                }
            } else {
                self.approveBtn.hidden = YES;
                self.approveIcon.hidden = YES;
            }
            
            self.editBtn.hidden = NO;
            
            FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
            if (userModel.vip) {
                self.reminderLabel.text = @"您好，尊贵的大决策VIP会员";
            } else {
                self.reminderLabel.text = @"您好，尊敬的大决策会员";
            }
            
            NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"账号：%@  ", userModel.userId]];
            UIImage *copyImg = ImageWithName(@"复制");
            [attrStr insertAttributedString:[NSAttributedString yy_attachmentStringWithContent:copyImg contentMode:UIViewContentModeCenter attachmentSize:copyImg.size alignToFont:[FMHelper scaleFont:13.0] alignment:YYTextVerticalAlignmentCenter] atIndex:attrStr.length];
            attrStr.yy_color = UIColor.fm_userCenter_welcomeTextColor;
            self.accountNumLabel.attributedText = attrStr;
            self.accountNumLabel.hidden = NO;
            
            NSString *string;
            for (NSInteger i = 0; i < self.bottomStackView.subviews.count; i ++) {
                UILabel *label = self.bottomStackView.subviews[i];
                if (i == 0) {
                    if (userModel.writeNum >= 10000) {
                        string = [NSString stringWithFormat:@"%.1fW\n创作",(userModel.writeNum / 10000.0)];
                    } else {
                        string = [NSString stringWithFormat:@"%zd\n创作", userModel.writeNum];
                    }
                } else if (i == 1) {
                    if (userModel.noticeNum >= 10000) {
                        string = [NSString stringWithFormat:@"%.1fW\n关注",(userModel.noticeNum / 10000.0)];
                    } else {
                        string = [NSString stringWithFormat:@"%zd\n关注", userModel.noticeNum];
                    }
                } else if (i == 2) {
                    if (userModel.noticedUserNum >= 10000) {
                        string = [NSString stringWithFormat:@"%.1fW\n粉丝",(userModel.noticedUserNum / 10000.0)];
                    } else {
                        string = [NSString stringWithFormat:@"%zd\n粉丝", userModel.noticedUserNum];
                    }
                }
                if (i < 3) {
                    NSMutableAttributedString *attr = [[NSMutableAttributedString alloc] initWithString:string];
                    [attr addAttributes:@{NSFontAttributeName:[FMHelper scaleFont:12.0],NSForegroundColorAttributeName:ColorWithHex(0x888888)} range:NSMakeRange(attr.length - 2, 2)];
                    label.attributedText = attr;
                }
            }

            if (userModel.attestationType == 2 && [userModel.letterPrice floatValue] > 0) {
                self.privateLetterBtn.imgV.image = [UIImage imageWithTintColor:FMNavColor blendMode:kCGBlendModeDestinationIn WithImageObject:FMImgInBundle(@"我的/私信")];
                [self.privateLetterBtn.imgV mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.width.equalTo(@18);
                    make.height.equalTo(@15);
                }];
                self.privateLetterBtn.textLabel.text = @"私信";
                
                if (userModel.letterNum > 0) {
                    NSString *str = userModel.letterNum > 99 ? @"99+" : [NSString stringWithFormat:@"%zd", userModel.letterNum];
                    CGSize signSize = [str sizeWithAttributes:@{NSFontAttributeName : [FMHelper scaleFont:12.0]}];
                    CGSize tagImgSize = CGSizeMake(ceil(signSize.width) < 18 ? 18 : ceil(signSize.width) + 3, 18);
                    UIImage *bgImg = [[UIImage imageWithColor:ColorWithHex(0xfc7f00) andSize:tagImgSize] addTextWithImageSize:tagImgSize text:str textRect:CGRectMake((tagImgSize.width - ceil(signSize.width)) / 2 , (tagImgSize.height - ceil(signSize.height)) / 2, ceil(signSize.width), ceil(signSize.height)) textAttributes:@{NSFontAttributeName : [FMHelper scaleFont:12.0], NSForegroundColorAttributeName : FMWhiteColor}];
                    self.privateLetterBtn.signImgV.image = bgImg;
                    UI_View_Radius(self.privateLetterBtn.signImgV, 9);
                    self.privateLetterBtn.signImgV.hidden = NO;
                } else {
                    self.privateLetterBtn.signImgV.hidden = YES;
                }
            } else {
                self.privateLetterBtn.imgV.image = [UIImage imageWithTintColor:FMNavColor blendMode:kCGBlendModeDestinationIn WithImageObject:FMImgInBundle(@"我的/编辑资料")];
                [self.privateLetterBtn.imgV mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.width.equalTo(@16);
                    make.height.equalTo(@16);
                }];
                self.privateLetterBtn.textLabel.text = @"编辑资料";
                self.privateLetterBtn.signImgV.hidden = YES;
            }
        } else {
            self.approveBtn.hidden = YES;
            self.approveIcon.hidden = YES;
            self.editBtn.hidden = YES;
        }
    } else {
        NSString *string;
        for (NSInteger i = 0; i < self.bottomStackView.subviews.count; i ++) {
            UILabel *label = self.bottomStackView.subviews[i];
            if (i == 0) {
                string = [NSString stringWithFormat:@"0\n创作"];
            } else if (i == 1) {
                string = [NSString stringWithFormat:@"0\n关注"];
            } else if (i == 2) {
                string = [NSString stringWithFormat:@"0\n粉丝"];
            }
            if (i < 3) {
                NSMutableAttributedString *attr = [[NSMutableAttributedString alloc] initWithString:string];
                [attr addAttributes:@{NSFontAttributeName:[FMHelper scaleFont:12.0],NSForegroundColorAttributeName:ColorWithHex(0x888888)} range:NSMakeRange(attr.length - 2, 2)];
                label.attributedText = attr;
            }
        }
        
        self.bottomStackView.hidden = YES;
        [self.bottomStackView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(@(CGFLOAT_MIN));
        }];
        
        self.approveBtn.hidden = YES;
        self.approveIcon.hidden = YES;
        self.editBtn.hidden = YES;
        self.nameLabel.text = @"登录/注册";
        self.iconImgV.image = ImageWithName(@"userCenter_wdl");
        self.reminderLabel.text = @"享受更多投资特权";
        self.accountNumLabel.hidden = YES;
    }
}

- (void)jumpToUserInfo {
    if ([FMHelper checkLoginStatus]) {
        FMMeInfoVC *vc = [[FMMeInfoVC alloc] init];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    }
}

@end
