//
//  FMPaySuccessPopView.m
//  QCYZT
//
//  Created by zeng on 2023/12/28.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMPaySuccessPopView.h"

@interface FMPaySuccessPopView()

@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, strong) UILabel *minusLabel;

@end

@implementation FMPaySuccessPopView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT)]) {
        [self setUp];
    }
    
    return self;
}

- (void)setUp  {
    UIView *contentView = [[UIView alloc] init];
    [self addSubview:contentView];
    [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(0);
        make.centerY.equalTo(-25);
        make.width.equalTo(270);
    }];
    contentView.backgroundColor = FMWhiteColor;
    contentView.layer.cornerRadius = 10.0f;
    contentView.layer.masksToBounds = YES;
    
    UIImageView *imgV = [[UIImageView alloc] initWithImage:ImageWithName(@"paySuccess")];
    [contentView addSubview:imgV];
    [imgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(0);
        make.top.equalTo(25);
    }];
    
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(20) textColor:FMNavColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [contentView addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(0);
        make.top.equalTo(imgV.mas_bottom).offset(7.5);
    }];
    label.text = @"支付成功";
    
    [contentView addSubview:self.stackView];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.top.equalTo(label.mas_bottom).offset(15);
        make.bottom.equalTo(-25);
    }];
    
    [self.stackView addArrangedSubview:self.minusLabel];
    self.minusLabel.hidden = YES;
    
    UILabel *label2 = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [self.stackView addArrangedSubview:label2];
    NSString *str = @"可在“我的-已购服务”查看";
    label2.attributedText = [str attrStrWithMatchColor:ColorWithHex(0x0074FA) pattern:@"我的-已购服务" textFont:FontWithSize(13)];
    label2.userInteractionEnabled = YES;
    [label2 addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(jumpToPurchased)]];
    
    UIButton *closeBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:ImageWithName(@"quiz_close") target:self action:@selector(dismiss)];
    [self addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(0);
        make.top.equalTo(contentView.mas_bottom).offset(15);
    }];
}

- (void)jumpToPurchased {
    [self dismiss];
    
    [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://purchasedService?index=%zd", self.jumpIndex]];
}

- (void)dismiss{
    [[FMPopWindowManager shareManager] dismiss:self animation:NO];
}

- (void)show {
    [[FMPopWindowManager shareManager].mutableArr addObject:self];
    [[FMPopWindowManager shareManager] showWithAnimation:NO];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self dismiss];
    });
}

- (void)setMinusNum:(NSInteger)minusNum {
    _minusNum = minusNum;
    
    if (minusNum > 0) {
        self.minusLabel.hidden = NO;
        NSString *str = [NSString stringWithFormat:@"已为您随机立减%zd金币", minusNum];
        self.minusLabel.attributedText = [str attrStrWithMatchColor:FMNavColor pattern:@"\\d+" textFont:FontWithSize(15)];
    } else {
        self.minusLabel.hidden = YES;
    }
}

- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentCenter distribution:UIStackViewDistributionFill spacing:5 arrangedSubviews:nil];
    }
    
    return _stackView;
}

- (UILabel *)minusLabel {
    if (!_minusLabel) {
        _minusLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:ColorWithHex(0x333333) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    }
    
    return _minusLabel;
}

@end
