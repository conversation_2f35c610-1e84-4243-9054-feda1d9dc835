//
//  PaymentView.m
//  QCYZT
//
//  Created by Mr.文 on 2017/9/18.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "PaymentView.h"
#import "PayMethodCell.h"
#import "PayCouponCell.h"
#import "PayPointsCell.h"
#import "PayContentCell.h"
#import "FMPayMentCouponSelectVC.h"
#import "FMCouponTableModel.h"
#import "HttpRequestTool+UserCenter.h"

// MARK: - Constants
static const CGFloat kTopNavHeight = 50.0f;  // 顶部导航高度
static const CGFloat kPayCoinCellHeight = 90.0f; // 支付方式Cell高度（金币支付）
static const CGFloat kCellHeight = 53.0f; // 普通Cell高度
static const CGFloat kSectionSpacing = 8.0f; // section间距
static const CGFloat kTableFooterHeight = 60.0f; // tableFooter高度
static const CGFloat kSureBtnHeight = 45.0f; // 确定按钮高度
static const CGFloat kSureBtnBottomPadding = 15.0f; // 确定按钮底部padding

// MARK: - Button Tags
typedef NS_ENUM(NSInteger, PaymentButtonTag) {
    PaymentButtonTagRecharge = 1001,
    PaymentButtonTagPay = 1002
};

@interface PaymentView () <UITableViewDelegate, UITableViewDataSource>

// MARK: - UI Properties
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *tableFooterView;
@property (nonatomic, strong) UIButton *sureButton;                         // 确定按钮
@property (nonatomic, strong) ZLTagLabel *firstRechargeLabel;               // 首充优惠标签
@property (nonatomic, strong) UILabel *footerLB;

// MARK: - Cell Properties
@property (nonatomic, strong) PayContentCell *payContentCell;               // 付费内容cell
@property (nonatomic, strong) PayCouponCell *payCouponCell;                 // 优惠券选择cell
@property (nonatomic, strong) PayPointsCell *payPointsCell;                 // 积分选择cell

// MARK: - Data Properties
@property (nonatomic, strong) NSArray<EnablePayModel *> *enablePayList;     // 所有可选的支付方式
@property (nonatomic, strong) EnablePayModel *selectedModel;                // 被选中的支付方式
@property (nonatomic, strong) NSArray<FMCouponTableModel *> *availableCoupons; // 可用卡券列表
@property (nonatomic, strong) FMCouponTableModel *selectedCoupon;           // 选择的卡券
@property (nonatomic, assign) NSInteger userPoints;                         // 用户积分数
@property (nonatomic, assign) NSInteger pointsRatio;                        // 积分与金币兑换比例

// MARK: - Configuration Properties
@property (nonatomic, copy) NSString *price; // 商品价格
@property (nonatomic, copy) NSString *productName; // 商品名
@property (nonatomic, copy) NSString *bottomReminder; // 底部提示

// MARK: - Callback Properties
@property (nonatomic, copy) void(^sureAction)(EnablePayModel *selectedModel); // 支付确定回调
@property (nonatomic, copy) void(^dismissBlock)();                            // 点击退出按钮回调

@end

@implementation PaymentView

// MARK: - Factory Methods

/// 创建并显示支付视图（多个支付方式）
+ (instancetype)showWithEnablePayList:(NSArray *)enablePayList 
                             payPrice:(NSString *)price 
                       productName:(NSString *)productName 
                       bottomReminder:(NSString *)bottomReminder 
                            payAction:(void(^)(EnablePayModel *selectedModel))sureAction 
                         dismissBlock:(void(^)())dismissBlock {
    PaymentView *payView = [[self alloc] initWithEnablePayList:enablePayList 
                                                      payPrice:price 
                                                productName:productName 
                                                bottomReminder:bottomReminder 
                                                     payAction:sureAction 
                                                  dismissBlock:dismissBlock];
    [payView show];
    return payView;
}

/// 创建并显示支付视图（单个支付方式）
+ (instancetype)showWithEnablePayModel:(EnablePayModel *)payModel 
                              payPrice:(NSString *)price 
                        productName:(NSString *)productName 
                        bottomReminder:(NSString *)bottomReminder 
                             payAction:(void (^)(EnablePayModel *))sureAction 
                          dismissBlock:(void (^)())dismissBlock {
    return [self showWithEnablePayList:@[payModel] 
                              payPrice:price 
                        productName:productName 
                        bottomReminder:bottomReminder 
                             payAction:sureAction 
                          dismissBlock:dismissBlock];
}

// MARK: - Initialization

- (instancetype)initWithEnablePayList:(NSArray *)enablePayList 
                             payPrice:(NSString *)price 
                       productName:(NSString *)productName 
                       bottomReminder:(NSString *)bottomReminder 
                            payAction:(void(^)(EnablePayModel *selectedModel))sureAction 
                         dismissBlock:(void(^)())dismissBlock {
    if (self = [super initWithFrame:[UIScreen mainScreen].bounds]) {
        [self configureWithPayList:enablePayList 
                             price:price 
                    productName:productName 
                    bottomReminder:bottomReminder 
                         payAction:sureAction 
                      dismissBlock:dismissBlock];
        [self setupViews];
    }
    return self;
}

/// 配置支付视图的基本属性
- (void)configureWithPayList:(NSArray *)enablePayList 
                       price:(NSString *)price 
              productName:(NSString *)productName 
              bottomReminder:(NSString *)bottomReminder 
                   payAction:(void(^)(EnablePayModel *selectedModel))sureAction 
                dismissBlock:(void(^)())dismissBlock {
    self.enablePayList = enablePayList;
    self.selectedModel = enablePayList.firstObject;
    self.selectedModel.choosed = YES;
    self.sureAction = sureAction;
    self.dismissBlock = dismissBlock;
    self.price = price;
    self.productName = productName;
    self.bottomReminder = bottomReminder;
    self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.7f];
}

// MARK: - UI Setup

/// 设置视图层次结构和布局
- (void)setupViews {
    [self addSubview:self.contentView];
    self.contentView.frame = [self contentViewRect];
    
    [self setupNavigationBar];
    [self setupSureButton];
    [self setupTableView];
    [self setupFirstRechargeLabel];
    
    [self requestCoupon];
}

/// 设置导航栏（标题和关闭按钮）
- (void)setupNavigationBar {
    UIView *topView = [[UIView alloc] init];
    [self.contentView addSubview:topView];
    [topView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(0);
        make.height.equalTo(kTopNavHeight);
    }];
    
    // 关闭按钮
    UIButton *closeButton = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:FMImgInBundle(@"导航/亮黑暗灰关闭") target:self action:@selector(dismiss)];
    [topView addSubview:closeButton];
    [closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(0);
        make.right.equalTo(0);
        make.width.height.equalTo(44);
    }];
    
    // 标题标签
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(18) textColor:UIColor.fm_market_nav_text_zeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    titleLabel.text = @"确认支付";
    [topView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(0);
    }];
    
    // 分割线
    [topView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.right.bottom.equalTo(0);
        make.height.equalTo(0.5);
    }].backgroundColor = UIColor.fm_sepline_color;
}

/// 设置确定按钮
- (void)setupSureButton {
    UIButton *sureButton = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(18) normalTextColor:FMWhiteColor backgroundColor:UIColor.up_riseColor title:@"确定" image:nil target:self action:@selector(sureClick:)];
    [self.contentView addSubview:sureButton];
    [sureButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.bottom.equalTo(-15 - UI_SAFEAREA_BOTTOM_HEIGHT);
        make.height.equalTo(45);
    }];
    UI_View_Radius(sureButton, 22.5);
    self.sureButton = sureButton;
}

/// 设置表格视图
- (void)setupTableView {
    self.tableView.frame = CGRectMake(0, kTopNavHeight, UI_SCREEN_WIDTH, self.contentView.height - kTopNavHeight - kSureBtnHeight - kSureBtnBottomPadding - UI_SAFEAREA_BOTTOM_HEIGHT);
    [self.contentView addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(0);
        make.top.equalTo(kTopNavHeight);
        make.bottom.equalTo(self.sureButton.mas_top);
    }];

    // 设置表尾
    if (self.bottomReminder.length > 0) {
        self.tableView.tableFooterView = self.tableFooterView;
        self.footerLB.text = self.bottomReminder;
    } else {
        self.tableView.tableFooterView = [UIView new];
    }
}

/// 设置首充优惠标签
- (void)setupFirstRechargeLabel {
    self.firstRechargeLabel = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:ColorWithHex(0x6a0000) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    self.firstRechargeLabel.widthPadding = 20;
    [self.contentView addSubview:self.firstRechargeLabel];
    [self.firstRechargeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.sureButton);
        make.top.equalTo(self.sureButton.mas_top).offset(-12.5);
        make.height.equalTo(25);
    }];
    self.firstRechargeLabel.hidden = YES;
}

// MARK: - Layout & Animation
- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.contentView layerAndBezierPathWithRect:self.contentView.bounds cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight];
    
    // 更新首充优惠标签的外观（渐变背景和圆角）
    if (!self.firstRechargeLabel.hidden) {
        self.firstRechargeLabel.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xF5CE62), ColorWithHex(0xFFE59B)]
                                                                   withFrame:self.firstRechargeLabel.bounds
                                                                   direction:GradientDirectionLeftToRight];
        
        [self.firstRechargeLabel layerAndBezierPathWithRect:self.firstRechargeLabel.bounds
                                                cornerRadii:CGSizeMake(12.5, 12.5)
                                          byRoundingCorners:UIRectCornerTopLeft|UIRectCornerTopRight|UIRectCornerBottomRight];
    }
}

/// 显示支付视图（从底部滑入动画）
- (void)show {
    [[FMAppDelegate shareApp].window addSubview:self];
    self.backgroundColor = [UIColor colorWithWhite:0 alpha:0];
    self.contentView.top = UI_SCREEN_HEIGHT;
    
    [UIView animateWithDuration:0.3 animations:^{
        self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.7f];
        self.contentView.bottom = UI_SCREEN_HEIGHT;
    }];
}

/// 关闭支付视图
- (void)dismiss {
    if (self.dismissBlock) {
        self.dismissBlock();
    }
    [self hideWithAnimation];
}

/// 隐藏支付视图（滑出动画）
- (void)hideWithAnimation {
    [UIView animateWithDuration:0.3 animations:^{
        self.backgroundColor = [UIColor colorWithWhite:0 alpha:0];
        self.contentView.top = UI_SCREEN_HEIGHT;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

// MARK: - Business Logic
/// 更新确定按钮状态（根据余额判断是否需要充值）
- (void)updateSureButtonState {
    BOOL needsRecharge = [self shouldShowRechargeButton];
    
    // 更新按钮文本和标签
    [self.sureButton setTitle:needsRecharge ? @"余额不足，请先充值" : @"确定" forState:UIControlStateNormal];
    self.sureButton.backgroundColor = UIColor.up_riseColor;
    self.sureButton.tag = needsRecharge ? PaymentButtonTagRecharge : PaymentButtonTagPay;
    
    // 更新首充优惠标签显示状态
    [self updateFirstRechargeLabelVisibility:needsRecharge];
}

/// 判断是否需要显示充值按钮
- (BOOL)shouldShowRechargeButton {
    if (self.selectedModel.type == PaymentTypeWechat) { // 微信支付不需要显示充值
        return NO;
    }
    
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    if ([self actualPayPrice] <= userModel.coin || [self actualPayPrice] == 0) {
        return NO;
    }
    
    return YES;
}

/// 更新首充优惠标签的显示状态
- (void)updateFirstRechargeLabelVisibility:(BOOL)needsRecharge {
    // 判断是否应该显示首充优惠标签
    BOOL shouldShowFirstRecharge = NO;
    if (needsRecharge) {
        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
        NSString *firstRechargeText = [FMUserDefault getSeting:AppInit_FirstRecharge_Text];
        shouldShowFirstRecharge = (userModel.isShowFirstDesc && firstRechargeText.length > 0);
    }

    if (shouldShowFirstRecharge) {
        NSString *firstRechargeText = [FMUserDefault getSeting:AppInit_FirstRecharge_Text];

        self.firstRechargeLabel.hidden = NO;
        self.firstRechargeLabel.text = firstRechargeText;

        // 延迟更新布局以确保动画效果
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self setNeedsLayout];
            [self layoutIfNeeded];
        });
    } else {
        self.firstRechargeLabel.hidden = YES;
    }
}

// 显示支付方式cell的数量
- (NSInteger)payMethodCellCount {
    if (self.enablePayList.lastObject.type == PaymentTypeWechat) {
        return [self actualPayPrice] > 0 ? self.enablePayList.count: self.enablePayList.count - 1;
    }
    
    return self.enablePayList.count;
}

// 实际需要支付价格
- (CGFloat)actualPayPrice {
    CGFloat price = self.price.floatValue;
    if (self.selectedModel.freeDesc.length) {
        price = 0;
    }
    if (self.selectedModel.couponId.length) { // 如果选择了优惠券
        if (self.selectedModel.type == PaymentTypeSpecialCoupon) { // 专用券，直接抵扣全部
            price = 0;
        } else {
            price -= self.selectedModel.couponDiscount; // 减去优惠券折扣
        }
    }
    if (self.selectedModel.usePoints) { // 如果使用积分
        price -= self.selectedModel.usePoints * self.pointsRatio; // 减去积分折扣
    }
    price = MAX(price, 0); // 确保价格不为负数
    return price;
}

// 计算ContentView的rect
- (CGRect)contentViewRect {
    CGFloat payMethodCellsHeight = 0;
    for (NSInteger i = 0; i < [self payMethodCellCount]; i++) {
        EnablePayModel *model = self.enablePayList[i];
        if (model.type == PaymentTypeWechat) {
            payMethodCellsHeight += kCellHeight;
        } else {
            payMethodCellsHeight += kPayCoinCellHeight;
        }
    }
    
    // 计算优惠券+积分区域高度（微信支付时只有优惠券cell）
    CGFloat couponAndPointsHeight = self.selectedModel.type == PaymentTypeWechat ? kCellHeight : (kCellHeight * 2);

    // 高度计算：导航栏 + PayContentCell + section间距 + (PayCouponCell + PayPointsCell) + section间距 + 支付方式cells + footer + 按钮区域
    CGFloat contentHeight = MIN(kTopNavHeight + kCellHeight + kSectionSpacing + couponAndPointsHeight + kSectionSpacing + payMethodCellsHeight + kTableFooterHeight + kSureBtnHeight + kSureBtnBottomPadding + UI_SAFEAREA_BOTTOM_HEIGHT, UI_SCREEN_HEIGHT * 0.9);
    
    return CGRectMake(0, UI_SCREEN_HEIGHT - contentHeight, UI_SCREEN_WIDTH, contentHeight);
}

// MARK: - User Actions
/// 确定按钮点击事件处理
- (void)sureClick:(UIButton *)sender {
    [self hideWithAnimation];
    
    switch (sender.tag) {
        case PaymentButtonTagRecharge:
            [self handleRechargeAction];
            break;
        case PaymentButtonTagPay:
            [self handlePaymentAction];
            break;
        default:
            break;
    }
}

/// 处理充值操作
- (void)handleRechargeAction {
    [ProtocolJump jumpWithUrl:@"qcyzt://recharge"];
}

/// 处理支付操作
- (void)handlePaymentAction {
    if (self.sureAction) {
        self.sureAction(self.selectedModel);
    }
}

// MARK: - Network Requests
/// 请求优惠券列表数据
- (void)requestCoupon {
    [HttpRequestTool requestPaymentCouponListWithBignameId:self.selectedModel.bignameId 
                                               consumeType:self.selectedModel.consumeType 
                                                 contentId:self.selectedModel.contentId 
                                                     start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD dismiss];
    } success:^(NSDictionary *dic) {
        [SVProgressHUD dismiss];
        [self handleCouponRequestSuccess:dic];
    }];
}

// MARK: - Coupon Management
/// 处理优惠券请求成功的响应
- (void)handleCouponRequestSuccess:(NSDictionary *)responseData {
    if (![responseData[@"status"] isEqualToString:@"1"]) {
        return;
    }
    
    self.availableCoupons = [NSArray modelArrayWithClass:[FMCouponTableModel class] json:responseData[@"data"]];
    
    // 自动选择可用的专用券
    [self autoSelectAvailableSpecialCoupon];
    
    // 更新UI显示
    [self updateCouponDisplayState];
    [self.payPointsCell updateOrderAmount:self.price.integerValue couponDiscount:[self calcCouponDiscountValue]]; // 更新积分cell;
    [self.tableView reloadData];
    
    // 延迟回调优惠券信息获取完成
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (self.getCouponInfo) {
            self.getCouponInfo();
        }
    });
}

/// 自动选择可用的专用券
- (void)autoSelectAvailableSpecialCoupon {
    for (FMCouponTableModel *model in self.availableCoupons) {
        if ([self isSpecialCouponAvailable:model]) {
            self.selectedCoupon = model;
            break;
        }
    }
}

/// 判断专用券是否可用
- (BOOL)isSpecialCouponAvailable:(FMCouponTableModel *)coupon {
    // 必须是可用的专用券
    if (!coupon.isEnable || coupon.goodsType.integerValue != PaymentTypeSpecialCoupon) {
        return NO;
    }
    
    // 内容ID必须匹配
    if (coupon.consumeContentid.integerValue != self.selectedModel.contentId) {
        return NO;
    }
    
    // 消费类型必须匹配，直播或笔记才行
    return (coupon.consumeType == 5 || coupon.consumeType == 1);
}

/// 更新优惠券显示状态和UI
- (void)updateCouponDisplayState {
    // 更新业务逻辑
    if (self.selectedCoupon) {
        [self configureSelectedCouponDisplay];
    } else {
        [self configureUnselectedCouponDisplay];
    }

    // 直接更新优惠券cell实例变量的显示状态
    [self.payCouponCell configureWithSelectedCoupon:self.selectedCoupon
                                   availableCoupons:self.availableCoupons
                                           tapBlock:^{
        [self handleCouponCellTap];
    }];
}

/// 处理优惠券cell点击事件
- (void)handleCouponCellTap {
    self.hidden = YES;
    FMPayMentCouponSelectVC *vc = [[FMPayMentCouponSelectVC alloc] init];
    vc.selectedModel = self.selectedCoupon;
    vc.dataArray = self.availableCoupons;
    vc.closePage = ^{
        self.hidden = NO;
    };
    WEAKSELF;
    vc.couponSelectBlock = ^(FMCouponTableModel *selectedCoupon) {
        __weakSelf.hidden = NO;
        __weakSelf.selectedCoupon = selectedCoupon;
        [__weakSelf updateCouponDisplayState];
        [__weakSelf updateSureButtonState];
        [__weakSelf.payPointsCell updateOrderAmount:__weakSelf.price.integerValue couponDiscount:[__weakSelf calcCouponDiscountValue]]; // 更新积分cell
        [__weakSelf.tableView reloadData];
        __weakSelf.contentView.frame = [__weakSelf contentViewRect];

        if (selectedCoupon) {
            if (__weakSelf.couponSelected) {
                __weakSelf.couponSelected();
            }
        }
    };
    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
}

/// 配置已选择优惠券的显示状态（保留业务逻辑）
- (void)configureSelectedCouponDisplay {
    // 更新支付方式的优惠券ID
    [self updatePaymentMethodsCouponId:self.selectedCoupon.couponId];

    // 更新支付类型（除微信支付外）
    if (self.selectedModel.type != PaymentTypeWechat) {
        self.selectedModel.type = self.selectedCoupon.goodsType.integerValue;
    }
}

/// 配置未选择优惠券的显示状态（保留业务逻辑）
- (void)configureUnselectedCouponDisplay {
    // 清空优惠券相关信息
    [self updatePaymentMethodsCouponId:@""];

    // 重置支付类型为余额支付（除微信支付外）
    if (self.selectedModel.type != PaymentTypeWechat) {
        self.selectedModel.type = PaymentTypeCoin;
    }
}

/// 更新所有支付方式的优惠券ID
- (void)updatePaymentMethodsCouponId:(NSString *)couponId {
    [self.enablePayList enumerateObjectsUsingBlock:^(EnablePayModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.couponId = couponId;
        obj.couponDiscount = [self calcCouponDiscountValue];
    }];
}

/// 计算优惠券抵扣值
- (NSInteger)calcCouponDiscountValue {
    CGFloat couponDiscount = 0;
    if (self.selectedCoupon) {
        if (self.selectedCoupon.goodsType.integerValue == 3) {
            couponDiscount = MIN(self.price.integerValue, self.selectedCoupon.value);
        } else if (self.selectedCoupon.goodsType.integerValue == 4) {
            couponDiscount = self.price.integerValue;
        }
    }
    return (NSInteger)couponDiscount;
}

- (CGRect)couponBgViewCoverWindowFrame {
    // 直接使用优惠券cell实例变量
    if (self.payCouponCell) {
        CGRect couponBgFrame = [self.payCouponCell couponBgViewFrame];
        CGRect labelFrameInWindow = [self.payCouponCell convertRect:couponBgFrame toView:[FMAppDelegate shareApp].window];
        return labelFrameInWindow;
    }
    return CGRectZero;
}

- (CGRect)headerViewCoverWindowFrame {
    // 直接使用付费内容cell实例变量
    if (self.payContentCell) {
        CGRect contentFrame = [self.tableView convertRect:self.payContentCell.frame toView:[FMAppDelegate shareApp].window];
        // 包含PayContentCell、优惠券cell、积分cell和第一个支付方式cell的区域
        CGFloat totalHeight = kCellHeight + kSectionSpacing + (kCellHeight * 2) + kSectionSpacing + kPayCoinCellHeight;
        CGRect labelFrameInWindow = CGRectMake(contentFrame.origin.x, contentFrame.origin.y, contentFrame.size.width, totalHeight);
        return labelFrameInWindow;
    }
    return CGRectZero;
}

#pragma mark - UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 3; // 付费内容、优惠券+积分、支付方式
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section == 0) {
        return 1; // 付费内容
    } else if (section == 1) {
        // 如果选择微信支付，只显示优惠券cell，隐藏积分cell
        if (self.selectedModel.type == PaymentTypeWechat) {
            return 1; // 只有优惠券
        } else {
            return 2; // 优惠券 + 积分
        }
    } else {
        return [self payMethodCellCount]; // 支付方式
    }
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        // 返回付费内容cell实例变量
        return self.payContentCell;
    } else if (indexPath.section == 1) {
        if (indexPath.row == 0) {
            // 返回优惠券选择cell实例变量
            return self.payCouponCell;
        } else {
            // 返回积分选择cell实例变量（仅在非微信支付时）
            return self.payPointsCell;
        }
    } else {
        PayMethodCell *cell = [tableView reuseCellClass:[PayMethodCell class]];
        EnablePayModel *model = self.enablePayList[indexPath.row];
        if (model.type == PaymentTypeWechat) { // 微信支付不抵扣积分
            CGFloat price = [self actualPayPrice];
            if (self.selectedModel.usePoints) {
                price += self.selectedModel.usePoints * self.pointsRatio;
            }
            cell.price = [NSString stringWithFormat:@"%.0f", price];
        } else {
            cell.price = [NSString stringWithFormat:@"%.0f", [self actualPayPrice]];
        }
        cell.originalPrice = self.price;
        cell.model = model;
        WEAKSELF
        cell.gotoRechargeBlock = ^{
            [__weakSelf hideWithAnimation];
            [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://web?url=%@%@&title=%@", prefix.URLEncodedString, kAPI_UserCenter_FLZX.URLEncodedString, @"福利中心".URLEncodedString]];
            }];
        };
        return cell;
    }
}

#pragma mark - UITableViewDelegate
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0 || indexPath.section == 1) {
        return kCellHeight;
    } else {
        if (indexPath.row < self.enablePayList.count) {
            EnablePayModel *model = self.enablePayList[indexPath.row];
            if (model.type == PaymentTypeWechat) {
                return kCellHeight;
            }
        }
        return kPayCoinCellHeight; // 选择通用券、专用券支付时都还是显示金币支付Cell
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 2) {
        // 支付方式选择
        EnablePayModel *previousModel = self.selectedModel;
        self.selectedModel.choosed = NO;
        if (indexPath.row < self.enablePayList.count) {
            self.selectedModel = self.enablePayList[indexPath.row];
            self.selectedModel.choosed = YES;

            // 如果支付方式从微信切换到其他方式，或从其他方式切换到微信，需要重新计算布局
            BOOL needsLayoutUpdate = (previousModel.type == PaymentTypeWechat) != (self.selectedModel.type == PaymentTypeWechat);

            [self.tableView reloadData];

            // 更新支付按钮状态
            [self updateSureButtonState];

            // 如果需要更新布局（微信支付切换时积分cell显示/隐藏）
            if (needsLayoutUpdate) {
                self.contentView.frame = [self contentViewRect];
            }
        }
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (section == 0) {
        return CGFLOAT_MIN;
    }
    return kSectionSpacing;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (section == 0) {
        return nil;
    }
    UIView *headerView = [[UIView alloc] init];
    headerView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    return headerView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return nil;
}

#pragma mark - Points Methods
/// 处理积分选择状态变化
- (void)handlePointsSelectionChanged:(NSInteger)usePoints {
    // 更新支付方式的积分使用
    [self.enablePayList enumerateObjectsUsingBlock:^(EnablePayModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.usePoints = usePoints;
    }];

    // 更新支付按钮状态
    [self updateSureButtonState];

    [self.tableView reloadData];
    self.contentView.frame = [self contentViewRect];
}

#pragma mark - Setter/Getter
- (UIView *)contentView {
    if (_contentView == nil) {
        _contentView = [[UIView alloc] init];
        _contentView.backgroundColor = UIColor.up_contentBgColor;
    }
    return _contentView;
}

- (UITableView *)tableView {
    if (_tableView == nil) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        [_tableView registerCellClass:[PayMethodCell class]];
        _tableView.bounces = NO;
        _tableView.allowsSelection = YES;
        _tableView.tableFooterView = [UIView new];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.backgroundColor = UIColor.up_contentBgColor;
    }
    return _tableView;
}

- (UIView *)tableFooterView {
    if (!_tableFooterView) {
        _tableFooterView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, kTableFooterHeight)];
        
        UILabel *footerLB = [[UILabel alloc] initWithFrame:CGRectMake(15, 0, UI_SCREEN_WIDTH - 30, 60) font:FontWithSize(12) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentCenter];
        self.footerLB = footerLB;
        
        [_tableFooterView addSubview:self.footerLB];
    }
    return _tableFooterView;
}

- (PayContentCell *)payContentCell {
    if (!_payContentCell) {
        _payContentCell = [[PayContentCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:nil];
        [_payContentCell configureWithProductName:self.productName];
    }
    return _payContentCell;
}

- (PayCouponCell *)payCouponCell {
    if (!_payCouponCell) {
        _payCouponCell = [[PayCouponCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:nil];
        WEAKSELF;
        [_payCouponCell configureWithSelectedCoupon:self.selectedCoupon
                                   availableCoupons:self.availableCoupons
                                           tapBlock:^{
            [__weakSelf handleCouponCellTap];
        }];
    }
    return _payCouponCell;
}

- (PayPointsCell *)payPointsCell {
    if (!_payPointsCell) {
        _payPointsCell = [[PayPointsCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:nil];
        
        // 获取用户积分和配置
        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
        self.userPoints = userModel.points;
        self.pointsRatio = 1; // 默认1:1，后续可从配置获取
        WEAKSELF;
        [_payPointsCell configureWithFreeDesc:self.selectedModel.freeDesc
                                    userPoints:self.userPoints
                                  orderAmount:self.price.integerValue
                               couponDiscount:0
                                  pointsRatio:self.pointsRatio
                        selectionChangedBlock:^(NSInteger usePoints) {
            [__weakSelf handlePointsSelectionChanged:usePoints];
        }];
    }
    return _payPointsCell;
}



@end
