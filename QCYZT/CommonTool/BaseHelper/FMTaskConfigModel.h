//
//  FMTaskConfigModel.h
//  QCYZT
//
//  Created by zeng on 2022/5/9.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, FMTaskType) {
    FMTaskTypeSign = 1, // 签到任务
    FMTaskTypeLaxin, // 拉新任务
    FMTaskTypeComment, // 评论任务
    FMTaskTypeShare, // 分享任务
    FMTaskTypeWatchLive, // 观看直播任务
    FMTaskTypeReadNote, // 阅读笔记任务
    FMTaskTypeRegister, // 注册任务
    FMTaskTypeInfoRegistration, // 信息登记任务
    FMTaskTypeRiskEvaluation, // 风险评估任务
    FMTaskTypePublishNote, // 发布笔记任务
    FMTaskTypeBrowseHomepage, // 浏览首页任务
    FMTaskTypeFollowFirstTeacher, // 关注第一个老师任务
    FMTaskTypeFirstSign, // 第一次签到任务
    FMTaskTypeNoteCoupon, // 笔记券任务
};


@interface FMSubTask : NSObject

@property (nonatomic , assign) NSInteger              taskId;
@property (nonatomic , assign) long long              updateTime;
@property (nonatomic , assign) long long              createTime;
@property (nonatomic , assign) NSInteger              isEnable;
//@property (nonatomic , assign)   NSInteger              taskAward;  // 任务奖励
@property (nonatomic , copy)   NSString              * taskDesc;    // 任务描述
@property (nonatomic , assign) NSInteger              taskNeedNum;  // 单个任务需求数量：例如阅读三篇笔记,此处即为3
@property (nonatomic , assign) NSInteger              taskNum;      // 任务每日可重复数量：例如阅读三篇笔记为一次,一天五次,此处即为5  数值为-1代表无限次数
@property (nonatomic , assign) NSInteger              taskRequire;  // 任务需求信息：例如阅读笔记三篇笔记,每篇15秒.此处即为15
@property (nonatomic , assign) NSInteger              taskRound;    // 任务周期：例如阅读笔记,每天刷新次数,次数即为1
@property (nonatomic , assign) NSInteger              taskType;     // 任务类型：1表示周期任务(例如签到 七天为一周期,阅读笔记 一天为一周期)； 2表示次数任务(例如拉新 没有周期,无限次数)
@property (nonatomic , assign) NSInteger              maxAward;     // 最大奖励：例如拉新 30天最多可获得1000  此处即为1000
@property (nonatomic , assign) NSInteger              maxAwardRound;     // 最大奖励周期：例如拉新 30天最多可获得1000  此处即为30
@property (nonatomic , assign) NSInteger              completeNum;  // 任务完成数量
@property (nonatomic , assign) NSInteger              completeTaskNeedNum; // 单个任务完成数量,例如阅读三篇笔记算完成一次,此处返回数值为三篇笔记阅读了多少篇
@property (nonatomic , assign) NSInteger              completeTaskNum; // 任务完成数量,例如阅读三篇笔记完成了一次,一天可以完成三次,次数即为三次里面已完成的次数

@property (nonatomic, assign) NSInteger awardType; // 奖励类型1积分、2抽奖机会、3笔记券、4私信券、5问股券
@property (nonatomic, assign) NSInteger awardValue; // 奖励数值（积分数卡券id抽奖次数）

@end

@interface FMTaskConfigModel : NSObject

@property (nonatomic, strong) NSMutableDictionary<NSNumber *, FMSubTask *> *taskDic;

@end

NS_ASSUME_NONNULL_END
