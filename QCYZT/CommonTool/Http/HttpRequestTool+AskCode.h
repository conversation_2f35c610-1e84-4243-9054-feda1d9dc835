//
//  HttpRequestTool+AskCode.h
//  QCYZT
//
//  Created by macPro on 2019/2/20.
//  Copyright © 2019 LZKJ. All rights reserved.
//

#import "HttpRequestTool.h"

NS_ASSUME_NONNULL_BEGIN

/// 问股评分 意见反馈
#define kAPI_Question_FeedBack                   KDakaBaseUrl(@"/api/v2/questionInterface/feedback.do")

@interface HttpRequestTool (AskCode)


#pragma mark 问股 2.0

/// 问股列表
+(void)getAskCodeListWithPage:(NSInteger)page
                     pageSize:(NSInteger)pageSize
                     listType:(NSInteger)listType
                     keyWords:(NSString *)keyWords
                        start:(void (^)())startBlock
                      failure:(void (^)())failBlock
                      success:(requestSuccessBlock)success;

/// 投顾主页问股列表
/// - Parameters:
///   - page: 页码
///   - pageSize: 分页size
///   - dakaId: 投顾id
///   - startBlock: 开始回调
///   - failBlock: 失败回调
///   - success: 成功回调
+ (void)getDakaAskCodeListWithPage:(NSInteger)page
                          pageSize:(NSInteger)pageSize
                            dakaId:(NSString *)dakaId
                             start:(void (^)())startBlock
                           failure:(void (^)())failBlock
                           success:(requestSuccessBlock)success;

/// 热门问股数据
+ (void)getAskCodeRecommonDataWithstart:(void (^)())startBlock
                                failure:(void (^)())failBlock
                                success:(requestSuccessBlock)success;

/// 问股收藏列表
+(void)getAskCodeCollectListWithQuestionIdPage:(NSInteger)page
                                      pageSize:(NSInteger)pageSize
                                         start:(void (^)())startBlock
                                       failure:(void (^)())failBlock
                                       success:(requestSuccessBlock)success;


// 问股详情
+(void)getQuestionDetailWithWithQuestionId:(NSString *)noteId
                                     start:(void (^)())startBlock
                                   failure:(void (^)())failBlock
                                   success:(requestSuccessBlock)success;


// 问股收藏
+(void)questionCollectWithQuestionId:(NSString *)noteId
                               start:(void (^)())startBlock
                             failure:(void (^)())failBlock
                             success:(requestSuccessBlock)success;


// 取消问股收藏
+(void)cancelQuestionCollectWithQuestionId:(NSString *)noteId
                                     start:(void (^)())startBlock
                                   failure:(void (^)())failBlock
                                   success:(requestSuccessBlock)succes;

// 问股超值
+(void)questionPraiseWithQuestionId:(NSString *)questionid
                              start:(void (^)())startBlock
                            failure:(void (^)())failBlock
                            success:(requestSuccessBlock)success;

// 查询排队时间
+(void)questionCheckWaitTimeWithAnswerId:(NSString *)answerId
                                   start:(void (^)())startBlock
                                 failure:(void (^)())failBlock
                                 success:(requestSuccessBlock)success;

// 提问
+(void)questionAskWithAnswerId:(NSString *)answerId
                       content:(NSString *)content
                          type:(NSString *)type
                       goodsId:(NSString *)goodsId
                     stockCode:(NSString *)stockCode
                     stockName:(NSString *)stockName
                     usePoints:(NSInteger)usePoints
                         start:(void (^)())startBlock
                       failure:(void (^)())failBlock
                       success:(requestSuccessBlock)success;

// 回答
+(void)questionAnswerWithQuestionid:(NSString *)questionid
                         timeLength:(NSString *)timeLength
                             ossUrl:(NSString *)url
                           isPublic:(NSString *)isPublic
                              start:(void (^)())startBlock
                            failure:(void (^)())failBlock
                            success:(requestSuccessBlock)success;

// 自选股是否有问股数据
+(void)getSelfStocksQuestionCountWithKeyWord:(NSString *)keyWord
                                       start:(void (^)())startBlock
                                     failure:(void (^)())failBlock
                                     success:(requestSuccessBlock)success;

// 我的问股数量
+ (void)getMyQuestionCountWithStart:(void (^)())startBlock
                            failure:(void (^)())failBlock
                            success:(requestSuccessBlock)success;

// 问我的数量
+ (void)getAskMeCountWithStart:(void (^)())startBlock
                            failure:(void (^)())failBlock
                       success:(requestSuccessBlock)success;

// 未回答数量
+ (void)getNotAnswerCountWithStart:(void (^)())startBlock
                            failure:(void (^)())failBlock
                           success:(requestSuccessBlock)success;

// 问股评分 意见反馈
+ (void)askCodeFeedBackWithQuestionId:(NSInteger)questionId
                            StarLevel:(NSInteger)starLevel
                            feedback:(NSString *)feedback
                                start:(void (^)())startBlock
                              failure:(void (^)())failBlock
                              success:(requestSuccessBlock)success;

@end

NS_ASSUME_NONNULL_END
