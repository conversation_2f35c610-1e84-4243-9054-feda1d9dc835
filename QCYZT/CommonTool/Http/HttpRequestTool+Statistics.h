//
//  HttpRequestTool+Statistics.h
//  QCYZT
//
//  Created by Cursor on 2023-05-10
//  Copyright © 2023 Cursor. All rights reserved.
//

#import "HttpRequestTool.h"



@class StatisticsFunctionItemModel;

@interface HttpRequestTool (Statistics)

/**
 * 功能使用上报
 *
 * @param items 功能使用上报项数组，数组元素为StatisticsFunctionItemModel或包含functionId和nums字段的NSDictionary
 * @param startBlock 请求开始回调
 * @param failBlock 请求失败回调
 * @param success 请求成功回调
 */
+ (void)requestStatisticsFunctionUsageWithItems:(NSArray *)items
                                         start:(void (^)(void))startBlock
                                       failure:(void (^)(void))failBlock
                                       success:(requestSuccessBlock)success;

/**
 * 会员中心访问上报
 *
 * @param functionId 功能ID
 * @param startBlock 请求开始回调
 * @param failBlock 请求失败回调
 * @param success 请求成功回调
 */
+ (void)requestMemberCenterVisitWithFunctionId:(NSInteger)functionId
                                        start:(void (^)(void))startBlock
                                      failure:(void (^)(void))failBlock
                                      success:(requestSuccessBlock)success;

@end

