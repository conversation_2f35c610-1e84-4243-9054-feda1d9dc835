//
//  HttpRequestTool+UserCenter.m
//  QCYZT
//
//  Created by shum<PERSON> on 2022/6/26.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "HttpRequestTool+UserCenter.h"

#define kAPI_User_Detail             KDakaBaseUrl(@"/api/v2/user/detail")                            // 个人信息
#define kAPI_User_SyncData             KDakaBaseUrl(@"/api/v2/user/syncData")                            // 同步数据
#define kAPI_User_QueryCoinAndPoints KDakaBaseUrl(@"/api/v2/user/queryAccount")               // 查询金币和积分
#define kAPI_User_QueryConsume       KDakaBaseUrl(@"/api/v2/user/queryConsume")                      // 查询消费记录
#define kAPI_User_QueryIncome        KDakaBaseUrl(@"/api/v2/user/queryIncome")                       // 查询收入

#define kAPI_UserCenter_Visit                      KDakaBaseUrl(@"/api/v2/user/visitRecord")          // 经常访问
#define kAPI_PrivateLetter_BigcastHome             KDakaBaseUrl(@"/api/v2/letter/index.do")          // 私信投顾主页
#define kAPI_PrivateLetter_Send                    KDakaBaseUrl(@"/api/v2/letter/sendMsg.do")          // 发送私信
#define kAPI_PrivateLetter_List                    KDakaBaseUrl(@"/api/v2/letter/message.do")          // 私信历史消息
#define kAPI_PrivateLetter_RefreshRead             KDakaBaseUrl(@"/api/v2/letter/read.do")          // 私信更新已读
#define kAPI_Daka_PrivateLetter_RecordList         KDakaBaseUrl(@"/api/v2/letter/dk/record/list.do")          // 投顾私信聊天记录
#define kAPI_Daka_PrivateLetter_OrderList          KDakaBaseUrl(@"/api/v2/letter/dk/order/list.do")           // 投顾私信订单列表
#define kAPI_Daka_PrivateLetter_Stop               KDakaBaseUrl(@"/api/v2/letter/dk/stop.do")                  // 投顾结束私信
#define kAPI_Daka_PrivateLetter_Send               KDakaBaseUrl(@"/api/v2/letter/dk/sendMsg.do")          // 投顾私信发送消息
#define KAPI_Coupon_List                           KDakaBaseUrl(@"/api/v2/userGoods/list.do")                      // 我的优惠券
#define KAPI_Payment_Coupon_List                   KDakaBaseUrl(@"/api/v2/userGoods/available/list.do")                      // 支付方式卡券列表

// 获取赠送的卡券
#define kAPIGetCoupon                   KDakaBaseUrl(@"/api/v2/dailyTask/giveGoods.do")

#define KAPI_UserCenter_AddFeedBack    KDakaBaseUrl(@"/api/stock/info/addFeedback.do")               // 提交反馈


@implementation HttpRequestTool (UserCenter)


// 个人信息
+ (void)getPersonalInfoStart:(void (^)())startBlock
                     failure:(void (^)())failBlock
                     success:(requestSuccessBlock)success{
    [self getDataInfoWithUrl:kAPI_User_Detail
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 个人数据同步
+ (void)userSyncDataWithStart:(void (^)())startBlock
                     failure:(void (^)())failBlock
                     success:(requestSuccessBlock)success{
    [self getDataInfoWithUrl:kAPI_User_SyncData
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 查询金币和积分
+ (void)queryDakaCoinsAndPointsStart:(void (^)())startBlock
                             failure:(void (^)())failBlock
                             success:(requestSuccessBlock)success{
    [self getDataInfoWithUrl:kAPI_User_QueryCoinAndPoints params:nil start:startBlock failure:failBlock success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            NSDictionary *data = dic[@"data"];
            NSInteger coinNum = [data[@"coinNum"] integerValue];
            FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
            userModel.coin = coinNum;
            NSInteger newPoints = [data[@"pointsNum"] integerValue];
            userModel.points = newPoints;

            [MyKeyChainManager save:kUserModel data:userModel];
        }
        
        if (success) {
            success(dic);
        }
    }];
}

// 查询消费记录
+ (void)queryConsumePage:(NSUInteger)page
                pageSize:(NSUInteger)pageSize
                   start:(void (^)())startBlock
                 failure:(void (^)())failBlock
                 success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_User_QueryConsume
                      params:@{@"page_no":[NSString stringWithFormat:@"%zd",page],
                               @"page_size":[NSString stringWithFormat:@"%zd",pageSize]}
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 查询收入记录
+ (void)queryIncomePage:(NSUInteger)page
               pageSize:(NSUInteger)pageSize
                  start:(void (^)())startBlock
                failure:(void (^)())failBlock
                success:(requestSuccessBlock)success{
    [self getDataInfoWithUrl:kAPI_User_QueryIncome
                      params:@{@"page_no":[NSString stringWithFormat:@"%zd",page],
                               @"page_size":[NSString stringWithFormat:@"%zd",pageSize]}
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 获取我的经常访问
+ (void)getUserCenterOffenVisitRequestStart:(void (^)())startBlock
                                    failure:(void (^)())failBlock
                                    success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_UserCenter_Visit params:nil start:startBlock failure:failBlock success:success];
}


#pragma mark - 客户私信
// 私信投顾主页
+ (void)requestPrivateLetterHomeWithBigcastId:(NSString *)bigcastId
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    if (bigcastId) {
        [param setObject:bigcastId forKey:@"bignameId"];
    }
    [self getDataInfoWithUrl:kAPI_PrivateLetter_BigcastHome params:param start:startBlock failure:failBlock success:success];
}



// 发送私信
+ (void)requestPrivateLetterSendWithBigcastId:(NSString *)bigcastId
                                      orderId:(NSString *)orderId
                                      content:(NSString *)content
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    if (bigcastId) {
        [param setObject:bigcastId forKey:@"bignameId"];
    }
    if (orderId.length) {
        [param setObject:orderId forKey:@"orderId"];
    }
    if (content) {
        [param setObject:content forKey:@"content"];
    }
    [self postDataJsonInfoWithUrl:kAPI_PrivateLetter_Send params:param start:startBlock failure:failBlock success:success];
}

// 私信消息列表
+ (void)requestPrivateLetterListWithBigcastId:(NSString *)bigcastId
                                     pageSize:(NSInteger)pageSize
                                lastMessageId:(NSString *)lastMessageId
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    if (bigcastId) {
        [param setObject:bigcastId forKey:@"bignameId"];
    }
    if (lastMessageId) {
        [param setObject:lastMessageId forKey:@"lastMessageId"];
    }
    [param setObject:[NSString stringWithFormat:@"%zd", pageSize] forKey:@"pageSize"];
    [self getDataInfoWithUrl:kAPI_PrivateLetter_List params:param start:startBlock failure:failBlock success:success];
}

// 私信更新已读
+ (void)requestPrivateLetterRefreshReadWithBigcastId:(NSString *)bigcastId
                                           messageId:(NSString *)messageId
                                               start:(void (^)())startBlock
                                             failure:(void (^)())failBlock
                                             success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    if (bigcastId) {
        [param setObject:bigcastId forKey:@"bignameId"];
    }
    if (messageId) {
        [param setObject:messageId forKey:@"messageId"];
    }
    [self getDataInfoWithUrl:kAPI_PrivateLetter_RefreshRead params:param start:startBlock failure:failBlock success:success];
}

#pragma mark - 投顾私信
+ (void)requestDakaPrivateLetterOrderListWithStart:(void (^)())startBlock
                                           failure:(void (^)())failBlock
                                           success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_Daka_PrivateLetter_OrderList params:nil start:startBlock failure:failBlock success:success];
}

+ (void)requestDakaPrivateLetterRecordListWithOrderId:(NSString *)orderId
                                                start:(void (^)())startBlock
                                              failure:(void (^)())failBlock
                                              success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    if (orderId.length) {
        [param setObject:orderId forKey:@"orderId"];
    }
    [self getDataInfoWithUrl:kAPI_Daka_PrivateLetter_RecordList params:param start:startBlock failure:failBlock success:success];
}

+ (void)requestDakaPrivateLetterStopWithOrderId:(NSString *)orderId
                                          start:(void (^)())startBlock
                                        failure:(void (^)())failBlock
                                        success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    if (orderId.length) {
        [param setObject:orderId forKey:@"orderId"];
    }
    [self getDataInfoWithUrl:kAPI_Daka_PrivateLetter_Stop params:param start:startBlock failure:failBlock success:success];
}

// 发送私信
+ (void)requestDakaPrivateLetterSendWithOrderId:(NSString *)orderId
                                        content:(NSString *)content
                                    contentType:(NSString *)contentType
                                          start:(void (^)())startBlock
                                        failure:(void (^)())failBlock
                                        success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    if (contentType) {
        [param setObject:contentType forKey:@"contentType"];
    }
    if (orderId.length) {
        [param setObject:orderId forKey:@"orderId"];
    }
    if (content) {
        [param setObject:content forKey:@"content"];
    }
    [self postDataJsonInfoWithUrl:kAPI_Daka_PrivateLetter_Send params:param start:startBlock failure:failBlock success:success];
}


#pragma mark -- 卡券列表
+ (void)requestCouponListWithStatus:(NSInteger )status
                               Page:(NSInteger)page
                           pageSize:(NSInteger)pageSize
                              start:(void (^)())startBlock
                            failure:(void (^)())failBlock
                            success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:[NSString stringWithFormat:@"%zd", status] forKey:@"status"];
    [params setObject:[NSString stringWithFormat:@"%zd", page] forKey:@"pageNo"];
    [params setObject:[NSString stringWithFormat:@"%zd", pageSize] forKey:@"pageSize"];
    [self getDataInfoWithUrl:KAPI_Coupon_List
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}


#pragma mark --- 支付时选择卡券列表
+ (void)requestPaymentCouponListWithBignameId:(NSString *)bignameId
                                  consumeType:(NSInteger)consumeType
                                    contentId:(NSInteger)contentId
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (bignameId.length) {
        [params setObject:bignameId forKey:@"bignameId"];
    }
    [params setObject:[NSNumber numberWithInteger:consumeType] forKey:@"consumeType"];
    [params setObject:[NSNumber numberWithInteger:contentId] forKey:@"contentId"];
    [self getDataInfoWithUrl:KAPI_Payment_Coupon_List
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

//获取赠送给新手的卡券
+ (void)getBeginnerUserCouponStart:(void (^)())startBlock
                           failure:(void (^)())failBlock
                           success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPIGetCoupon params:nil start:startBlock failure:failBlock success:success];
}


// 私信评分
+ (void)letterFeedBackWithQuestionId:(NSInteger)letterId StarLevel:(NSInteger)starLevel feedback:(NSString *)feedback start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:[NSNumber numberWithInteger:letterId] forKey:@"id"];
    [param setObject:[NSNumber numberWithInteger:starLevel] forKey:@"starLevel"];
    [param setObject:feedback ? feedback : @"" forKey:@"feedback"];
    [self postDataJsonInfoWithUrl:kAPI_Letter_FeedBack params:param start:startBlock failure:failBlock success:success];
}



@end
