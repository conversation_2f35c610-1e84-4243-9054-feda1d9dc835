//
//  HttpRequestTool+Statistics.m
//  QCYZT
//
//  Created by Cursor on 2023-05-10
//  Copyright © 2023 Cursor. All rights reserved.
//

#import "HttpRequestTool+Statistics.h"
#import "StatisticsFunctionItemModel.h"

static NSString *const kAPI_Statistics_FunctionUsage = @"/api/v2/silent/functionUsage"; // 功能使用上报
static NSString *const kAPI_Statistics_MemberCenterVisit = @"/api/v2/uvc/visit"; // 会员中心访问上报

@implementation HttpRequestTool (Statistics)

/**
 * 功能使用上报
 *
 * @param items 功能使用上报项数组
 * @param startBlock 请求开始回调
 * @param failBlock 请求失败回调
 * @param success 请求成功回调
 */
+ (void)requestStatisticsFunctionUsageWithItems:(NSArray *)items
                                         start:(void (^)(void))startBlock
                                       failure:(void (^)(void))failBlock
                                       success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    
    if (items.count) {
        NSMutableArray *itemsArray = [NSMutableArray array];
        
        for (id item in items) {
            if ([item isKindOfClass:[StatisticsFunctionItemModel class]]) {
                StatisticsFunctionItemModel *model = (StatisticsFunctionItemModel *)item;
                [itemsArray addObject:[model toDictionary]];
            } else if ([item isKindOfClass:[NSDictionary class]]) {
                [itemsArray addObject:item];
            }
        }
        
        if (itemsArray.count) {
            [params setObject:itemsArray forKey:@"items"];
        }
    }
    
    [self postDataJsonInfoWithUrl:KDakaBaseUrl(kAPI_Statistics_FunctionUsage)
                       params:params
                        start:startBlock
                      failure:failBlock
                      success:success];
}

/**
 * 会员中心访问上报
 *
 * @param functionId 功能ID
 * @param startBlock 请求开始回调
 * @param failBlock 请求失败回调
 * @param success 请求成功回调
 */
+ (void)requestMemberCenterVisitWithFunctionId:(NSInteger)functionId
                                        start:(void (^)(void))startBlock
                                      failure:(void (^)(void))failBlock
                                      success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:@(functionId) forKey:@"functionId"];

    [self getDataInfoWithUrl:KDakaBaseUrl(kAPI_Statistics_MemberCenterVisit)
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

@end
