//
//  HttpRequestTool+AskCode.m
//  QCYZT
//
//  Created by macPro on 2019/2/20.
//  Copyright © 2019 LZKJ. All rights reserved.
//

#import "HttpRequestTool+AskCode.h"
                
/// 问股 列表
#define kAPI_AskCode_List                       KDakaBaseUrl(@"/api/v2/questionInterface/list.do")
/// 问股 热门数据列表
#define kAPI_AskCode_Recommon_List              KDakaBaseUrl(@"/api/v2/recommonContent/question/list.do")
/// 问股 我听的列表
#define kAPI_AskCode_Listen_List                KDakaBaseUrl(@"/api/v2/questionInterface/listenList.do")
/// 问股 我问的列表 我是用户
#define kAPI_AskCode_Ask_List                   KDakaBaseUrl(@"/api/v2/questionInterface/askList.do")
/// 问股 问我的列表 我是投顾
#define kAPI_AskCode_AskMe_List                 KDakaBaseUrl(@"/api/v2/questionInterface/answerList.do")
/// 问股 投顾回答的问股列表
#define kAPI_AskCode_DakaAnswer_List            KDakaBaseUrl(@"/api/v2/questionInterface/dkAnswerList.do")
/// 问股 收藏列表
#define kAPI_AskCode_CollectList                KDakaBaseUrl(@"/api/v2/questionInterface/collectList.do")
/// 问股 是否有自选股相关的问股 (判断问股页面是否有自选股选项卡)
#define kAPI_AskCode_SelfListCount              KDakaBaseUrl(@"/api/v2/questionInterface/selfListCount.do")
/// 问股 自选股相关问股列表
#define kAPI_AskCode_SelfList                   KDakaBaseUrl(@"/api/v2/questionInterface/selfStockList.do")
/// 问股详情
#define kAPI_AskCode_Detail                     KDakaBaseUrl(@"/api/v2/questionInterface/detail.do")
/// 收藏问股
#define kAPI_Question_Collect                   KDakaBaseUrl(@"/api/v2/questionInterface/collect.do")
/// 取消收藏问股
#define kAPI_Question_CancelCollect             KDakaBaseUrl(@"/api/v2/questionInterface/cancel_collect.do")
/// 提问
#define kAPI_Question_Ask                       KDakaBaseUrl(@"/api/v2/questionInterface/question_submit.do")
/// 提问后 排队时间提醒信息
#define kAPI_Question_WaitTime                  KDakaBaseUrl(@"/api/v2/questionInterface/remind.do")
/// 回答问股
#define kAPI_Question_Answer                    KDakaBaseUrl(@"/api/v2/questionInterface/answer_submit.do")
/// 问股点赞
#define kAPI_Question_Praise                    KDakaBaseUrl(@"/api/v2/questionInterface/satisfy.do")


///  我的问股数量
#define kAPI_Question_MineNum                    KDakaBaseUrl(@"/api/v2/questionInterface/askListCount.do")
/// 问我的数量
#define kAPI_Question_AskMeNum                    KDakaBaseUrl(@"/api/v2/questionInterface/answerListCount.do")
/// 未回答数量
#define kAPI_Question_NotAnswerNum                   KDakaBaseUrl(@"/api/v2/questionInterface/needAnswerListCount.do")



@implementation HttpRequestTool (AskCode)

#pragma mark 问股 2.0

/// 问股列表
/// - Parameters:
///   - page: 页码
///   - pageSize: 分页size
///   - listType: 列表类型  2 所有的 3 我听过的 4 我问的 5 投顾回答的 6 问我的 8 自选
///   - keyWords: 搜索关键字
///   - startBlock: 开始回调
///   - failBlock: 失败回调
///   - success: 成功
+(void)getAskCodeListWithPage:(NSInteger)page
                     pageSize:(NSInteger)pageSize
                     listType:(NSInteger)listType
                     keyWords:(NSString *)keyWords
                        start:(void (^)())startBlock
                      failure:(void (^)())failBlock
                      success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:[NSNumber numberWithInteger:page] forKey:@"pageNo"];
    [param setObject:[NSNumber numberWithInteger:pageSize] forKey:@"pageSize"];
    if (keyWords.length > 0) {
        [param setObject:keyWords forKey:@"keyWords"];
    }
    if (listType == 2) { // 所有
        [self postDataJsonInfoWithUrl:kAPI_AskCode_List params:param start:startBlock failure:failBlock success:success];
    } else if (listType == 3) {
        [self postDataJsonInfoWithUrl:kAPI_AskCode_Listen_List params:param start:startBlock failure:failBlock success:success];
    } else if (listType == 4) { // 我问的
        [self postDataJsonInfoWithUrl:kAPI_AskCode_Ask_List params:param start:startBlock failure:failBlock success:success];
    } else if (listType == 6) { //问我的
        [self postDataJsonInfoWithUrl:kAPI_AskCode_AskMe_List params:param start:startBlock failure:failBlock success:success];
    } else if (listType == 8) { // 自选的
        [self postDataJsonInfoWithUrl:kAPI_AskCode_SelfList params:param start:startBlock failure:failBlock success:success];
    }
}

/// 投顾主页问股列表
/// - Parameters:
///   - page: 页码
///   - pageSize: 分页size
///   - dakaId: 投顾id
///   - startBlock: 开始回调
///   - failBlock: 失败回调
///   - success: 成功回调
+ (void)getDakaAskCodeListWithPage:(NSInteger)page
                          pageSize:(NSInteger)pageSize
                            dakaId:(NSString *)dakaId
                             start:(void (^)())startBlock
                           failure:(void (^)())failBlock
                           success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:[NSNumber numberWithInteger:page] forKey:@"pageNo"];
    [param setObject:[NSNumber numberWithInteger:pageSize] forKey:@"pageSize"];
    if (dakaId.length > 0) {
        [param setObject:dakaId forKey:@"answerUserid"];
    }
    [self postDataJsonInfoWithUrl:kAPI_AskCode_DakaAnswer_List params:param start:startBlock failure:failBlock success:success];
}


/// 热门问股数据
+ (void)getAskCodeRecommonDataWithstart:(void (^)())startBlock
                                failure:(void (^)())failBlock
                                success:(requestSuccessBlock)success {
    [self postDataJsonInfoWithUrl:kAPI_AskCode_Recommon_List params:@{@"positionid": @200108} start:startBlock failure:failBlock success:success];
}


/// 问股收藏列表
+(void)getAskCodeCollectListWithQuestionIdPage:(NSInteger)page
                                      pageSize:(NSInteger)pageSize
                                         start:(void (^)())startBlock
                                       failure:(void (^)())failBlock
                                       success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:[NSNumber numberWithInteger:page] forKey:@"pageNo"];
    [param setObject:[NSNumber numberWithInteger:pageSize] forKey:@"pageSize"];
    [self postDataJsonInfoWithUrl:kAPI_AskCode_CollectList params:param start:startBlock failure:failBlock success:success];
}

// 问股详情
+(void)getQuestionDetailWithWithQuestionId:(NSString *)questionid
                                     start:(void (^)())startBlock
                                   failure:(void (^)())failBlock
                                   success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (questionid) {
        [params setObject:questionid forKey:@"questionid"];
    }
    [self getDataInfoWithUrl:kAPI_AskCode_Detail
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 问股收藏
+(void)questionCollectWithQuestionId:(NSString *)questionid
                               start:(void (^)())startBlock
                             failure:(void (^)())failBlock
                             success:(requestSuccessBlock)success{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (questionid) {
        [params setObject:questionid forKey:@"questionid"];
    }
    [self getDataInfoWithUrl:kAPI_Question_Collect
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 取消问股收藏
+(void)cancelQuestionCollectWithQuestionId:(NSString *)questionid
                                     start:(void (^)())startBlock
                                   failure:(void (^)())failBlock
                                   success:(requestSuccessBlock)success{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (questionid) {
        [params setObject:questionid forKey:@"questionid"];
    }
    [self getDataInfoWithUrl:kAPI_Question_CancelCollect
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 问股超值
+(void)questionPraiseWithQuestionId:(NSString *)questionid
                              start:(void (^)())startBlock
                            failure:(void (^)())failBlock
                            success:(requestSuccessBlock)success{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (questionid) {
        [params setObject:questionid forKey:@"questionid"];
    }
    [self getDataInfoWithUrl:kAPI_Question_Praise
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 查询排队时间
+(void)questionCheckWaitTimeWithAnswerId:(NSString *)answerId
                                   start:(void (^)())startBlock
                                 failure:(void (^)())failBlock
                                 success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (answerId) {
        [params setObject:answerId forKey:@"answerUserid"];
    }
    [self postDataJsonInfoWithUrl:kAPI_Question_WaitTime params:params start:startBlock failure:failBlock success:success];
}


// 提问
+(void)questionAskWithAnswerId:(NSString *)answerId
                       content:(NSString *)content
                          type:(NSString *)type
                       goodsId:(NSString *)goodsId
                     stockCode:(NSString *)stockCode
                     stockName:(NSString *)stockName
                     usePoints:(NSInteger)usePoints
                         start:(void (^)())startBlock
                       failure:(void (^)())failBlock
                       success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (answerId) {
        [params setObject:answerId forKey:@"answerUserid"];
    }
    if (content) {
        [params setObject:content forKey:@"questionContent"];
    }
    if (stockCode) {
        [params setObject:stockCode forKey:@"stockCode"];
    }
    if (stockName) {
        [params setObject:stockName forKey:@"stockName"];
    }
    if (type.length > 0) {
        [params setObject:type forKey:@"type"];
    }
    if (goodsId.length > 0) {
        [params setObject:goodsId forKey:@"goodsId"];
    }
    if (usePoints > 0) {
        [params setObject:@(1) forKey:@"usePoints"];
    }
    [self postDataJsonInfoWithUrl:kAPI_Question_Ask params:params start:startBlock failure:failBlock success:success];
}

// 回答
+(void)questionAnswerWithQuestionid:(NSString *)questionid
                         timeLength:(NSString *)timeLength
                             ossUrl:(NSString *)url
                           isPublic:(NSString *)isPublic
                              start:(void (^)())startBlock
                            failure:(void (^)())failBlock
                            success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (questionid) {
        [params setObject:questionid forKey:@"questionId"];
    }
    if (timeLength) {
        [params setObject:timeLength forKey:@"questionAnswerLength"];
    }
    if (isPublic) {
        [params setObject:isPublic forKey:@"isPublic"];
    }
    if (url) {
        [params setObject:url forKey:@"answerOssUrl"];
    }
    [self postDataJsonInfoWithUrl:kAPI_Question_Answer params:params start:startBlock failure:failBlock success:success];
}

// 自选股是否有问股数据
+(void)getSelfStocksQuestionCountWithKeyWord:(NSString *)keyWord
                                       start:(void (^)())startBlock
                                     failure:(void (^)())failBlock
                                     success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (keyWord.length) {
        [params setObject:keyWord forKey:@"keyWords"];
    }
    [self postDataJsonInfoWithUrl:kAPI_AskCode_SelfListCount params:params start:startBlock failure:failBlock success:success];
}

// 我的问股数量
+ (void)getMyQuestionCountWithStart:(void (^)())startBlock
                            failure:(void (^)())failBlock
                            success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_Question_MineNum params:nil start:startBlock failure:failBlock success:success];
}

// 问我的数量
+ (void)getAskMeCountWithStart:(void (^)())startBlock
                            failure:(void (^)())failBlock
                            success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_Question_AskMeNum params:nil start:startBlock failure:failBlock success:success];
}

// 未回答数量
+ (void)getNotAnswerCountWithStart:(void (^)())startBlock
                            failure:(void (^)())failBlock
                            success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_Question_NotAnswerNum params:nil start:startBlock failure:failBlock success:success];
}

// 问股评分 意见反馈
+ (void)askCodeFeedBackWithQuestionId:(NSInteger)questionId
                            StarLevel:(NSInteger)starLevel
                            feedback:(NSString *)feedback
                                start:(void (^)())startBlock
                              failure:(void (^)())failBlock
                              success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:[NSNumber numberWithInteger:questionId] forKey:@"questionId"];
    [param setObject:[NSNumber numberWithInteger:starLevel] forKey:@"starLevel"];
    [param setObject:feedback ? feedback : @"" forKey:@"feedback"];
    [self postDataJsonInfoWithUrl:kAPI_Question_FeedBack params:param start:startBlock failure:failBlock success:success];
}

@end
