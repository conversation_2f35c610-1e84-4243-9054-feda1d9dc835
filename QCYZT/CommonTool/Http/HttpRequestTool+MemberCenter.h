//
//  HttpRequestTool+MemeberCenter.h
//  QCYZT
//
//  Created by zeng on 2022/6/20.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "HttpRequestTool.h"
#import "FMMemberCenterConstants.h"

@interface HttpRequestTool (MemberCenter)

// 会员中心决策请求类型
typedef NS_ENUM(NSInteger, MemberCenterJCRequestType) {
    MemberCenterJCRequestTypeNone = 0,
    MemberCenterJCRequestTypeGSYJCourse = 2, // 股市赢家课堂
    MemberCenterJCRequestTypeJCQLCourse = 3, // 决策擒龙课堂
    MemberCenterJCRequestTypeJCDJCourse = 4, // 决策点金课堂
    MemberCenterJCRequestTypeJCQLReport = 9, // 决策擒龙研报
    MemberCenterJCRequestTypeJCDJReport = 10, // 决策点金研报
    MemberCenterJCRequestTypeJCDJReport2 = 11, // 决策点金研报2
};

// 我的VIP
+ (void)requestMyVIPInfoWithStart:(void (^)())startBlock
                          failure:(void (^)())failBlock
                          success:(requestSuccessBlock)success;

// 投顾战绩
+ (void)requestBigcastRecordWithStart:(void (^)())startBlock
                              failure:(void (^)())failBlock
                              success:(requestSuccessBlock)success;

+ (void)requestBigcastRecordListWithPage:(NSUInteger)page
                                pageSize:(NSUInteger)pageSize
                                   start:(void (^)())startBlock
                                 failure:(void (^)())failBlock
                                 success:(requestSuccessBlock)success;

// 投顾vip产品
+ (void)requestBigcastProductDetailWithBigcastId:(NSString *)bigcastId
                                           start:(void (^)())startBlock
                                         failure:(void (^)())failBlock
                                         success:(requestSuccessBlock)success;

// 投顾产品分组
+ (void)requestBigcastProductGroupWithBigcastId:(NSString *)bigcastId
                                          start:(void (^)())startBlock
                                        failure:(void (^)())failBlock
                                        success:(requestSuccessBlock)success;

// 投顾产品服务期限
+ (void)requestBigcastProductPeriodWithBigcastId:(NSString *)bigcastId
                                           start:(void (^)())startBlock
                                         failure:(void (^)())failBlock
                                         success:(requestSuccessBlock)success;
// 会员优惠券
+ (void)requestBigcastProductCouponWithBigcastId:(NSString *)bigcastId
                                           start:(void (^)())startBlock
                                         failure:(void (^)())failBlock
                                         success:(requestSuccessBlock)success;

// 计算价格
+ (void)requestBigcastProductCalcPriceWithBigCastId:(NSString *)bigcastId
                                            groupId:(NSString *)groupId
                                            goodsId:(NSString *)goodsId
                                              actId:(NSString *)actId
                                           couponId:(NSString *)couponId
                                              start:(void (^)())startBlock
                                            failure:(void (^)())failBlock
                                            success:(requestSuccessBlock)success;

// 下单产品
+ (void)requestOrderBigcastProductWithBigCastId:(NSString *)bigcastId
                                        groupId:(NSString *)groupId
                                        goodsId:(NSString *)goodsId
                                          actId:(NSString *)actId
                                       couponId:(NSString *)couponId
                                    mailAddress:(NSString *)mailAddress
                                          start:(void (^)())startBlock
                                        failure:(void (^)())failBlock
                                        success:(requestSuccessBlock)success;

// 查询支付结果
+ (void)queryPayMemberProductResult;

// 查看卡券兑换老师列表
+ (void)requestCouponExchangeBigcastListWithCouponId:(NSString *)couponId
                                               start:(void (^)())startBlock
                                             failure:(void (^)())failBlock
                                             success:(requestSuccessBlock)success;

// 卡券兑换前检查信息
+ (void)requestCouponExchangeCheckInfoWithStart:(void (^)())startBlock
                                        failure:(void (^)())failBlock
                                        success:(requestSuccessBlock)success;

// 兑换服务
+ (void)requestCouponExchangeWithBigCastId:(NSString *)bigcastId
                                   groupId:(NSString *)groupId
                                  couponId:(NSString *)couponId
                                     start:(void (^)())startBlock
                                   failure:(void (^)())failBlock
                                   success:(requestSuccessBlock)success;

// 领取特权的老师列表
+ (void)requestVipPrivilegeBigcastListWithStart:(void (^)())startBlock
                                        failure:(void (^)())failBlock
                                        success:(requestSuccessBlock)success;

// 领取特权
+ (void)requestVipPrivilegeChooseBigcastWithIds:(NSString *)ids
                                          start:(void (^)())startBlock
                                        failure:(void (^)())failBlock
                                        success:(requestSuccessBlock)success;

//// 擒龙解盘/点金解盘(直播数据)
//+ (void)requestQLJPListDataWithPageType:(NSInteger)pageType
//                               PageSize:(NSInteger)pageSize
//                                 pageNo:(NSInteger)pageNo
//                                  start:(void (^)())startBlock
//                                failure:(void (^)())failBlock
//                                success:(requestSuccessBlock)success;

// 龙头密训、大师投研下面笔记的列表
+ (void)requestQLNoteListDataWithPageType:(NSInteger)pageType
                                 PageSize:(NSInteger)pageSize
                                   pageNo:(NSInteger)pageNo
                                 pushTime:(NSString *)pushTime
                                    start:(void (^)())startBlock
                                  failure:(void (^)())failBlock
                                  success:(requestSuccessBlock)success;

// 龙头密训、大师投研下面的列表
+ (void)requestXLListDataWithRequesType:(MemberCenterJCRequestType)requestType
                               pageSize:(NSInteger)pageSize
                                 pageNo:(NSInteger)pageNo
                                  start:(void (^)())startBlock
                                failure:(void (^)())failBlock
                                success:(requestSuccessBlock)success;


// 擒龙训练 / 点金策略 / 点金研报 / 擒龙战法 / 捉妖战法 详情
+ (void)requestZFAndDJDetailWithRequestType:(MemberCenterJCRequestType)requestType
                                  contentId:(NSInteger)ContentId
                                 serverType:(NSString *)serverType
                                      start:(void (^)())startBlock
                                    failure:(void (^)())failBlock
                                    success:(requestSuccessBlock)success;

// 决策战绩榜
+ (void)requestMemberCenterJCRecordWithType:(NSInteger)type
                                      start:(void (^)())startBlock
                                    failure:(void (^)())failBlock
                                    success:(requestSuccessBlock)success;

// 决策擒龙
+ (void)requestMemberCenterJCQLWithDay:(NSString *)day
                                  type:(NSInteger)type
                                 start:(void (^)())startBlock
                               failure:(void (^)())failBlock
                               success:(requestSuccessBlock)success;

// 价值龙头
+ (void)requestMemberCenterJZLTWithDay:(NSString *)day
                                 start:(void (^)())startBlock
                               failure:(void (^)())failBlock
                               success:(requestSuccessBlock)success;

// 龙虎抓妖
+ (void)requestMemberCenterLHZYWithDay:(NSString *)day
                          capitalModel:(NSString *)capitalModel
                                 start:(void (^)())startBlock
                               failure:(void (^)())failBlock
                               success:(requestSuccessBlock)success;

// 龙虎抓妖资金模型角标
+ (void)requestMemberCenterLHZYBadgesWithDay:(NSString *)day
                                       start:(void (^)())startBlock
                                     failure:(void (^)())failBlock
                                     success:(requestSuccessBlock)success;

// 龙虎抓妖席位数据
+ (void)requestMemberCenterLHZYSeatsWithJoinPoolID:(NSString *)joinPoolID
                                             start:(void (^)())startBlock
                                           failure:(void (^)())failBlock
                                           success:(requestSuccessBlock)success;

#pragma mark - 股市赢家
// 签约大咖
+ (void)requestMemberCenterVIPSignDakaWithType:(NSString *)type
                                    serverType:(NSString *)serverType
                                         start:(void (^)(void))startBlock
                                       failure:(void (^)(void))failBlock
                                       success:(requestSuccessBlock)success;

// 股市赢家今日操作
+ (void)requestGSYJTodayOperationWithServerType:(JCVIPType)serverType
                                           start:(void (^)(void))startBlock
                                         failure:(void (^)(void))failBlock
                                         success:(requestSuccessBlock)success;

// 股市赢家止盈个股
+ (void)requestGSYJProfitEndStocksWithServerType:(JCVIPType)serverType
                                           start:(void (^)(void))startBlock
                                    failure:(void (^)(void))failBlock
                                         success:(requestSuccessBlock)success;

// 股市赢家股票操作
+ (void)requestGSYJStockOperationWithServerType:(JCVIPType)serverType
                                       strageId:(NSString *)strageId
                                          start:(void (^)(void))startBlock
                                        failure:(void (^)(void))failBlock
                                        success:(requestSuccessBlock)success;

// 股市赢家历史持仓（支持分页）
+ (void)requestGSYJHisHoldsWithPage:(NSUInteger)page
                           pageSize:(NSUInteger)pageSize
                         serverType:(JCVIPType)serverType
                              start:(void (^)(void))startBlock
                            failure:(void (^)(void))failBlock
                            success:(requestSuccessBlock)success;

// 股市赢家当前持仓
+ (void)requestGSYJCurHoldsWithServerType:(JCVIPType)serverType
                                     start:(void (^)(void))startBlock
                                   failure:(void (^)(void))failBlock
                                   success:(requestSuccessBlock)success;

// 股市赢家策略池
+ (void)requestGSYJStrategyPoolStockWithDay:(NSString *)day
                                         serverType:(JCVIPType)serverType
                                              start:(void (^)())startBlock
                                            failure:(void (^)())failBlock
                                            success:(requestSuccessBlock)success;

// 股市赢家定制版小班池
+ (void)requestGSYJDZSmallPoolStockWithDay:(NSString *)day
                                serverType:(JCVIPType)serverType
                                     start:(void (^)())startBlock
                                   failure:(void (^)())failBlock
                                   success:(requestSuccessBlock)success;

// 首席课堂  type 1：投教课程 2：童学大决策
+ (void)requestGSYJPrivilegeCourseWithPageSize:(NSInteger)pageSize
                                                pageNo:(NSInteger)pageNo
                                                  type:(NSInteger)type
                                            serverType:(JCVIPType)serverType
                                                 start:(void (^)(void))startBlock
                                               failure:(void (^)(void))failBlock
                                               success:(requestSuccessBlock)success;

// 策略报告/投研报告
+ (void)requestGSYJPrivilegeReportsWithPageSize:(NSInteger)pageSize
                                                 pageNo:(NSInteger)pageNo
                                                   type:(NSInteger)type
                                             serverType:(JCVIPType)serverType
                                                  start:(void (^)(void))startBlock
                                                failure:(void (^)(void))failBlock
                                                success:(requestSuccessBlock)success;

@end

