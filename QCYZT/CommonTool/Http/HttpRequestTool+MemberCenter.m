//
//  HttpRequestTool+MemeberCenter.m
//  QCYZT
//
//  Created by zeng on 2022/6/20.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "HttpRequestTool+MemberCenter.h"

#define kAPI_MemberCenter_MyVIP                                     KDakaBaseUrl(@"/api/v2/uvc/myvip") //我的vip
#define kAPI_MemberCenter_BigcastRecord                             KDakaBaseUrl(@"/api/v2/rr/index.do") //投顾战绩
#define kAPI_MemberCenter_BigcastRecordList                         KDakaBaseUrl(@"/api/v2/rr/list.do") //投顾战绩列表
#define kAPI_MemberCenter_BigcastProductDetail                      KDakaBaseUrl(@"/api/bvic/detail.do") //投顾VIP产品详情
#define kAPI_MemberCenter_BigcastProductGroup                       KDakaBaseUrl(@"/api/bvgpc/list.do") //投顾产品会员分组
#define kAPI_MemberCenter_BigcastProductPeriod                      KDakaBaseUrl(@"/api/bcgc/list.do") //投顾产品会员服务期限
#define kAPI_MemberCenter_BigcastProductCoupon                      KDakaBaseUrl(@"/api/ucc/available/list.do") //会员优惠券
#define kAPI_MemberCenter_BigcastProductCalcPrice                   KDakaBaseUrl(@"/api/rechargev6/calcPrice.do") // 计算价格
#define kAPI_MemberCenter_BigcastProductOrder                       KDakaBaseUrl(@"/api/rechargev6/order.do") // 下单投顾产品
#define kAPI_MemberCenter_BigcastProductQueryResult                 KDakaBaseUrl(@"/api/rechargev6/recharge.do") // 查询支付结果
#define kAPI_MemberCenter_BigcastProductCouponForBigcast            KDakaBaseUrl(@"/api/bvic/list.do") // 卡券兑换选择老师
#define kAPI_MemberCenter_BigcastProductCouponCheckInfo             KDakaBaseUrl(@"/api/bvc/checkInfo.do") // 卡券兑换前检查信息
#define kAPI_MemberCenter_BigcastProductCouponExchange              KDakaBaseUrl(@"/api/rechargev6/exchange.do") // 卡券兑换
#define kAPI_MemberCenter_BigcastProductVipPrivilegeBigcastList     KDakaBaseUrl(@"/api/bvic/receive/list.do") // 领取特权的老师列表
#define kAPI_MemberCenter_BigcastProductVipPrivilegeChooseBigcast   KDakaBaseUrl(@"/api/bvc/receive.do") // 领取特权

//// 擒龙解盘
//static NSString *const kAPI_QLJP = @"/api/v2/pc/dcp/liveroom";
// 决策擒龙——龙头密训——笔记列表
static NSString *const kAPI_QLNote = @"/api/v2/pc/dcp/note";
// 决策擒龙——龙头密训——研报列表
static NSString *const kAPI_QLReport = @"/api/v2/pc/dcp/reports.do";
// 决策擒龙——龙头密训——课程列表
static NSString *const kAPI_QLCourse = @"/api/v2/pc/dcp/course.do";
// 决策擒龙——龙头密训——研报详情
static NSString *const kAPI_QLReportDetail = @"/api/v2/pc/dcp/reportDetail.do";
// 决策擒龙——龙头密训——课程详情
static NSString *const kAPI_QLCourseDetail = @"/api/v2/pc/dcp/courseDetail.do";


//// 点金解盘
//static NSString *const kAPI_DJJP = @"/api/v2/pc/dpp/liveroom";
// 决策点金——大师投研——笔记列表
static NSString *const kAPI_DJNote = @"/api/v2/pc/dpp/note";
// 决策点金——大师投研——研报列表
static NSString *const kAPI_DJReport = @"/api/v2/pc/dpp/reports.do";
// 决策点金——大师投研——课程列表
static NSString *const kAPI_DJCourse = @"/api/v2/pc/dpp/course.do";
// 决策点金——大师投研——研报详情
static NSString *const kAPI_DJReportDetail = @"/api/v2/pc/dpp/reportDetail.do";
// 决策点金——大师投研——课程详情
static NSString *const kAPI_DJCourseDetail = @"/api/v2/pc/dpp/courseDetail.do";



static NSString *const kAPI_MemberCenter_VIPJC_ZJB = @"/api/v2/uvc/stock"; // VIP决策战绩榜
static NSString *const kAPI_MemberCenter_VIPJC_JCQL = @"/api/v2/dcp/stocks"; // VIP决策决策擒龙
static NSString *const kAPI_MemberCenter_VIPJC_JZLT = @"/api/v2/dpp/price/stocks"; // VIP决策价值龙头
static NSString *const kAPI_MemberCenter_VIPJC_LHZY_Badges = @"/api/v2/dpp/demon/statStocks"; // 龙虎抓妖角标
static NSString *const kAPI_MemberCenter_VIPJC_LHZY = @"/api/v2/dpp/demon/stocks"; // VIP决策龙虎抓妖
static NSString *const kAPI_MemberCenter_VIPJC_LHZY_Seat = @"/api/v2/dpp/dragon/seats"; // VIP决策龙虎抓妖席位信息

// 股市赢家相关
static NSString *const kAPI_MemberCenter_VipSignDaka = @"/api/v2/uvc/querySignBigname";     // 签约大咖

static NSString *const kAPI_GSYJ_TodayOperation = @"/api/v2/csp/todayOpera.do";  // 今日操作建议
static NSString *const kAPI_GSYJDZ_TodayOperation = @"/api/v2/csp/ctodayOpera.do";  // 股市赢家定制 今日操作建议
static NSString *const kAPI_GSYJ_StockOperation = @"/api/v2/csp/stockOpera.do";  // 股票操作
static NSString *const kAPI_GSYJDZ_StockOperation = @"/api/v2/csp/cstockOpera.do";  // 股市赢家定制 股票操作
static NSString *const kAPI_GSYJ_HisHolds = @"/api/v2/csp/hisHolds.do";     // 历史持仓
static NSString *const kAPI_GSYJDZ_HisHolds = @"/api/v2/csp/chisHolds.do";     // 股市赢家定制 历史持仓
static NSString *const kAPI_GSYJ_CurHolds = @"/api/v2/csp/curHolds.do";     // 当前持仓
static NSString *const kAPI_GSYJDZ_CurHolds = @"/api/v2/csp/ccurHolds.do";     // 股市赢家定制 当前持仓
static NSString *const kAPI_GSYJ_ProfitEndStocks = @"/api/v2/csp/profitEnd";              // 止盈个股
static NSString *const kAPI_GSYJDZ_ProfitEndStocks = @"/api/v2/csp/cprofitEnd";              // 股市赢家定制 止盈个股

static NSString *const kAPI_GSYJ_StrategyPoolStock = @"/api/v2/csp/stock.do";     // 策略池股票
static NSString *const kAPI_GSYJDZ_SmallClassStock = @"/api/v2/csp/cstock.do";     // 小班池股票
static NSString *const kAPI_GSYJ_PrivilegeCourse = @"/api/v2/csp/course.do";              // 首席课堂列表
static NSString *const kAPI_GSYJ_PrivilegeReports = @"/api/v2/csp/reports.do";            // 策略报告/投研报告列表
static NSString *const kAPI_GSYJ_CourseDetail = @"/api/v2/csp/courseDetail.do";    // 股市赢家——专享特权——课程详情

@implementation HttpRequestTool (MemberCenter)

// 我的VIP
+ (void)requestMyVIPInfoWithStart:(void (^)())startBlock
                          failure:(void (^)())failBlock
                          success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_MemberCenter_MyVIP
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 投顾战绩
+ (void)requestBigcastRecordWithStart:(void (^)())startBlock
                              failure:(void (^)())failBlock
                              success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_MemberCenter_BigcastRecord
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

+ (void)requestBigcastRecordListWithPage:(NSUInteger)page
                                pageSize:(NSUInteger)pageSize
                                   start:(void (^)())startBlock
                                 failure:(void (^)())failBlock
                                 success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:[NSString stringWithFormat:@"%zd", page] forKey:@"pageNo"];
    [params setObject:[NSString stringWithFormat:@"%zd", pageSize] forKey:@"pageSize"];
    [self getDataInfoWithUrl:kAPI_MemberCenter_BigcastRecordList
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 投顾vip产品
+ (void)requestBigcastProductDetailWithBigcastId:(NSString *)bigcastId
                                           start:(void (^)())startBlock
                                         failure:(void (^)())failBlock
                                         success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:bigcastId forKey:@"bignameId"];
    [self getDataInfoWithUrl:kAPI_MemberCenter_BigcastProductDetail
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 投顾产品分组
+ (void)requestBigcastProductGroupWithBigcastId:(NSString *)bigcastId
                                          start:(void (^)())startBlock
                                        failure:(void (^)())failBlock
                                        success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:bigcastId forKey:@"bignameId"];
    [self getDataInfoWithUrl:kAPI_MemberCenter_BigcastProductGroup
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 投顾产品服务期限
+ (void)requestBigcastProductPeriodWithBigcastId:(NSString *)bigcastId
                                           start:(void (^)())startBlock
                                         failure:(void (^)())failBlock
                                         success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:bigcastId forKey:@"bignameId"];
    [self getDataInfoWithUrl:kAPI_MemberCenter_BigcastProductPeriod
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 会员优惠券
+ (void)requestBigcastProductCouponWithBigcastId:(NSString *)bigcastId
                                           start:(void (^)())startBlock
                                         failure:(void (^)())failBlock
                                         success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:bigcastId forKey:@"bignameId"];
    [self getDataInfoWithUrl:kAPI_MemberCenter_BigcastProductCoupon
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 计算价格
+ (void)requestBigcastProductCalcPriceWithBigCastId:(NSString *)bigcastId
                                            groupId:(NSString *)groupId
                                            goodsId:(NSString *)goodsId
                                              actId:(NSString *)actId
                                           couponId:(NSString *)couponId
                                              start:(void (^)())startBlock
                                            failure:(void (^)())failBlock
                                            success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:bigcastId forKey:@"bignameId"];
    [params setObject:groupId forKey:@"groupId"];
    [params setObject:goodsId forKey:@"goodsId"];
    if (actId.length) {
        [params setObject:actId forKey:@"actId"];
    }
    if (couponId.length) {
        [params setObject:couponId forKey:@"couponsId"];
    }
    [self putDataInfoWithUrl:kAPI_MemberCenter_BigcastProductCalcPrice
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 下单产品
+ (void)requestOrderBigcastProductWithBigCastId:(NSString *)bigcastId
                                        groupId:(NSString *)groupId
                                        goodsId:(NSString *)goodsId
                                          actId:(NSString *)actId
                                       couponId:(NSString *)couponId
                                    mailAddress:(NSString *)mailAddress
                                          start:(void (^)())startBlock
                                        failure:(void (^)())failBlock
                                        success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:bigcastId forKey:@"bignameId"];
    [params setObject:groupId forKey:@"groupId"];
    [params setObject:goodsId forKey:@"goodsId"];
    if (actId.length) {
        [params setObject:actId forKey:@"actId"];
    }
    if (couponId.length) {
        [params setObject:couponId forKey:@"couponsId"];
    }
    if (mailAddress.length) {
        [params setObject:mailAddress forKey:@"mailAddress"];
    }
    [self putDataInfoWithUrl:kAPI_MemberCenter_BigcastProductOrder
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 查询VIP产品购买结果
+ (void)requestBigcastProductQueryResultWithOutTradeNo:(NSString *)outTradeNo
                                                 start:(void (^)())startBlock
                                               failure:(void (^)())failBlock
                                               success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:outTradeNo forKey:@"outTradeNo"];
    [self getDataInfoWithUrl:kAPI_MemberCenter_BigcastProductQueryResult
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

+ (void)queryPayMemberProductResult {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSDictionary *dic = [defaults objectForKey:kUserDefault_IsPayMemberProduct];
    if (dic && [[FMUserDefault getUserId] isEqualToString:dic[@"userId"]]) {
        [HttpRequestTool requestBigcastProductQueryResultWithOutTradeNo:dic[@"outTradeNo"] start:^{
            //            [SVProgressHUD show];
        } failure:^{
            [SVProgressHUD showErrorWithStatus:@"订单查询失败"];
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            [defaults setObject:nil forKey:kUserDefault_IsPayMemberProduct];
            [defaults synchronize];
        } success:^(NSDictionary *dict) {
            if ([dict[@"status"] isEqualToString:@"1"]) {
                [SVProgressHUD showSuccessWithStatus:@"会员产品购买成功"];

                [[NSNotificationCenter defaultCenter] postNotificationName:kMemberProductPaySuccess object:nil userInfo:@{@"orderNo":dic[@"outTradeNo"]}];
            } else if ([dic[@"errcode"] isEqualToString:@"1003"]) {
                // 订单正在处理中
                [SVProgressHUD showInfoWithStatus:dict[@"errmessage"]];
            } else {
                [SVProgressHUD showErrorWithStatus:dict[@"errmessage"]];
            }

            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            [defaults setObject:nil forKey:kUserDefault_IsPayMemberProduct];
            [defaults synchronize];
        }];
    }
}

// 查看卡券兑换老师列表
+ (void)requestCouponExchangeBigcastListWithCouponId:(NSString *)couponId
                                               start:(void (^)())startBlock
                                             failure:(void (^)())failBlock
                                             success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:couponId forKey:@"couponsId"];
    [self getDataInfoWithUrl:kAPI_MemberCenter_BigcastProductCouponForBigcast
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 卡券兑换前检查信息
+ (void)requestCouponExchangeCheckInfoWithStart:(void (^)())startBlock
                                        failure:(void (^)())failBlock
                                        success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_MemberCenter_BigcastProductCouponCheckInfo
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 兑换服务
+ (void)requestCouponExchangeWithBigCastId:(NSString *)bigcastId
                                   groupId:(NSString *)groupId
                                  couponId:(NSString *)couponId
                                     start:(void (^)())startBlock
                                   failure:(void (^)())failBlock
                                   success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:bigcastId forKey:@"bignameId"];
    [params setObject:groupId forKey:@"groupId"];
    [params setObject:couponId forKey:@"couponsId"];
    [self putDataInfoWithUrl:kAPI_MemberCenter_BigcastProductCouponExchange
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 领取特权的老师列表
+ (void)requestVipPrivilegeBigcastListWithStart:(void (^)())startBlock
                                        failure:(void (^)())failBlock
                                        success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_MemberCenter_BigcastProductVipPrivilegeBigcastList
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 领取特权
+ (void)requestVipPrivilegeChooseBigcastWithIds:(NSString *)ids
                                          start:(void (^)())startBlock
                                        failure:(void (^)())failBlock
                                        success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:ids forKey:@"ids"];
    [self getDataInfoWithUrl:kAPI_MemberCenter_BigcastProductVipPrivilegeChooseBigcast
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

//// 擒龙、点金解盘(直播数据)
//+ (void)requestQLJPListDataWithPageType:(NSInteger)pageType
//                               PageSize:(NSInteger)pageSize
//                                 pageNo:(NSInteger)pageNo
//                                  start:(void (^)())startBlock
//                                failure:(void (^)())failBlock
//                                success:(requestSuccessBlock)success {
//    NSMutableDictionary *param = [NSMutableDictionary dictionary];
//    [param setObject:[NSNumber numberWithInteger:pageNo] forKey:@"pageNo"];
//    [param setObject:[NSNumber numberWithInteger:pageSize] forKey:@"pageSize"];
//    NSString *url = pageType == 0 ? KDakaBaseUrl(kAPI_QLJP) : KDakaBaseUrl(kAPI_DJJP);
//    [self postDataJsonInfoWithUrl:url params:param start:startBlock failure:failBlock success:success];
//}

// 龙头密训、大师投研下面笔记的列表
+ (void)requestQLNoteListDataWithPageType:(NSInteger)pageType
                                 PageSize:(NSInteger)pageSize
                                   pageNo:(NSInteger)pageNo
                                 pushTime:(NSString *)pushTime
                                    start:(void (^)())startBlock
                                  failure:(void (^)())failBlock
                                  success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:[NSNumber numberWithInteger:pageNo] forKey:@"pageNo"];
    [param setObject:[NSNumber numberWithInteger:pageSize] forKey:@"pageSize"];
    NSString *url = pageType == 0 ? KDakaBaseUrl(kAPI_QLNote) : KDakaBaseUrl(kAPI_DJNote);
    [self postDataJsonInfoWithUrl:url params:param start:startBlock failure:failBlock success:success];
}

// 龙头密训、大师投研下面研报、课程的列表
+ (void)requestXLListDataWithRequesType:(MemberCenterJCRequestType)requestType
                               pageSize:(NSInteger)pageSize
                                 pageNo:(NSInteger)pageNo
                                  start:(void (^)())startBlock
                                failure:(void (^)())failBlock
                                success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:[NSNumber numberWithInteger:pageNo] forKey:@"pageNo"];
    [param setObject:[NSNumber numberWithInteger:pageSize] forKey:@"pageSize"];
    [param setObject:[NSNumber numberWithInteger:requestType] forKey:@"type"];
    NSString *url;
    if (requestType == MemberCenterJCRequestTypeJCQLCourse) {
        url = KDakaBaseUrl(kAPI_QLCourse);
    } else if (requestType == MemberCenterJCRequestTypeJCDJCourse) {
        url = KDakaBaseUrl(kAPI_DJCourse);
    } else if (requestType == MemberCenterJCRequestTypeJCQLReport) {
        url = KDakaBaseUrl(kAPI_QLReport);
    } else if (requestType == MemberCenterJCRequestTypeJCDJReport || requestType == MemberCenterJCRequestTypeJCDJReport2) {
        url = KDakaBaseUrl(kAPI_DJReport);
    }
    [self postDataJsonInfoWithUrl:url params:param start:startBlock failure:failBlock success:success];
}

// 龙头密训、大师投研下面研报、课程的详情
+ (void)requestZFAndDJDetailWithRequestType:(MemberCenterJCRequestType)requestType
                                  contentId:(NSInteger)ContentId
                                 serverType:(NSString *)serverType
                                      start:(void (^)())startBlock
                                    failure:(void (^)())failBlock
                                    success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:[NSNumber numberWithInteger:ContentId] forKey:@"id"];
    NSString *url;
    if (requestType == MemberCenterJCRequestTypeJCQLCourse) {
        url = KDakaBaseUrl(kAPI_QLCourseDetail);
    } else if (requestType == MemberCenterJCRequestTypeJCDJCourse) {
        url = KDakaBaseUrl(kAPI_DJCourseDetail);
    } else if (requestType == MemberCenterJCRequestTypeJCQLReport) {
        url = KDakaBaseUrl(kAPI_QLReportDetail);
    } else if (requestType == MemberCenterJCRequestTypeJCDJReport || requestType == MemberCenterJCRequestTypeJCDJReport2) {
        url = KDakaBaseUrl(kAPI_DJReportDetail);
    } else if (requestType == MemberCenterJCRequestTypeGSYJCourse) {
        url = KDakaBaseUrl(kAPI_GSYJ_CourseDetail);
    }
    [self getDataInfoWithUrl:url
                      header:(serverType.length ? @{@"serverType": serverType} : nil)
                      params:param
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 决策战绩榜
+ (void)requestMemberCenterJCRecordWithType:(NSInteger)type
                                      start:(void (^)())startBlock
                                    failure:(void (^)())failBlock
                                    success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:@(type) forKey:@"type"];
    [self getDataInfoWithUrl:KDakaBaseUrl(kAPI_MemberCenter_VIPJC_ZJB)
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 决策擒龙
+ (void)requestMemberCenterJCQLWithDay:(NSString *)day
                                  type:(NSInteger)type
                                 start:(void (^)())startBlock
                               failure:(void (^)())failBlock
                               success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:day forKey:@"day"];
    [params setObject:@(type) forKey:@"type"];
    [self getDataInfoWithUrl:KDakaBaseUrl(kAPI_MemberCenter_VIPJC_JCQL)
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 价值龙头
+ (void)requestMemberCenterJZLTWithDay:(NSString *)day
                                 start:(void (^)())startBlock
                               failure:(void (^)())failBlock
                               success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:day forKey:@"day"];
    [self getDataInfoWithUrl:KDakaBaseUrl(kAPI_MemberCenter_VIPJC_JZLT)
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 龙虎抓妖
+ (void)requestMemberCenterLHZYWithDay:(NSString *)day
                          capitalModel:(NSString *)capitalModel
                                 start:(void (^)())startBlock
                               failure:(void (^)())failBlock
                               success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (day.length) {
        [params setObject:day forKey:@"day"];
    }
    if (capitalModel.length) {
        [params setObject:capitalModel forKey:@"capitalModel"];
    }
    [self getDataInfoWithUrl:KDakaBaseUrl(kAPI_MemberCenter_VIPJC_LHZY)
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 龙虎抓妖资金模型角标
+ (void)requestMemberCenterLHZYBadgesWithDay:(NSString *)day
                                       start:(void (^)())startBlock
                                     failure:(void (^)())failBlock
                                     success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (day.length) {
        [params setObject:day forKey:@"day"];
    }
    [self getDataInfoWithUrl:KDakaBaseUrl(kAPI_MemberCenter_VIPJC_LHZY_Badges)
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 龙虎抓妖席位数据
+ (void)requestMemberCenterLHZYSeatsWithJoinPoolID:(NSString *)joinPoolID
                                             start:(void (^)())startBlock
                                           failure:(void (^)())failBlock
                                           success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (joinPoolID.length) {
        [params setObject:joinPoolID forKey:@"id"];
    }

    [self getDataInfoWithUrl:KDakaBaseUrl(kAPI_MemberCenter_VIPJC_LHZY_Seat)
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}


#pragma mark - 股市赢家新增
// 签约大咖
+ (void)requestMemberCenterVIPSignDakaWithType:(NSString *)type
                                    serverType:(NSString *)serverType
                                         start:(void (^)(void))startBlock
                                       failure:(void (^)(void))failBlock
                                       success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:KDakaBaseUrl(kAPI_MemberCenter_VipSignDaka)
                      header:@{@"serverType" : serverType}
                      params:@{@"type" : type}
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 股市赢家今日操作
+ (void)requestGSYJTodayOperationWithServerType:(JCVIPType)serverType
                                          start:(void (^)(void))startBlock
                                        failure:(void (^)(void))failBlock
                                        success:(requestSuccessBlock)success {
    NSString *serverTypeString = [NSString stringWithFormat:@"%zd", serverType];
    NSString *apiUrl = (serverType == JCVIPTypeGSYJDZ) ? kAPI_GSYJDZ_TodayOperation : kAPI_GSYJ_TodayOperation;
    [self postDataJsonInfoWithUrl:[kContentPrefix stringByAppendingString:apiUrl]
                           header:@{@"serverType" : serverTypeString}
                           params:nil
                            start:startBlock
                          failure:failBlock
                          success:success];
}


// 止盈个股
+ (void)requestGSYJProfitEndStocksWithServerType:(JCVIPType)serverType
                                           start:(void (^)(void))startBlock
                                    failure:(void (^)(void))failBlock
                                    success:(requestSuccessBlock)success {
    NSString *serverTypeString = [NSString stringWithFormat:@"%zd", serverType];
    NSString *apiUrl = (serverType == JCVIPTypeGSYJDZ) ? kAPI_GSYJDZ_ProfitEndStocks : kAPI_GSYJ_ProfitEndStocks;
    [self getDataInfoWithUrl:[kContentPrefix stringByAppendingString:apiUrl]
                      header:@{@"serverType" : serverTypeString}
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 股市赢家股票操作
+ (void)requestGSYJStockOperationWithServerType:(JCVIPType)serverType
                                       strageId:(NSString *)strageId
                                          start:(void (^)(void))startBlock
                                        failure:(void (^)(void))failBlock
                                        success:(requestSuccessBlock)success {
    NSString *serverTypeString = [NSString stringWithFormat:@"%zd", serverType];
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:strageId forKey:@"id"];
    NSString *apiUrl = (serverType == JCVIPTypeGSYJDZ) ? kAPI_GSYJDZ_StockOperation : kAPI_GSYJ_StockOperation;
    [self postDataJsonInfoWithUrl:[kContentPrefix stringByAppendingString:apiUrl]
                           header:@{@"serverType" : serverTypeString}
                           params:params
                            start:startBlock
                          failure:failBlock
                          success:success];
}

// 股市赢家历史持仓（支持分页）
+ (void)requestGSYJHisHoldsWithPage:(NSUInteger)page
                           pageSize:(NSUInteger)pageSize
                         serverType:(JCVIPType)serverType
                              start:(void (^)(void))startBlock
                            failure:(void (^)(void))failBlock
                            success:(requestSuccessBlock)success {
    NSString *serverTypeString = [NSString stringWithFormat:@"%zd", serverType];
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:@(page) forKey:@"pageNo"];
    [params setObject:@(pageSize) forKey:@"pageSize"];
    
    NSString *apiUrl = (serverType == JCVIPTypeGSYJDZ) ? kAPI_GSYJDZ_HisHolds : kAPI_GSYJ_HisHolds;
    [self postDataJsonInfoWithUrl:[kContentPrefix stringByAppendingString:apiUrl]
                           header:@{@"serverType" : serverTypeString}
                           params:params
                            start:startBlock
                          failure:failBlock
                          success:success];
}

// 股市赢家当前持仓
+ (void)requestGSYJCurHoldsWithServerType:(JCVIPType)serverType
                                    start:(void (^)(void))startBlock
                                  failure:(void (^)(void))failBlock
                                  success:(requestSuccessBlock)success {
    NSString *serverTypeString = [NSString stringWithFormat:@"%zd", serverType];
    NSString *apiUrl = (serverType == JCVIPTypeGSYJDZ) ? kAPI_GSYJDZ_CurHolds : kAPI_GSYJ_CurHolds;
    [self postDataJsonInfoWithUrl:[kContentPrefix stringByAppendingString:apiUrl]
                           header:@{@"serverType" : serverTypeString}
                           params:nil
                            start:startBlock
                          failure:failBlock
                          success:success];
}

// 股市赢家策略池
+ (void)requestGSYJStrategyPoolStockWithDay:(NSString *)day
                                 serverType:(JCVIPType)serverType
                                      start:(void (^)())startBlock
                                    failure:(void (^)())failBlock
                                    success:(requestSuccessBlock)success {
    NSString *serverTypeString = [NSString stringWithFormat:@"%zd", serverType];
    [self postDataJsonInfoWithUrl:KDakaBaseUrl(kAPI_GSYJ_StrategyPoolStock)
                           header:@{@"serverType": serverTypeString}
                           params:@{@"day": day}
                            start:startBlock
                          failure:failBlock
                          success:success];
}

// 股市赢家定制版小班池
+ (void)requestGSYJDZSmallPoolStockWithDay:(NSString *)day
                                serverType:(JCVIPType)serverType
                                     start:(void (^)())startBlock
                                   failure:(void (^)())failBlock
                                   success:(requestSuccessBlock)success {
    NSString *serverTypeString = [NSString stringWithFormat:@"%zd", serverType];
    [self getDataInfoWithUrl:KDakaBaseUrl(kAPI_GSYJDZ_SmallClassStock)
                           header:@{@"serverType": serverTypeString}
                           params:@{@"day": day}
                            start:startBlock
                          failure:failBlock
                          success:success];
}

// 首席课堂列表
+ (void)requestGSYJPrivilegeCourseWithPageSize:(NSInteger)pageSize
                                        pageNo:(NSInteger)pageNo
                                          type:(NSInteger)type
                                    serverType:(JCVIPType)serverType
                                         start:(void (^)(void))startBlock
                                       failure:(void (^)(void))failBlock
                                       success:(requestSuccessBlock)success {
    NSString *serverTypeString = [NSString stringWithFormat:@"%zd", serverType];
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:@(pageSize) forKey:@"pageSize"];
    [params setObject:@(pageNo) forKey:@"pageNo"];
    [params setObject:@(type) forKey:@"type"];

    [self postDataJsonInfoWithUrl:KDakaBaseUrl(kAPI_GSYJ_PrivilegeCourse)
                           header:@{@"serverType": serverTypeString}
                           params:params
                            start:startBlock
                          failure:failBlock
                          success:success];
}

// 策略报告/投研报告
+ (void)requestGSYJPrivilegeReportsWithPageSize:(NSInteger)pageSize
                                         pageNo:(NSInteger)pageNo
                                           type:(NSInteger)type
                                     serverType:(JCVIPType)serverType
                                          start:(void (^)(void))startBlock
                                        failure:(void (^)(void))failBlock
                                        success:(requestSuccessBlock)success {
    NSString *serverTypeString = [NSString stringWithFormat:@"%zd", serverType];
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:@(pageSize) forKey:@"pageSize"];
    [params setObject:@(pageNo) forKey:@"pageNo"];
    [params setObject:@(type) forKey:@"type"];

    [self postDataJsonInfoWithUrl:KDakaBaseUrl(kAPI_GSYJ_PrivilegeReports)
                           header:@{@"serverType": serverTypeString}
                           params:params
                            start:startBlock
                          failure:failBlock
                          success:success];
}


@end
