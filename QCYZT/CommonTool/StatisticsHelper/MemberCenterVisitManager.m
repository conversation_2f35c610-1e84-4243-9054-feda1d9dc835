//
//  MemberCenterVisitManager.m
//  QCYZT
//
//  Created by Augment on 2025-01-14
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import "MemberCenterVisitManager.h"
#import "HttpRequestTool+Statistics.h"

@interface MemberCenterVisitManager ()

@property (nonatomic, strong) NSMutableArray<NSNumber *> *pageStack; // 页面栈，记录功能ID

@end

@implementation MemberCenterVisitManager

+ (instancetype)sharedManager {
    static MemberCenterVisitManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _pageStack = [NSMutableArray array];
    }
    return self;
}

- (void)trackPageEnter:(UIViewController *)viewController functionId:(MemberCenterFunctionType)functionId {
    if (!viewController || functionId == MemberCenterFunctionTypeNone) {
        return;
    }

    NSString *className = NSStringFromClass([viewController class]);
    NSNumber *functionIdNumber = @(functionId);

    // 判断是否需要上报：如果pageStack中已经有这个functionId，则不上报
    BOOL shouldReport = ![self.pageStack containsObject:functionIdNumber];

    if (shouldReport) {
        [self reportVisit:functionId];
        FMLog(@"会员中心访问埋点 - 进入页面: %@, 功能ID: %ld", className, (long)functionId);
    } else {
        FMLog(@"会员中心访问埋点 - 进入页面: %@, 功能ID: %ld (已存在，不上报)", className, (long)functionId);
    }

    // 记录功能ID到栈中
    [self.pageStack addObject:functionIdNumber];
}

- (void)trackPageExit:(UIViewController *)viewController functionId:(MemberCenterFunctionType)functionId {
    if (!viewController || functionId == MemberCenterFunctionTypeNone) {
        return;
    }

    NSString *className = NSStringFromClass([viewController class]);
    NSNumber *functionIdNumber = @(functionId);

    // 从页面栈中移除一个对应的functionId（只移除第一个匹配的）
    NSUInteger index = [self.pageStack indexOfObject:functionIdNumber];
    if (index != NSNotFound) {
        [self.pageStack removeObjectAtIndex:index];
    }

    // 判断是否需要上报：如果pageStack中还有这个functionId，则不上报
    BOOL shouldReport = ![self.pageStack containsObject:functionIdNumber];

    if (shouldReport) {
        [self reportVisit:functionId];
        FMLog(@"会员中心访问埋点 - 退出页面: %@, 功能ID: %ld", className, (long)functionId);
    } else {
        FMLog(@"会员中心访问埋点 - 退出页面: %@, 功能ID: %ld (栈中还有，不上报)", className, (long)functionId);
    }
}

- (void)reportVisit:(MemberCenterFunctionType)functionId {
    [HttpRequestTool requestMemberCenterVisitWithFunctionId:functionId
                                                     start:nil
                                                   failure:^{
        FMLog(@"会员中心访问埋点上报失败，功能ID: %ld", (long)functionId);
    }
                                                   success:^(NSDictionary *responseObject) {
        if (lz_HttpStatusCheck(responseObject)) {
            FMLog(@"会员中心访问埋点上报成功，功能ID: %ld", (long)functionId);
        } else {
            FMLog(@"会员中心访问埋点上报失败，服务器响应: %@", responseObject);
        }
    }];
}

@end
