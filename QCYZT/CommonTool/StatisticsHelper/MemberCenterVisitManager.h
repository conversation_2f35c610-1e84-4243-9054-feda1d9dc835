//
//  MemberCenterVisitManager.h
//  QCYZT
//
//  Created by Augment on 2025-01-14
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 会员中心访问功能ID枚举
 */
typedef NS_ENUM(NSInteger, MemberCenterFunctionType) {
    MemberCenterFunctionTypeNone = 0,
    MemberCenterFunctionTypeGSYJ = 8,                    // 股市赢家
    MemberCenterFunctionTypeChiefClassroom = 9,          // 首席课堂列表、详情
    MemberCenterFunctionTypeStrategyReport = 10,         // 策略报告列表、详情
    MemberCenterFunctionTypeInvestmentReport = 11,       // 投研报告列表、详情
    MemberCenterFunctionTypeWealthDaily = 12,            // 财富日刊列表、详情
    MemberCenterFunctionTypeGSYJCustom = 13,             // 股市赢家定制版
    MemberCenterFunctionTypeSmallClassMarket = 14,       // 小班看市列表、详情
    MemberCenterFunctionTypeMacroEconomy = 15,           // 宏观经济列表、详情
    MemberCenterFunctionTypeDecisionDragon = 18,         // 决策擒龙
    MemberCenterFunctionTypeDragonAnalysis = 19,         // 擒龙解盘列表、详情
    MemberCenterFunctionTypeDragonTraining = 20,         // 擒龙训练列表、详情
    MemberCenterFunctionTypeDecisionGold = 21,           // 决策点金
    MemberCenterFunctionTypeMasterFollow = 22,           // 大师跟盘列表、详情
    MemberCenterFunctionTypeMasterStrategy = 23,         // 大师策略列表、详情
    MemberCenterFunctionTypeMasterResearch = 24,         // 大师精研列表、详情
    MemberCenterFunctionTypeDragonTactics = 25,          // 擒龙战法列表、详情
    MemberCenterFunctionTypeCatchDemonTactics = 26,      // 捉妖战法列表、详情
};


/**
 * 会员中心访问统计管理器
 * 用于处理会员中心页面访问埋点上报
 */
@interface MemberCenterVisitManager : NSObject

/**
 * 获取单例实例
 */
+ (instancetype)sharedManager;

/**
 * 页面进入埋点
 *
 * @param viewController 控制器实例
 * @param functionId 功能ID
 */
- (void)trackPageEnter:(UIViewController *)viewController functionId:(MemberCenterFunctionType)functionId;

/**
 * 页面退出埋点
 *
 * @param viewController 控制器实例
 * @param functionId 功能ID
 */
- (void)trackPageExit:(UIViewController *)viewController functionId:(MemberCenterFunctionType)functionId;

/**
 * 直接上报访问记录
 *
 * @param functionId 功能ID
 */
- (void)reportVisit:(MemberCenterFunctionType)functionId;

@end

NS_ASSUME_NONNULL_END
